@extends('admin.admin_dashboard')
@section('admin')

@php
    $type = request()->query('type', 'immobilisation');
    $isImmobilisation = $type === 'immobilisation';
    $isConsommable = $type === 'consommable';
    
    $title = $isConsommable 
        ? "Enregistrer une Nouvelle Entrée de Consommable" 
        : "Enregistrer une Nouvelle Entrée d'Immobilisation";
@endphp

<style>
    .card {
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.05);
        border: none;
        margin-bottom: 24px;
    }
    .card-header {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 15px 20px;
        border-bottom: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .card-body {
        padding: 25px;
        border-radius: 0 0 15px 15px;
    }
    .form-label {
        font-weight: 600;
        color: #5a5c69;
        margin-bottom: 8px;
    }
    .form-control, .form-select {
        border-radius: 10px;
        padding: 10px 15px;
        border: 1px solid #e3e6f0;
        transition: all 0.3s;
    }
    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
    .input-group-text {
        border-radius: 10px 0 0 10px;
        background-color: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-right: none;
    }
    .form-control.with-icon {
        border-radius: 0 10px 10px 0;
    }
    .btn-action {
        padding: 10px 20px;
        border-radius: 50px;
        font-weight: 500;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }
    .btn-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        border: none;
    }
    .btn-secondary {
        background: linear-gradient(135deg, #858796 0%, #60616f 100%);
        border: none;
    }
    .form-text {
        color: #858796;
        font-size: 0.875rem;
        margin-top: 5px;
    }
    .required-field::after {
        content: '*';
        color: #e74a3b;
        margin-left: 4px;
    }
    .form-section {
        background-color: #f8f9fc;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #4e73df;
    }
    .form-section-title {
        color: #4e73df;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .select2-container--bootstrap-5 .select2-selection {
        border-radius: 10px;
        padding: 5px 10px;
        border: 1px solid #e3e6f0;
        min-height: 42px;
    }
    .select2-container--bootstrap-5.select2-container--focus .select2-selection {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
    .select2-container--bootstrap-5 .select2-dropdown {
        border-radius: 10px;
        border: 1px solid #e3e6f0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }
    .select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted[aria-selected] {
        background-color: #4e73df;
    }
    .select2-container--bootstrap-5 .select2-dropdown .select2-results__option[aria-selected=true] {
        background-color: #eaecf4;
    }
    .info-card {
        background-color: rgba(78, 115, 223, 0.05);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        border-left: 4px solid #4e73df;
    }
    .info-card-title {
        color: #4e73df;
        font-weight: 600;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .info-card-text {
        color: #5a5c69;
        margin-bottom: 0;
    }
</style>

<div class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a></li>
                    <li class="breadcrumb-item">Enregistrements</li>
                    <li class="breadcrumb-item"><a href="{{ route('entrees.index') }}">Entrées</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Nouvelle Entrée</li>
                </ol>
            </nav>
        </div>
    </div>
    <!--end breadcrumb-->

    <div class="card animate__animated animate__fadeIn">
        <div class="card-header">
            <div>
                <i class="bx bx-log-in-circle me-1"></i>
                <span class="fw-bold">{{ $title }}</span>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('entrees.create', ['type' => 'immobilisation']) }}" 
                   class="btn {{ $isImmobilisation ? 'btn-success' : 'btn-outline-success' }} btn-sm">
                    <i class="bx bx-building-house"></i> Entrée d'Immobilisation
                </a>
                <a href="{{ route('entrees.create', ['type' => 'consommable']) }}" 
                   class="btn {{ $isConsommable ? 'btn-info' : 'btn-outline-info' }} btn-sm">
                    <i class="bx bx-package"></i> Entrée de Consommable
                </a>
                <a href="{{ route('entrees.index') }}" class="btn btn-secondary btn-sm">
                    <i class="bx bx-arrow-back"></i> Retour à la liste
                </a>
            </div>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('entrees.store') }}" enctype="multipart/form-data" id="entry-form">
                <!-- Conteneur pour les alertes -->
                <div id="alerts-container" class="mb-3"></div>
                @csrf
                <input type="hidden" name="type_entree" value="{{ $type }}">
                
                @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif
                
                <div class="container-fluid">
    <!-- Container pour les alertes -->
    <div id="alert-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
    
    <div class="row">
        <div class="col-12">
                        <!-- Navigation par onglets -->
                        <ul class="nav nav-tabs mb-3" id="entryTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="document-tab" data-bs-toggle="tab" data-bs-target="#document" type="button" role="tab" aria-controls="document" aria-selected="true">
                                    <i class="bx bx-file me-1"></i> Document
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="product-tab" data-bs-toggle="tab" data-bs-target="#product" type="button" role="tab" aria-controls="product" aria-selected="false">
                                    <i class="bx bx-package me-1"></i> Produit
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab" aria-controls="summary" aria-selected="false">
                                    <i class="bx bx-list-check me-1"></i> Récapitulatif
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="entryTabsContent">
                            <!-- Onglet Document (Informations Communes) -->
                            <div class="tab-pane fade show active" id="document" role="tabpanel" aria-labelledby="document-tab">
                                <div class="form-section">
                                    <h5 class="form-section-title">
                                        <i class="bx bx-info-circle"></i> Informations du Document
                                    </h5>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label for="date_oem" class="form-label required-field">Date de l'OEM</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bx bx-calendar"></i></span>
                                                <input type="date" class="form-control with-icon" id="date_oem" name="date_oem" value="{{ date('Y-m-d') }}" required>
                                            </div>
                                            <div class="form-text">Date de l'Ordre d'Entrée de Matière</div>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="reference" class="form-label required-field">Référence Facture</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bx bx-hash"></i></span>
                                                <input type="text" class="form-control with-icon" id="reference" name="reference" required>
                                            </div>
                                            <div class="form-text">Numéro de référence de la facture ou du document justificatif</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="piece_justificative" class="form-label">Pièce Justificative</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="bx bx-file"></i></span>
                                                <input type="file" class="form-control with-icon" id="piece_justificative" name="piece_justificative" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                            </div>
                                            <div class="form-text">Joindre un document justificatif (PDF, image ou document Word)</div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="observations" class="form-label">Observations Générales</label>
                                        <textarea class="form-control" id="observations" name="observations" rows="2"></textarea>
                                        <div class="form-text">Informations complémentaires sur cette entrée groupée</div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-end mt-3">
                                        <button type="button" class="btn btn-primary" onclick="document.getElementById('product-tab').click();">
                                            <i class="bx bx-right-arrow-alt"></i> Continuer vers Produit
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Onglet Produit -->
                            <div class="tab-pane fade" id="product" role="tabpanel" aria-labelledby="product-tab">
                                <div class="form-section">
                                    <h5 class="form-section-title">
                                        <i class="bx bx-package"></i> Informations du Produit
                                    </h5>
                                    <div id="product-form">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="designation_matiere" class="form-label required-field">Désignation Matière</label>
                                                <select class="form-select select2-with-search" id="designation_matiere" name="designation_matiere" required>
                                                    <option value="">Sélectionner une désignation</option>
                                                    @foreach($comptesMatiere as $code => $intitule)
                                                    <option value="{{ $intitule }}" data-code="{{ $code }}">{{ $intitule }}</option>
                                                    @endforeach
                                                </select>
                                                <div class="form-text">Sélectionnez la désignation de matière</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="compte_matiere_display" class="form-label">Compte Matière</label>
                                                <input type="text" class="form-control" id="compte_matiere_display" readonly>
                                                <input type="hidden" id="compte_matiere" name="compte_matiere" required>
                                                <div class="form-text">Le compte matière s'affiche automatiquement selon la désignation</div>
                                                <!-- Zone de débogage -->
                                                <div id="debug-info" class="mt-2 p-2 bg-light border" style="font-size: 12px; max-height: 100px; overflow-y: auto; display: none;">
                                                    <strong>Débogage:</strong> <span id="debug-content">Aucune sélection</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="nature" class="form-label required-field">Nature</label>
                                                <select class="form-select select2" id="nature" name="nature" required>
                                                    <option value="">Sélectionner une nature</option>
                                                    <option value="Mat Info">Mat Info</option>
                                                    <option value="Mat Bur">Mat Bur</option>
                                                    <option value="Mob Bur">Mob Bur</option>
                                                    <option value="Mat Roul">Mat Roul</option>
                                                    <option value="Terrains Battis">Terrains Battis</option>
                                                    <option value="Terrains non Battis">Terrains non Battis</option>
                                                </select>
                                                <div class="form-text">Sélectionnez la nature de la matière</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="format" class="form-label required-field">Format</label>
                                                <select class="form-select select2" id="format" name="format" required>
                                                    <option value="">Sélectionner un format</option>
                                                    <option value="Grand">Grand</option>
                                                    <option value="Moyen">Moyen</option>
                                                    <option value="Petit">Petit</option>
                                                    <option value="Standard">Standard</option>
                                                </select>
                                                <div class="form-text">Sélectionnez le format de la matière</div>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="etat" class="form-label required-field">État</label>
                                                <select class="form-select select2" id="etat" name="etat" required>
                                                    <option value="">Sélectionner un état</option>
                                                    <option value="Bon">Bon</option>
                                                    <option value="Passable">Passable</option>
                                                    <option value="En Panne">En Panne</option>
                                                    <option value="Hors Service">Hors Service</option>
                                                </select>
                                                <div class="form-text">Sélectionnez l'état de la matière</div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="quantite" class="form-label required-field">Quantité</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bx bx-package"></i></span>
                                                    <input type="number" class="form-control with-icon" id="quantite" name="quantite" min="1" required>
                                                    <span class="input-group-text" id="unite-display">Unité</span>
                                                </div>
                                                <div class="form-text">Quantité entrée en stock</div>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="prix_unitaire" class="form-label required-field">Prix Unitaire</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bx bx-money"></i></span>
                                                    <input type="number" class="form-control with-icon" id="prix_unitaire" name="prix_unitaire" min="0" required>
                                                    <span class="input-group-text">FCFA</span>
                                                </div>
                                                <div class="form-text">Prix unitaire de l'article</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between mt-4">
                                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('document-tab').click();">
                                            <i class="bx bx-left-arrow-alt"></i> Retour au Document
                                        </button>
                                        <button type="button" id="add-product" class="btn btn-success btn-action">
                                            <i class="bx bx-plus"></i> Ajouter ce produit
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Onglet Récapitulatif -->
                            <div class="tab-pane fade" id="summary" role="tabpanel" aria-labelledby="summary-tab">
                                <div class="form-section">
                                    <h5 class="form-section-title">
                                        <i class="bx bx-list-check"></i> Récapitulatif du Document
                                    </h5>
                                    <div class="summary-info p-3 bg-light rounded mb-3">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Date OEM:</strong> <span id="summary-date-oem">-</span></p>
                                                <p><strong>Référence:</strong> <span id="summary-reference">-</span></p>
                                                <p><strong>Observations:</strong> <span id="summary-observations">-</span></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Pièce jointe:</strong> <span id="summary-piece">-</span></p>
                                                <p><strong>Total produits:</strong> <span id="summary-total-products">0</span></p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Liste des produits ajoutés -->
                                    <div id="products-container" class="mt-4">
                                        <h5 class="form-section-title">
                                            <i class="bx bx-package"></i> Liste des Produits
                                            <span class="badge bg-primary ms-2" id="products-count">0</span>
                                        </h5>
                                        <div class="table-responsive">
                                            <table id="products-table" class="table table-bordered table-striped table-hover">
                                                <thead class="table-primary">
                                                    <tr>
                                                        <th width="30">#</th>
                                                        <th>Désignation</th>
                                                        <th>Compte</th>
                                                        <th>Nature</th>
                                                        <th>Format</th>
                                                        <th>État</th>
                                                        <th>Qté</th>
                                                        <th>Prix Unit.</th>
                                                        <th>Total</th>
                                                        <th width="80">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td colspan="10" class="text-center">Aucun produit ajouté</td>
                                                    </tr>
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <th colspan="9" class="text-end">Montant Total:</th>
                                                        <th colspan="2" id="total-amount">0 FCFA</th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <!-- Boutons d'action -->
                                    <div class="d-flex justify-content-between mt-4" id="summary-actions">
                                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('product-tab').click();">
                                            <i class="bx bx-left-arrow-alt"></i> Retour aux Produits
                                        </button>
                                        <div>
                                            <button type="button" id="validate-all" class="btn btn-primary btn-action d-none">
                                                <i class="bx bx-check-double"></i> Valider tous les produits
                                            </button>
                                            <button type="button" id="final-submit" class="btn btn-success btn-action d-none">
                                                <i class="bx bx-save"></i> Confirmer l'enregistrement
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Fin du récapitulatif -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                        
                <div class="info-card mt-3">
                    <h6 class="info-card-title">
                        <i class="bx bx-info-circle"></i> Informations
                    </h6>
                    <p class="info-card-text">
                        L'enregistrement d'une entrée augmente automatiquement le stock disponible de l'article concerné.
                        Assurez-vous que les informations saisies sont correctes avant de valider.
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

<style>
    /* Style pour Select2 avec recherche visible */
    .select2-container--bootstrap-5 .select2-dropdown {
        border-color: #86b7fe;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
    }
    
    .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        outline: 0;
    }
    
    .select2-highlighted {
        background-color: #fff3cd;
        font-weight: bold;
    }
    
    /* Augmenter la taille du champ de recherche dans Select2 */
    .select2-search__field {
        width: 100% !important;
        font-size: 1rem !important;
        padding: 0.375rem 0.75rem !important;
    }
    
    /* Style pour les options dans le dropdown */
    .select2-results__option {
        padding: 0.5rem 0.75rem;
        font-size: 0.95rem;
    }
    
    .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
        background-color: #f8f9fa;
        color: #212529;
    }
</style>

<script>
    $(document).ready(function() {
        // Initialiser Select2 standard
        $('.select2').select2({
            theme: 'bootstrap-5',
            placeholder: 'Sélectionner un élément',
            allowClear: true
        });
        
        // Configuration de Select2 avec recherche visible pour la désignation matière
        $('.select2-with-search').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: function() {
                return $(this).data('placeholder');
            },
            allowClear: true,
            language: {
                inputTooShort: function() {
                    return "Saisissez au moins un caractère...";
                },
                noResults: function() {
                    return "Aucun résultat trouvé";
                },
                searching: function() {
                    return "Recherche en cours...";
                }
            },
            minimumInputLength: 1,
            templateResult: function(data) {
                if (data.loading) return data.text;
                
                if ($(data.element).val() === "") return data.text;
                
                var term = $('.select2-search__field').val();
                var $result = $('<span></span>');
                
                if (term && term.length > 0) {
                    var searchTerm = term.toLowerCase();
                    var text = data.text;
                    var position = text.toLowerCase().indexOf(searchTerm);
                    
                    if (position > -1) {
                        var start = text.substring(0, position);
                        var match = text.substring(position, position + searchTerm.length);
                        var end = text.substring(position + searchTerm.length);
                        
                        $result.append(start);
                        $result.append($('<span class="select2-highlighted"></span>').text(match));
                        $result.append(end);
                        return $result;
                    }
                }
                
                return data.text;
            },
        $(document).ready(function() {
            // Créer un tableau direct pour le mapping code => intitulé
            var mappingCodeToIntitule = {
                @foreach($comptesMatiere as $code => $intitule)
                    "{{ $code }}": "{{ $intitule }}",
                @endforeach
            };
            
            // Créer un tableau direct pour le mapping intitulé => code
            var mappingIntituleToCode = {
                @foreach($comptesMatiere as $code => $intitule)
                    "{{ $intitule }}": "{{ $code }}",
                @endforeach
            };
            
            // Fonction pour mettre à jour le compte matière
            function updateCompteMatiere() {
                var selectedIntitule = $('#designation_matiere').val();
                var code = mappingIntituleToCode[selectedIntitule];
                
                if (code) {
                    $('#compte_matiere').val(code);
                    $('#compte_matiere_display').val(code);
                    $('#recap-article').text(code + ' - ' + selectedIntitule);
                } else if (selectedIntitule) {
                    $('#compte_matiere').val('');
                    $('#compte_matiere_display').val('Compte non trouvé');
                    $('#recap-article').text('Désignation: ' + selectedIntitule);
                } else {
                    $('#compte_matiere').val('');
                    $('#compte_matiere_display').val('');
                    $('#recap-article').text('Aucun compte matière sélectionné');
                }
            }
            
            // Attacher l'événement change au select
            $('#designation_matiere').on('change', updateCompteMatiere);
            
            // Attacher également l'événement select2:select pour Select2
            $('#designation_matiere').on('select2:select', updateCompteMatiere);
            
            // Exécuter une fois au chargement de la page
            setTimeout(updateCompteMatiere, 500);
        });
        
        // Le champ designation_matiere est déjà un select, pas besoin de mise à jour avant soumission
        
        // Mettre à jour le récapitulatif pour le champ Nature
        $('#nature').on('change', function() {
            var selectedNature = $(this).val();
            $('#recap-nature').text(selectedNature || 'Non spécifiée');
        });
        
        // Mettre à jour le récapitulatif pour le champ Format
        $('#format').on('change', function() {
            var selectedFormat = $(this).val();
            $('#recap-format').text(selectedFormat || 'Non spécifié');
        });
        
        // Mettre à jour le récapitulatif pour le champ État
        $('#etat').on('change', function() {
            var selectedEtat = $(this).val();
            $('#recap-etat').text(selectedEtat || 'Non spécifié');
        });
        
        // Mettre à jour le récapitulatif lors de la saisie de la quantité
        $('#quantite').on('input', function() {
            var quantite = $(this).val() || 0;
            $('#recap-quantite').text(quantite);
            updateTotal();
        });
        
        // Mettre à jour le récapitulatif lors de la saisie du prix
        $('#prix_unitaire').on('input', function() {
            var prix = $(this).val() || 0;
            $('#recap-prix').text(prix + ' FCFA');
        });
        
        // Tableau pour stocker les produits ajoutés
        window.addedProducts = [];

        // Gestion de la sélection de désignation matière
        $('#designation_matiere').on('change', function() {
            const selectedOption = $(this).find('option:selected');
            const code = selectedOption.data('code');
            $('#compte_matiere_display').val(code);
            $('#compte_matiere').val(code);
            
            // Mise à jour du debug (caché par défaut)
            $('#debug-content').html(`Désignation sélectionnée: ${selectedOption.val()}, Code: ${code}`);
        });

        // Synchronisation des données entre les onglets
        function updateSummary() {
            const reference = $('#reference').val() || '-';
            const observations = $('#observations').val() || '-';
            const pieceFile = $('#piece_justificative').val() ? $('#piece_justificative').val().split('\\').pop() : '-';
            const dateOem = $('#date_oem').val() || '-';
            
            $('#summary-reference').text(reference);
            $('#summary-observations').text(observations);
            $('#summary-piece').text(pieceFile);
            $('#summary-total-products').text(window.addedProducts ? window.addedProducts.length : 0);
            $('#summary-date-oem').text(dateOem);

            // Mise à jour du récapitulatif des produits
            refreshProductsTable();
        }

        // Événements pour mettre à jour le récapitulatif
        $('#reference, #observations, #piece_justificative, #date_oem').on('change', updateSummary);
        
        // Événements pour les onglets
        $('#document-tab').on('shown.bs.tab', function() {
            updateSummary();
        });
        
        $('#product-tab').on('shown.bs.tab', function() {
            updateSummary();
        });
        
        $('#summary-tab').on('shown.bs.tab', function() {
            updateSummary();
        });

        // Ajouter un produit à la liste
        $('#add-product').on('click', function() {
            console.log('Bouton Ajouter ce produit cliqué');
            // Vérifier que tous les champs requis sont remplis
            const requiredFields = ['designation_matiere', 'nature', 'format', 'etat', 'quantite', 'prix_unitaire'];
            let isValid = true;
            
            requiredFields.forEach(field => {
                const input = $('#' + field);
                console.log('Vérification du champ:', field, input.length ? input.val() : 'champ non trouvé');
                if (!input.length) {
                    console.error(`Le champ ${field} n'existe pas dans le DOM`);
                    isValid = false;
                    return;
                }
                
                if (!input.val()) {
                    isValid = false;
                    input.addClass('is-invalid');
                } else {
                    input.removeClass('is-invalid');
                }
            });
            
            if (!isValid) {
                showAlert('Veuillez remplir tous les champs obligatoires du produit.', 'danger');
                return;
            }
            
            // Récupérer les valeurs du produit
            const quantite = parseFloat($('#quantite').val());
            const prix_unitaire = parseFloat($('#prix_unitaire').val());
            const montant_total = quantite * prix_unitaire;
            console.log('Valeurs produit:', {
                quantite,
                prix_unitaire,
                montant_total
            });
            
            const product = {
                id: Date.now(), // Identifiant unique pour le produit
                date_oem: $('#date_oem').val(),
                designation_matiere: $('#designation_matiere').val(),
                compte_matiere: $('#compte_matiere').val(),
                nature: $('#nature').val(),
                format: $('#format').val(),
                etat: $('#etat').val(),
                quantite: quantite,
                prix_unitaire: prix_unitaire,
                montant_total: montant_total
            };
            console.log('Produit à ajouter:', product);
            
            // Ajouter le produit au tableau
            if (!window.addedProducts) {
                window.addedProducts = [];
            }
            window.addedProducts.push(product);
            console.log('Produits après ajout:', window.addedProducts);
            
            // Rafraîchir le tableau des produits
            refreshProductsTable();
            
            // Réinitialiser le formulaire de produit
            resetProductForm();
            
            showAlert('Produit ajouté avec succès!', 'success');
            
            // Passer à l'onglet récapitulatif après avoir ajouté un produit
            setTimeout(() => {
                document.getElementById('summary-tab').click();
            }, 500);
        });

        // Fonction pour rafraîchir le tableau des produits
        function refreshProductsTable() {
            console.log('refreshProductsTable appelé', window.addedProducts);
            const tbody = $('#products-table tbody');
            tbody.empty();
            
            if (window.addedProducts && window.addedProducts.length > 0) {
                $('#products-container').removeClass('d-none');
                $('#validate-all').removeClass('d-none');
                
                let totalAmount = 0;
                
                window.addedProducts.forEach((product, index) => {
                    const productTotal = product.montant_total;
                    totalAmount += productTotal;
                    
                    const row = `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${product.date_oem}</td>
                            <td>${product.designation_matiere}</td>
                            <td>${product.compte_matiere}</td>
                            <td>${product.nature}</td>
                            <td>${product.format}</td>
                            <td>${product.etat}</td>
                            <td>${product.quantite}</td>
                            <td>${product.prix_unitaire} FCFA</td>
                            <td>${productTotal} FCFA</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary edit-product" data-id="${product.id}">
                                    <i class="bx bx-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-product" data-id="${product.id}">
                                    <i class="bx bx-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    
                    tbody.append(row);
                });
                
                $('#products-count').text(window.addedProducts.length);
                $('#total-amount').text(totalAmount + ' FCFA');
                
                // Ajouter le bouton "Continuer" si c'est le premier produit
                if (window.addedProducts.length === 1 && !$('#continue-adding').length) {
                    const continueBtn = $('<button>', {
                        id: 'continue-adding',
                        type: 'button',
                        class: 'btn btn-outline-primary ms-2',
                        html: '<i class="bx bx-plus"></i> Ajouter un autre produit',
                        click: function() {
                            document.getElementById('product-tab').click();
                        }
                    });
                    $('#summary-actions').append(continueBtn);
                }
            } else {
                // Même si aucun produit n'est ajouté, on garde le conteneur visible mais on affiche un message
                tbody.append('<tr><td colspan="11" class="text-center">Aucun produit ajouté</td></tr>');
                $('#validate-all').addClass('d-none');
                $('#final-submit').addClass('d-none');
                $('#continue-adding').remove();
            }
        }

        // Réinitialiser le formulaire de produit
        function resetProductForm() {
            // Reset ne fonctionne pas sur le formulaire partiel, donc on réinitialise manuellement
            $('#designation_matiere').val('').trigger('change');
            $('#nature').val('').trigger('change');
            $('#format').val('').trigger('change');
            $('#etat').val('').trigger('change');
            $('#compte_matiere_display').val('');
            $('#compte_matiere').val('');
            $('#quantite').val('');
            $('#prix_unitaire').val('');
            $('#debug-content').html('Aucune sélection');
            
            // Supprimer les états d'erreur
            $('.is-invalid').removeClass('is-invalid');
        }

        // Supprimer un produit
        $(document).on('click', '.delete-product', function() {
            const productId = $(this).data('id');
            window.addedProducts = window.addedProducts.filter(product => product.id !== productId);
            console.log('Après suppression:', window.addedProducts);
            refreshProductsTable();
            updateSummary();
            showAlert('Produit supprimé avec succès!', 'warning');
        });
        
        // Éditer un produit
        $(document).on('click', '.edit-product', function() {
            const productId = $(this).data('id');
            const product = window.addedProducts.find(p => p.id === productId);
            
            if (product) {
                console.log('Produit à éditer:', product);
                // Remplir le formulaire avec les données du produit
                // Note: date_oem reste inchangée car elle est commune à tous les produits
                $('#designation_matiere').val(product.designation_matiere).trigger('change');
                $('#nature').val(product.nature).trigger('change');
                $('#format').val(product.format).trigger('change');
                $('#etat').val(product.etat).trigger('change');
                $('#quantite').val(product.quantite);
                $('#prix_unitaire').val(product.prix_unitaire);
                
                // Supprimer le produit de la liste
                window.addedProducts = window.addedProducts.filter(p => p.id !== productId);
                refreshProductsTable();
                
                // Passer à l'onglet produit
                document.getElementById('product-tab').click();
                
                showAlert('Vous pouvez modifier le produit et l\'ajouter à nouveau.', 'info');
            }
        });

        // Valider tous les produits
        $('#validate-all').on('click', function() {
            if (!window.addedProducts || window.addedProducts.length === 0) {
                showAlert('Aucun produit n\'a été ajouté. Veuillez ajouter au moins un produit.', 'warning');
                return;
            }
            
            // Vérifier si les informations communes sont remplies
            if (!$('#reference').val()) {
                showAlert('Veuillez remplir au moins le champ Référence Facture dans Informations du Document.', 'danger');
                document.getElementById('document-tab').click(); // Rediriger vers l'onglet document
                return;
            }
            
            $(this).addClass('d-none');
            $('#final-submit').removeClass('d-none');
            $('#continue-adding').addClass('d-none');
            $('.nav-tabs .nav-link').prop('disabled', true).addClass('disabled');
            
            showAlert('Produits validés! Cliquez sur "Confirmer l\'enregistrement" pour finaliser.', 'info');
        });

        // Soumettre le formulaire final
        $('#final-submit').on('click', function() {
            if (addedProducts.length === 0) {
                showAlert('Aucun produit à enregistrer.', 'danger');
                return;
            }
            
            // Créer des champs cachés pour chaque produit
            window.addedProducts.forEach((product, index) => {
                createHiddenInput(`products[${index}][designation_matiere]`, product.designation_matiere);
                createHiddenInput(`products[${index}][compte_matiere]`, product.compte_matiere);
                createHiddenInput(`products[${index}][nature]`, product.nature);
                createHiddenInput(`products[${index}][format]`, product.format);
                createHiddenInput(`products[${index}][etat]`, product.etat);
                createHiddenInput(`products[${index}][quantite]`, product.quantite);
                createHiddenInput(`products[${index}][prix_unitaire]`, product.prix_unitaire);
                createHiddenInput(`products[${index}][date_oem]`, product.date_oem);
            });
            
            // Indiquer que c'est un enregistrement groupé
            const groupedField = document.createElement('input');
            groupedField.type = 'hidden';
            groupedField.name = 'is_grouped';
            groupedField.value = '1';
            document.getElementById('entry-form').appendChild(groupedField);
            
            // Soumettre le formulaire
            document.getElementById('entry-form').submit();
            
            showAlert('Enregistrement en cours...', 'info');
        });

        // Fonction pour afficher une alerte
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            document.getElementById('alerts-container').appendChild(alertDiv);
            
            // Faire disparaître l'alerte après 5 secondes
            setTimeout(() => {
                $(alertDiv).fadeOut('slow', function() {
                    $(this).remove();
                });
            }, 5000);
        }
        
        // Empêcher la soumission directe du formulaire
        $('#entry-form').on('submit', function(e) {
            if ($('#submit-form').is(':hidden')) {
                e.preventDefault();
                toastr.error('Veuillez d\'abord valider tous les produits');
                return false;
            }
        });
    });
</script>

<!-- Chargement du script de gestion des produits -->
<script src="{{ asset('js/entree-products.js') }}"></script>
@endsection

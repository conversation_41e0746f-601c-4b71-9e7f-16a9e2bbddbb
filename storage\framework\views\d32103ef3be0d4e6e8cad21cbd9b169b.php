<?php $__env->startSection('styles'); ?>
<!-- Dashboard Modern CSS -->
<link rel="stylesheet" href="<?php echo e(asset('css/dashboard-modern.css')); ?>">
<!-- Modern Receptions CSS -->
<link rel="stylesheet" href="<?php echo e(asset('css/modern-receptions.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('admin'); ?>
<div class="page-content dashboard-container">
    <!-- 1. En-tête et présentation générale -->
    <div class="welcome-banner animate-fade-in">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2>Bienvenue, <?php echo e(Auth::user()->name); ?> !</h2>
                <p>Voici un aperçu de l'état actuel du parc automobile. Utilisez ce tableau de bord pour surveiller les statistiques clés, les tendances et les activités récentes.</p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="filter-container justify-content-lg-end">
                    <select class="form-select form-select-sm filter-select" id="dateRangeFilter">
                        <option value="this-week">Cette semaine</option>
                        <option value="this-month" selected>Ce mois</option>
                        <option value="last-month">Mois dernier</option>
                        <option value="last-year">Année dernière</option>
                    </select>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light dropdown-toggle export-btn" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class='bx bx-export'></i> Exporter
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="javascript:exportData('pdf')"><i class='bx bxs-file-pdf me-2'></i>PDF</a></li>
                            <li><a class="dropdown-item" href="javascript:exportData('excel')"><i class='bx bxs-file-excel me-2'></i>Excel</a></li>
                            <li><a class="dropdown-item" href="javascript:exportData('csv')"><i class='bx bxs-file-csv me-2'></i>CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 2. Section des statistiques clés -->
    <div class="row row-cols-1 row-cols-md-2 row-cols-xl-4 g-4 mb-4">
        <!-- Carte statistique - Total des véhicules -->
        <div class="col animate-fade-in delay-1">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-vehicles">
                        <i class='bx bx-car'></i>
                    </div>
                    <h6 class="card-title">TOTAL DES VÉHICULES</h6>
                    <h3 class="stat-value"><?php echo e($totalVehicules); ?></h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold"><?php echo e($vehiculesOperationnels); ?> opérationnels</span>
                        <span class="text-muted">(<?php echo e($vehiculesEnMaintenance); ?> en maintenance)</span>
                        <span class="trend-indicator <?php echo e($tendanceVehicules >= 0 ? 'trend-up' : 'trend-down'); ?>"><?php echo e($tendanceVehicules >= 0 ? '+' : ''); ?><?php echo e($tendanceVehicules); ?>%</span>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Carte statistique - Réceptions récentes -->
        <div class="col animate-fade-in delay-2">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-receptions">
                        <i class='bx bx-archive-in'></i>
                    </div>
                    <h6 class="card-title">RÉCEPTIONS RÉCENTES</h6>
                    <h3 class="stat-value"><?php echo e($totalReceptions); ?></h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold"><?php echo e($receptionsTerminees); ?> terminées</span>
                        <span class="text-muted">(<?php echo e($receptionsEnAttente); ?> en attente)</span>
                        <span class="trend-indicator <?php echo e($tendanceReceptions >= 0 ? 'trend-up' : 'trend-down'); ?>"><?php echo e($tendanceReceptions >= 0 ? '+' : ''); ?><?php echo e($tendanceReceptions); ?>%</span>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Carte statistique - Directions actives -->
        <div class="col animate-fade-in delay-3">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-departments">
                        <i class='bx bxs-building'></i>
                    </div>
                    <h6 class="card-title">DIRECTIONS ACTIVES</h6>
                    <h3 class="stat-value"><?php echo e($totalDepartements); ?></h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold">Utilisation du parc</span>
                        <span class="text-muted">(<?php echo e($efficaciteParc); ?>% d'efficacité)</span>
                        <span class="trend-indicator <?php echo e($tendanceDepartements >= 0 ? 'trend-up' : 'trend-down'); ?>"><?php echo e($tendanceDepartements >= 0 ? '+' : ''); ?><?php echo e($tendanceDepartements); ?>%</span>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Carte statistique - Personnel -->
        <div class="col animate-fade-in delay-4">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-personnel">
                        <i class='bx bx-group'></i>
                    </div>
                    <h6 class="card-title">PERSONNEL DE LA DAF</h6>
                    <h3 class="stat-value"><?php echo e($totalEmployeesDAF); ?></h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold"><?php echo e($chauffeurs); ?> chauffeurs</span>
                        <span class="text-muted">(<?php echo e($utilisateurs); ?> utilisateurs)</span>
                        <span class="trend-indicator <?php echo e($tendancePersonnel >= 0 ? 'trend-up' : 'trend-down'); ?>"><?php echo e($tendancePersonnel >= 0 ? '+' : ''); ?><?php echo e($tendancePersonnel); ?>%</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!-- 3. Section des graphiques interactifs -->
    <div class="row mb-4">
        <!-- Graphique principal - Évolution des réceptions -->
        <div class="col-lg-8 animate-fade-in delay-1">
            <div class="chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Évolution des réceptions</h5>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: rgba(30, 136, 229, 1)"></div>
                            <span>Réceptions</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="receptionChart" data-chart-data="<?php echo e(json_encode($receptionsParMois)); ?>"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Graphique circulaire - Répartition des véhicules -->
        <div class="col-lg-4 animate-fade-in delay-2">
            <div class="chart-card">
                <div class="card-header">
                    <h5>Répartition des Véhicules par rapport à leur Etat</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="vehicleTypeChart" data-chart-data="<?php echo e(json_encode($vehiculesParEtatComplet)); ?>"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Graphique à barres - Utilisation par département -->
        <div class="col-lg-8 animate-fade-in delay-3">
            <div class="chart-card">
                <div class="card-header">
                    <h5>Affectation des Véhicules par Direction</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="departmentChart" data-chart-data="<?php echo e(json_encode($vehiculesParDepartement)); ?>"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Widget Événements à venir - Côté droit du graphique -->
        <div class="col-lg-4 animate-fade-in delay-3">
            <div class="side-widget">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Événements à venir</h5>
                    <button class="btn btn-sm btn-outline-primary"><i class='bx bx-plus'></i></button>
                </div>
                <div class="card-body">
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">02</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Entretien Toyota Hilux</h6>
                            <p>Garage Central, 9h00</p>
                        </div>
                    </div>
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">05</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Contrôle technique Ford Ranger</h6>
                            <p>Centre Auto, 14h30</p>
                        </div>
                    </div>
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">10</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Réception nouveau véhicule</h6>
                            <p>Concessionnaire, 10h00</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 4. Section des activités récentes -->
    <div class="row mb-4">
        <div class="col-lg-8 animate-fade-in delay-1">
            <div class="modern-activity-card">
                <div class="card-header-modern d-flex justify-content-between align-items-center">
                    <div class="header-title-section">
                        <h5 class="card-title-modern">
                            <i class='bx bx-receipt me-2'></i>
                            Réceptions récentes
                        </h5>
                        <p class="card-subtitle-modern">Dernières réceptions enregistrées dans le système</p>
                    </div>
                    <div class="header-actions">
                        <a href="<?php echo e(route('liste_reception')); ?>" class="btn-modern btn-primary-modern">
                            <i class='bx bx-list-ul me-1'></i>
                            Voir tout
                        </a>
                    </div>
                </div>
                <div class="card-body-modern">
                    <?php $__empty_1 = true; $__currentLoopData = $recentReceptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reception): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="reception-item-modern">
                        <div class="reception-icon-modern">
                            <div class="icon-wrapper-modern <?php echo e($reception->pv_reception ? 'completed' : 'pending'); ?>">
                                <i class='bx <?php echo e($reception->pv_reception ? "bx-check-circle" : "bx-time-five"); ?>'></i>
                            </div>
                        </div>
                        <div class="reception-content-modern">
                            <div class="reception-header-modern">
                                <h6 class="reception-reference-modern"><?php echo e($reception->reference_courier); ?></h6>
                                <div class="reception-status-modern">
                                    <?php if($reception->pv_reception): ?>
                                        <span class="status-badge-modern completed">
                                            <i class='bx bx-check-circle me-1'></i>
                                            Terminé
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge-modern pending">
                                            <i class='bx bx-time-five me-1'></i>
                                            En attente
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="reception-details-modern">
                                <p class="reception-object-modern"><?php echo e(Str::limit($reception->objet, 60)); ?></p>
                                <div class="reception-meta-modern">
                                    <span class="meta-item-modern">
                                        <i class='bx bx-calendar me-1'></i>
                                        <?php echo e(\Carbon\Carbon::parse($reception->date_enregistrement)->format('d/m/Y')); ?>

                                    </span>
                                    <span class="meta-item-modern">
                                        <i class='bx bx-building me-1'></i>
                                        <?php echo e($reception->departement ? Str::limit($reception->departement->nom_departement, 25) : 'N/A'); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="reception-actions-modern">
                            <a href="<?php echo e(route('reception.details', $reception->id)); ?>" class="action-btn-modern view" data-bs-toggle="tooltip" title="Voir détails">
                                <i class='bx bx-show'></i>
                            </a>
                            <a href="<?php echo e(route('editer_reception', $reception->id)); ?>" class="action-btn-modern edit" data-bs-toggle="tooltip" title="Modifier">
                                <i class='bx bx-edit'></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="empty-state-modern">
                        <div class="empty-icon-modern">
                            <i class='bx bx-inbox'></i>
                        </div>
                        <h6 class="empty-title-modern">Aucune réception récente</h6>
                        <p class="empty-text-modern">Les nouvelles réceptions apparaîtront ici</p>
                        <a href="<?php echo e(route('ajouter_reception')); ?>" class="btn-modern btn-outline-modern">
                            <i class='bx bx-plus me-1'></i>
                            Ajouter une réception
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Widget Tâches prioritaires - Côté droit des réceptions récentes -->
        <div class="col-lg-4 animate-fade-in delay-1">
            <div class="side-widget">
                <div class="card-header">
                    <h5>Tâches prioritaires</h5>
                </div>
                <div class="card-body">
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task1">
                        </div>
                        <div class="task-content">
                            <h6>Valider les réceptions en attente</h6>
                            <p>3 réceptions à traiter</p>
                        </div>
                        <span class="task-priority priority-high">Urgent</span>
                    </div>
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task2">
                        </div>
                        <div class="task-content">
                            <h6>Planifier les entretiens du mois</h6>
                            <p>5 véhicules concernés</p>
                        </div>
                        <span class="task-priority priority-medium">Moyen</span>
                    </div>
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task3">
                        </div>
                        <div class="task-content">
                            <h6>Mettre à jour les documents</h6>
                            <p>Assurances et cartes grises</p>
                        </div>
                        <span class="task-priority priority-low">Faible</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 5. Section graphique employés par département -->
    <div class="row mb-4">
        <div class="col-lg-12 animate-fade-in delay-4">
            <div class="chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="header-title-section">
                        <h5 class="mb-0">
                            <i class='bx bx-group me-2'></i>
                            Répartition des Fonctionnaires par Département
                        </h5>
                        <p class="text-muted small mb-0 mt-1">Distribution du personnel dans les différents départements</p>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: rgba(52, 152, 219, 1)"></div>
                            <span>Fonctionnaires (<?php echo e(count($employesParDepartement)); ?> départements)</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 450px;">
                        <canvas id="employeeChart" data-chart-data="<?php echo e(json_encode($employesParDepartement)); ?>"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 6. Section alertes et notifications -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="row">
                <div class="col-lg-6">
                    <div class="alert-widget alert-warning">
                        <h6><i class='bx bx-error-circle me-2'></i>Attention</h6>
                        <p>3 véhicules nécessitent un contrôle technique dans les 30 prochains jours.</p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="alert-widget alert-info">
                        <h6><i class='bx bx-info-circle me-2'></i>Information</h6>
                        <p>Nouvelle mise à jour du système disponible. Planifier la maintenance.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>
    <!-- Toast pour les notifications -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
        <div id="updateToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <small>À l'instant</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                Les données du tableau de bord ont été mises à jour avec succès.
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Dashboard Modern JS -->
<script src="<?php echo e(asset('js/dashboard-modern.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\GestionParkAutoFinal_ok\resources\views/admin/index.blade.php ENDPATH**/ ?>
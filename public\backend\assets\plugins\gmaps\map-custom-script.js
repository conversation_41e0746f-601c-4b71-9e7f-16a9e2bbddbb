// google map scripts
var map;

function initMap() {
	// Basic map  
	map = new google.maps.Map(document.getElementById('simple-map'), {
		center: {
			lat: -34.397,
			lng: 150.644
		},
		zoom: 8
	});
	// marker map
	var myLatLng = {
		lat: -25.363,
		lng: 131.044
	};
	var map = new google.maps.Map(document.getElementById('marker-map'), {
		zoom: 4,
		center: myLatLng
	});
	var marker = new google.maps.Marker({
		position: myLatLng,
		map: map,
		title: 'Hello World!'
	});
	// overlays map	
	var overlay;
	USGSOverlay.prototype = new google.maps.OverlayView();
	// Initialize the map and the custom overlay.
	function initMap() {
		var map = new google.maps.Map(document.getElementById('overlay-map'), {
			zoom: 11,
			center: {
				lat: 62.323907,
				lng: -150.109291
			},
			mapTypeId: 'satellite'
		});
		var bounds = new google.maps.LatLngBounds(new google.maps.LatLng(62.281819, -150.287132), new google.maps.LatLng(62.400471, -150.005608));
		// The photograph is courtesy of the U.S. Geological Survey.
		var srcImage = 'https://developers.google.com/maps/documentation/' + 'javascript/examples/full/images/talkeetna.png';
		// The custom USGSOverlay object contains the USGS image,
		// the bounds of the image, and a reference to the map.
		overlay = new USGSOverlay(bounds, srcImage, map);
	}
	/** @constructor */
	function USGSOverlay(bounds, image, map) {
		// Initialize all properties.
		this.bounds_ = bounds;
		this.image_ = image;
		this.map_ = map;
		// Define a property to hold the image's div. We'll
		// actually create this div upon receipt of the onAdd()
		// method so we'll leave it null for now.
		this.div_ = null;
		// Explicitly call setMap on this overlay.
		this.setMap(map);
	}
	/**
	 * onAdd is called when the map's panes are ready and the overlay has been
	 * added to the map.
	 */
	USGSOverlay.prototype.onAdd = function () {
		var div = document.createElement('div');
		div.style.borderStyle = 'none';
		div.style.borderWidth = '0px';
		div.style.position = 'absolute';
		// Create the img element and attach it to the div.
		var img = document.createElement('img');
		img.src = this.image_;
		img.style.width = '100%';
		img.style.height = '100%';
		img.style.position = 'absolute';
		div.appendChild(img);
		this.div_ = div;
		// Add the element to the "overlayLayer" pane.
		var panes = this.getPanes();
		panes.overlayLayer.appendChild(div);
	};
	USGSOverlay.prototype.draw = function () {
		// We use the south-west and north-east
		// coordinates of the overlay to peg it to the correct position and size.
		// To do this, we need to retrieve the projection from the overlay.
		var overlayProjection = this.getProjection();
		// Retrieve the south-west and north-east coordinates of this overlay
		// in LatLngs and convert them to pixel coordinates.
		// We'll use these coordinates to resize the div.
		var sw = overlayProjection.fromLatLngToDivPixel(this.bounds_.getSouthWest());
		var ne = overlayProjection.fromLatLngToDivPixel(this.bounds_.getNorthEast());
		// Resize the image's div to fit the indicated dimensions.
		var div = this.div_;
		div.style.left = sw.x + 'px';
		div.style.top = ne.y + 'px';
		div.style.width = (ne.x - sw.x) + 'px';
		div.style.height = (sw.y - ne.y) + 'px';
	};
	// The onRemove() method will be called automatically from the API if
	// we ever set the overlay's map property to 'null'.
	USGSOverlay.prototype.onRemove = function () {
		this.div_.parentNode.removeChild(this.div_);
		this.div_ = null;
	};
	google.maps.event.addDomListener(window, 'load', initMap);
	// polygons 
	var map = new google.maps.Map(document.getElementById('polygons-map'), {
		zoom: 5,
		center: {
			lat: 24.886,
			lng: -70.268
		},
		mapTypeId: 'terrain'
	});
	// Define the LatLng coordinates for the polygon's path.
	var triangleCoords = [{
		lat: 25.774,
		lng: -80.190
	}, {
		lat: 18.466,
		lng: -66.118
	}, {
		lat: 32.321,
		lng: -64.757
	}, {
		lat: 25.774,
		lng: -80.190
	}];
	// Construct the polygon.
	var bermudaTriangle = new google.maps.Polygon({
		paths: triangleCoords,
		strokeColor: '#FF0000',
		strokeOpacity: 0.8,
		strokeWeight: 2,
		fillColor: '#FF0000',
		fillOpacity: 0.35
	});
	bermudaTriangle.setMap(map);
	// Styles a map in night mode.
	var map = new google.maps.Map(document.getElementById('style-map'), {
		center: {
			lat: 40.674,
			lng: -73.945
		},
		zoom: 12,
		styles: [{
			elementType: 'geometry',
			stylers: [{
				color: '#242f3e'
			}]
		}, {
			elementType: 'labels.text.stroke',
			stylers: [{
				color: '#242f3e'
			}]
		}, {
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#746855'
			}]
		}, {
			featureType: 'administrative.locality',
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#d59563'
			}]
		}, {
			featureType: 'poi',
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#d59563'
			}]
		}, {
			featureType: 'poi.park',
			elementType: 'geometry',
			stylers: [{
				color: '#263c3f'
			}]
		}, {
			featureType: 'poi.park',
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#6b9a76'
			}]
		}, {
			featureType: 'road',
			elementType: 'geometry',
			stylers: [{
				color: '#38414e'
			}]
		}, {
			featureType: 'road',
			elementType: 'geometry.stroke',
			stylers: [{
				color: '#212a37'
			}]
		}, {
			featureType: 'road',
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#9ca5b3'
			}]
		}, {
			featureType: 'road.highway',
			elementType: 'geometry',
			stylers: [{
				color: '#746855'
			}]
		}, {
			featureType: 'road.highway',
			elementType: 'geometry.stroke',
			stylers: [{
				color: '#1f2835'
			}]
		}, {
			featureType: 'road.highway',
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#f3d19c'
			}]
		}, {
			featureType: 'transit',
			elementType: 'geometry',
			stylers: [{
				color: '#2f3948'
			}]
		}, {
			featureType: 'transit.station',
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#d59563'
			}]
		}, {
			featureType: 'water',
			elementType: 'geometry',
			stylers: [{
				color: '#17263c'
			}]
		}, {
			featureType: 'water',
			elementType: 'labels.text.fill',
			stylers: [{
				color: '#515c6d'
			}]
		}, {
			featureType: 'water',
			elementType: 'labels.text.stroke',
			stylers: [{
				color: '#17263c'
			}]
		}]
	});
}
// routes map
// style map
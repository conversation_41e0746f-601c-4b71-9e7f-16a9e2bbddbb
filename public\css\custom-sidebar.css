/* Styles personnalisés pour la sidebar avec fond bleu Facebook */
.sidebar-wrapper {
    background-color: #3b5998 !important; /* Bleu Facebook */
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Style pour le header de la sidebar */
.sidebar-header {
    background-color: #3b5998 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Style pour le texte du logo */
.sidebar-header .logo-text {
    color: #ffffff !important;
}

/* Style pour l'icône de toggle */
.sidebar-header .toggle-icon {
    color: #ffffff !important;
}

/* Style pour les éléments de menu */
.metismenu a {
    color: rgba(255, 255, 255, 0.85) !important;
}

/* Style pour les éléments de menu au survol */
.metismenu a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

/* Style pour les éléments de menu actifs */
.metismenu .mm-active > a {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* Style pour les icônes de menu */
.metismenu .parent-icon i {
    color: rgba(255, 255, 255, 0.85) !important;
}

/* Style pour les icônes de menu au survol */
.metismenu a:hover .parent-icon i {
    color: #ffffff !important;
}

/* Style pour les labels de menu */
.menu-label {
    color: rgba(255, 255, 255, 0.6) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Style pour les sous-menus */
.metismenu ul {
    background-color: rgba(0, 0, 0, 0.1) !important;
}

/* Style pour les éléments de sous-menu */
.metismenu ul a {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Style pour les éléments de sous-menu au survol */
.metismenu ul a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

/* Style pour les icônes de radio dans les sous-menus */
.metismenu ul a i.bx-radio-circle {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Style pour la barre de défilement */
.simplebar-scrollbar:before {
    background-color: rgba(255, 255, 255, 0.4) !important;
}

/* Style pour les flèches d'expansion des menus */
.metismenu .has-arrow::after {
    border-color: rgba(255, 255, 255, 0.7) !important;
}

/* Style pour les flèches d'expansion des menus au survol */
.metismenu .has-arrow:hover::after {
    border-color: #ffffff !important;
}

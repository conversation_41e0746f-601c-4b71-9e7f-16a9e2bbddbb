<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ComptabiliteMatiere
{
    /**
     * Middleware spécifique pour le module Comptabilité Matière
     * Vérifie si l'utilisateur a les permissions nécessaires pour accéder aux fonctionnalités
     * du module Comptabilité Matière selon son rôle et ses permissions
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $permission  Permission requise (optionnelle)
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $permission = null): Response
    {
        // Vérifier si l'utilisateur est authentifié
        if (!$request->user()) {
            return redirect()->route('login');
        }

        // Vérifier si l'utilisateur est admin (accès complet)
        if ($request->user()->role === 'admin') {
            return $next($request);
        }

        // Vérifier si l'utilisateur a le rôle 'comptable_matiere'
        if ($request->user()->role === 'comptable_matiere') {
            // Si aucune permission spécifique n'est requise, autoriser l'accès
            if ($permission === null) {
                return $next($request);
            }

            // Vérifier les permissions spécifiques
            // Note: Ceci est un exemple. Dans une implémentation réelle,
            // vous pourriez avoir une table de permissions en base de données
            $userPermissions = $this->getUserPermissions($request->user()->id);
            
            if (in_array($permission, $userPermissions)) {
                return $next($request);
            }
        }

        // Rediriger vers le tableau de bord avec un message d'erreur
        return redirect()->route('dashboard')
            ->with('error', 'Vous n\'avez pas les permissions nécessaires pour accéder à cette ressource.');
    }

    /**
     * Récupère les permissions de l'utilisateur
     * Dans une implémentation réelle, cette méthode récupérerait les permissions depuis la base de données
     *
     * @param int $userId
     * @return array
     */
    private function getUserPermissions($userId)
    {
        // Exemple de permissions pour le rôle 'comptable_matiere'
        // Dans une implémentation réelle, ces permissions seraient stockées en base de données
        return [
            'rapports.view',
            'rapports.export',
            'inventaires.view',
            'immobilisations.view',
        ];
    }
}

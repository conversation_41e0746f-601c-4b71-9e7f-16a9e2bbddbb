@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Journal Matière</h1>
        <div>
            <a href="{{ route('rapports.journal-matiere-form') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">
                <i class="fas fa-sync fa-sm text-white-50"></i> Modifier les paramètres
            </a>
            <a href="{{ route('rapports.journal-matiere', ['date_debut' => $dateDebut->format('Y-m-d'), 'date_fin' => $dateFin->format('Y-m-d'), 'export_pdf' => 1]) }}" class="d-none d-sm-inline-block btn btn-sm btn-danger shadow-sm">
                <i class="fas fa-file-pdf fa-sm text-white-50"></i> Exporter en PDF
            </a>
        </div>
    </div>

    <!-- Informations de la période -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Informations générales</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Période :</th>
                            <td>Du {{ $dateDebut->format('d/m/Y') }} au {{ $dateFin->format('d/m/Y') }}</td>
                        </tr>
                        <tr>
                            <th>Total entrées :</th>
                            <td>{{ $entrees->count() }} mouvements</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Total sorties :</th>
                            <td>{{ $sorties->count() }} mouvements</td>
                        </tr>
                        <tr>
                            <th>Total ajustements :</th>
                            <td>{{ $ajustements->count() }} mouvements</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Entrées -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Entrées de stock</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="tableEntrees" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Référence</th>
                            <th>Article</th>
                            <th>Quantité</th>
                            <th>P.U.</th>
                            <th>Montant</th>
                            <th>Source</th>
                            <th>Observations</th>
                            <th>Utilisateur</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($entrees as $entree)
                        <tr>
                            <td>{{ \Carbon\Carbon::parse($entree->date_entree)->format('d/m/Y') }}</td>
                            <td>{{ $entree->reference_doc }}</td>
                            <td>{{ $entree->article->designation }} ({{ $entree->article->code_article }})</td>
                            <td class="text-right">{{ $entree->quantite }} {{ $entree->article->unite->libelle }}</td>
                            <td class="text-right">{{ number_format($entree->prix_unitaire, 2, ',', ' ') }}</td>
                            <td class="text-right">{{ number_format($entree->quantite * $entree->prix_unitaire, 2, ',', ' ') }}</td>
                            <td>{{ $entree->source }}</td>
                            <td>{{ $entree->observations }}</td>
                            <td>{{ $entree->user->name }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Sorties -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Sorties de stock</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="tableSorties" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Article</th>
                            <th>Quantité</th>
                            <th>Bénéficiaire</th>
                            <th>Motif</th>
                            <th>Utilisateur</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sorties as $sortie)
                        <tr>
                            <td>{{ \Carbon\Carbon::parse($sortie->date_sortie)->format('d/m/Y') }}</td>
                            <td>{{ $sortie->article->designation }} ({{ $sortie->article->code_article }})</td>
                            <td class="text-right">{{ $sortie->quantite }} {{ $sortie->article->unite->libelle }}</td>
                            <td>{{ $sortie->beneficiaire->name }}</td>
                            <td>{{ $sortie->motif }}</td>
                            <td>{{ $sortie->user->name }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Ajustements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">Ajustements de stock</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="tableAjustements" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Article</th>
                            <th>Avant</th>
                            <th>Après</th>
                            <th>Écart</th>
                            <th>Motif</th>
                            <th>Utilisateur</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($ajustements as $ajustement)
                        <tr>
                            <td>{{ \Carbon\Carbon::parse($ajustement->date_ajustement)->format('d/m/Y') }}</td>
                            <td>{{ $ajustement->article->designation }} ({{ $ajustement->article->code_article }})</td>
                            <td class="text-right">{{ $ajustement->quantite_avant }} {{ $ajustement->article->unite->libelle }}</td>
                            <td class="text-right">{{ $ajustement->quantite_apres }} {{ $ajustement->article->unite->libelle }}</td>
                            <td class="text-right">
                                @php
                                    $ecart = $ajustement->quantite_apres - $ajustement->quantite_avant;
                                @endphp
                                @if($ecart > 0)
                                    <span class="text-success">+{{ $ecart }} {{ $ajustement->article->unite->libelle }}</span>
                                @else
                                    <span class="text-danger">{{ $ecart }} {{ $ajustement->article->unite->libelle }}</span>
                                @endif
                            </td>
                            <td>{{ $ajustement->motif }}</td>
                            <td>{{ $ajustement->user->name }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        $('#tableEntrees').DataTable({
            "order": [[ 0, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
        
        $('#tableSorties').DataTable({
            "order": [[ 0, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
        
        $('#tableAjustements').DataTable({
            "order": [[ 0, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
    });
</script>
@endsection

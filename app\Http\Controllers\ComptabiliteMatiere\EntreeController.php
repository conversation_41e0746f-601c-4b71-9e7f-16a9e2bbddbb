<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Entree;
use App\Models\Nomate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class EntreeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $entrees = Entree::with(['article', 'user'])
            ->orderBy('date_entree', 'desc')
            ->get();
        
        return view('comptabilite-matiere.entrees.index', compact('entrees'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $type = $request->query('type', 'immobilisation');
        
        // Récupérer les comptes matière depuis la table nomate
        $comptesMatiere = Nomate::where('statut', 'actif')
                            ->orderBy('code', 'asc')
                            ->pluck('intitule', 'code');
        
        // Récupérer aussi les articles pour la compatibilité avec le code existant
        $articles = Article::orderBy('designation', 'asc')->pluck('designation', 'id');
        
        return view('comptabilite-matiere.entrees.create', compact('articles', 'comptesMatiere', 'type'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Validation des champs communs
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string|max:50',
            'observations' => 'nullable|string',
            'type_entree' => 'required|string|in:immobilisation,consommable',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Vérifier si c'est un enregistrement groupé
        $isGrouped = $request->has('is_grouped') && $request->is_grouped == '1';

        // Si c'est un enregistrement groupé, valider les produits
        if ($isGrouped) {
            if (!$request->has('products') || !is_array($request->products) || count($request->products) == 0) {
                return redirect()->back()
                    ->with('error', 'Aucun produit n\'a été ajouté à l\'entrée groupée.')
                    ->withInput();
            }
        } else {
            // Validation pour un seul produit (méthode traditionnelle)
            $validator = Validator::make($request->all(), [
                'designation_matiere' => 'required|string',
                'compte_matiere' => 'required|string',
                'nature' => 'required|string',
                'format' => 'required|string',
                'etat' => 'required|string',
                'quantite' => 'required|numeric|min:0.01',
                'prix_unitaire' => 'required|numeric|min:0',
                'date_entree_oem' => 'required|date',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }
        }

        // Gérer le téléchargement du fichier si présent
        $pieceJustificativePath = null;
        if ($request->hasFile('piece_justificative') && $request->file('piece_justificative')->isValid()) {
            $pieceJustificative = $request->file('piece_justificative');
            $fileName = time() . '_' . $pieceJustificative->getClientOriginalName();
            $pieceJustificativePath = $pieceJustificative->storeAs('pieces_justificatives', $fileName, 'public');
        }

        DB::beginTransaction();
        try {
            if ($isGrouped) {
                // Traitement des produits groupés
                foreach ($request->products as $productData) {
                    // 1. Trouver ou créer l'article
                    $article = Article::firstOrCreate(
                        ['designation' => $productData['designation']],
                        [
                            'compte_matiere' => $productData['compte'],
                            'nature' => $productData['nature'],
                            'format' => $productData['format'],
                            'etat' => $productData['etat'],
                            'type' => $request->type_entree,
                            'unite_id' => 1, // ou une valeur par défaut
                            'stock_securite' => 10, // ou une valeur par défaut
                        ]
                    );

                    // 2. Créer l'entrée
                    Entree::create([
                        'article_id' => $article->id,
                        'quantite' => $productData['quantite'],
                        'prix_unitaire' => $productData['prix'],
                        'date_entree' => $productData['date'], // Date individuelle par produit
                        'reference_doc' => $request->reference,
                        'piece_justificative' => $pieceJustificativePath,
                        'observations' => $request->observations,
                        'user_id' => Auth::id(),
                    ]);
                }
            } else {
                // Traitement d'un seul produit (méthode traditionnelle)
                // 1. Trouver ou créer l'article
                $article = Article::firstOrCreate(
                    ['designation' => $request->designation_matiere],
                    [
                        'compte_matiere' => $request->compte_matiere,
                        'nature' => $request->nature,
                        'format' => $request->format,
                        'etat' => $request->etat,
                        'type' => $request->type_entree,
                        'unite_id' => 1, // ou une valeur par défaut
                        'stock_securite' => 10, // ou une valeur par défaut
                    ]
                );

                // 2. Créer l'entrée
                Entree::create([
                    'article_id' => $article->id,
                    'quantite' => $request->quantite,
                    'prix_unitaire' => $request->prix_unitaire,
                    'date_entree' => $request->date_entree_oem,
                    'reference_doc' => $request->reference,
                    'piece_justificative' => $pieceJustificativePath,
                    'observations' => $request->observations,
                    'user_id' => Auth::id(),
                ]);
            }

            DB::commit();

            $message = $isGrouped ? 
                count($request->products) . ' entrées en stock ont été enregistrées avec succès.' : 
                'L\'entrée en stock a été enregistrée avec succès.';

            return redirect()->route('entrees.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Une erreur est survenue lors de l\'enregistrement : ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $entree = Entree::with(['article', 'article.unite', 'user'])->findOrFail($id);
        return view('comptabilite-matiere.entrees.show', compact('entree'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $entree = Entree::findOrFail($id);
        $articles = Article::orderBy('designation', 'asc')->pluck('designation', 'id');
        
        return view('comptabilite-matiere.entrees.edit', compact('entree', 'articles'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $entree = Entree::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'quantite' => 'required|numeric|min:0.01',
            'prix_unitaire' => 'required|numeric|min:0',
            'date_entree' => 'required|date',
            'reference_doc' => 'required|string|max:50',
            'source' => 'required|string|max:100',
            'observations' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('entrees.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }

        $entree->update([
            'article_id' => $request->article_id,
            'quantite' => $request->quantite,
            'prix_unitaire' => $request->prix_unitaire,
            'date_entree' => $request->date_entree,
            'reference_doc' => $request->reference_doc,
            'source' => $request->source,
            'observations' => $request->observations,
        ]);

        return redirect()->route('entrees.index')
            ->with('success', 'Entrée en stock mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $entree = Entree::findOrFail($id);
        
        // Vérifier si la suppression de cette entrée ne créerait pas un stock négatif
        $article = Article::findOrFail($entree->article_id);
        $stockActuel = $article->stockActuel();
        
        if ($stockActuel < $entree->quantite) {
            return redirect()->route('entrees.index')
                ->with('error', 'Impossible de supprimer cette entrée car cela créerait un stock négatif.');
        }
        
        $entree->delete();

        return redirect()->route('entrees.index')
            ->with('success', 'Entrée en stock supprimée avec succès.');
    }
}

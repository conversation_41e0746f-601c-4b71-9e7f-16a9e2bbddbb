// Script de débogage pour la grille des véhicules
console.log('=== SCRIPT DE DÉBOGAGE GRILLE VÉHICULES ===');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé, vérification des éléments...');
    
    // Vérification des éléments principaux
    const gridView = document.getElementById('gridView');
    const tableView = document.getElementById('tableView');
    const gridSortOptions = document.getElementById('gridSortOptions');
    const vehicleCards = document.querySelectorAll('.vehicle-card');
    const viewSwitchBtns = document.querySelectorAll('.view-switch-btn');
    
    console.log('Éléments trouvés:');
    console.log('- gridView:', gridView);
    console.log('- tableView:', tableView);
    console.log('- gridSortOptions:', gridSortOptions);
    console.log('- vehicleCards:', vehicleCards.length);
    console.log('- viewSwitchBtns:', viewSwitchBtns.length);
    
    if (gridView) {
        console.log('Classes sur gridView:', gridView.className);
        console.log('Style display sur gridView:', window.getComputedStyle(gridView).display);
    }
    
    if (tableView) {
        console.log('Classes sur tableView:', tableView.className);
        console.log('Style display sur tableView:', window.getComputedStyle(tableView).display);
    }
    
    // Test de la grille CSS
    if (gridView) {
        const gridStyle = window.getComputedStyle(gridView);
        console.log('Styles CSS de la grille:');
        console.log('- display:', gridStyle.display);
        console.log('- grid-template-columns:', gridStyle.gridTemplateColumns);
        console.log('- gap:', gridStyle.gap);
    }
    
    // Test des cartes
    vehicleCards.forEach((card, index) => {
        console.log(`Carte ${index + 1}:`, {
            visible: window.getComputedStyle(card).display !== 'none',
            opacity: window.getComputedStyle(card).opacity,
            classes: card.className
        });
    });
    
    // Test des boutons de vue
    viewSwitchBtns.forEach((btn, index) => {
        console.log(`Bouton vue ${index + 1}:`, {
            dataView: btn.getAttribute('data-view'),
            active: btn.classList.contains('active'),
            classes: btn.className
        });
    });
});

// Test après chargement complet
window.addEventListener('load', function() {
    console.log('=== PAGE COMPLÈTEMENT CHARGÉE ===');

    // Forcer l'affichage de la grille de manière agressive
    function forceGridDisplay() {
        const gridView = document.getElementById('gridView');
        const tableView = document.getElementById('tableView');
        const gridSortOptions = document.getElementById('gridSortOptions');

        if (gridView && tableView) {
            console.log('FORÇAGE AGRESSIF de l\'affichage de la grille...');

            // Masquer le tableau complètement
            tableView.classList.add('d-none');
            tableView.style.cssText = 'display: none !important; visibility: hidden !important;';

            // Afficher la grille avec tous les styles nécessaires
            gridView.classList.remove('d-none');
            gridView.style.cssText = 'display: grid !important; visibility: visible !important; opacity: 1 !important; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)) !important; gap: 1.5rem !important; padding: 1rem 0 !important;';

            console.log('Grille FORCÉE à s\'afficher');
            console.log('gridView display après forçage:', window.getComputedStyle(gridView).display);
            console.log('gridView visibility:', window.getComputedStyle(gridView).visibility);
            console.log('gridView opacity:', window.getComputedStyle(gridView).opacity);

            // Vérifier le contenu de la grille
            const vehicleCards = gridView.querySelectorAll('.vehicle-card');
            console.log('Nombre de cartes véhicules dans la grille:', vehicleCards.length);
            console.log('Contenu HTML de la grille (premiers 500 caractères):', gridView.innerHTML.substring(0, 500));

            // Forcer l'affichage des cartes
            vehicleCards.forEach((card, index) => {
                card.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important;';
                console.log(`Carte ${index + 1} forcée à s'afficher`);
            });
        }

        // Afficher les options de tri
        if (gridSortOptions) {
            gridSortOptions.style.cssText = 'display: flex !important; visibility: visible !important;';
            console.log('Options de tri FORCÉES à s\'afficher');
        }

        // Activer le bouton grille
        const viewBtns = document.querySelectorAll('.view-switch-btn');
        viewBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-view') === 'grid') {
                btn.classList.add('active');
            }
        });
    }

    // Forcer immédiatement
    forceGridDisplay();

    // Forcer à nouveau après 1 seconde
    setTimeout(forceGridDisplay, 1000);

    // Forcer encore après 3 secondes
    setTimeout(forceGridDisplay, 3000);
});

@extends('admin.admin_dashboard')
@section('admin')

<style>
    .card {
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.05);
        border: none;
        margin-bottom: 24px;
    }
    .card-header {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 15px 20px;
        border-bottom: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .card-body {
        padding: 25px;
        border-radius: 0 0 15px 15px;
    }
    .btn-action {
        padding: 8px 16px;
        border-radius: 50px;
        font-weight: 500;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }
    .btn-success {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        border: none;
    }
    .badge {
        padding: 6px 12px;
        border-radius: 50px;
        font-weight: 500;
        font-size: 12px;
    }
    .badge-success {
        background-color: rgba(28, 200, 138, 0.15);
        color: #1cc88a;
        border: 1px solid rgba(28, 200, 138, 0.3);
    }
    .badge-warning {
        background-color: rgba(246, 194, 62, 0.15);
        color: #f6c23e;
        border: 1px solid rgba(246, 194, 62, 0.3);
    }
    .badge-danger {
        background-color: rgba(231, 74, 59, 0.15);
        color: #e74a3b;
        border: 1px solid rgba(231, 74, 59, 0.3);
    }
    .table {
        border-collapse: separate;
        border-spacing: 0 5px;
    }
    .table thead th {
        border-bottom: none;
        background-color: #f8f9fc;
        color: #6e707e;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 1px;
        padding: 15px;
    }
    .table tbody tr {
        box-shadow: 0 2px 10px rgba(0,0,0,0.03);
        border-radius: 5px;
        transition: all 0.2s;
    }
    .table tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .table tbody td {
        background-color: white;
        padding: 15px;
        vertical-align: middle;
        border-top: none;
    }
    .table tbody tr td:first-child {
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
    }
    .table tbody tr td:last-child {
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }
    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
    }
    .action-buttons .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 14px;
    }
    .empty-state {
        text-align: center;
        padding: 40px 20px;
    }
    .empty-state i {
        font-size: 60px;
        color: #d1d3e2;
        margin-bottom: 20px;
    }
    .empty-state h4 {
        color: #5a5c69;
        margin-bottom: 10px;
    }
    .empty-state p {
        color: #858796;
        margin-bottom: 20px;
    }
    .filter-row {
        background-color: #f8f9fc;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: center;
    }
    .filter-row .form-group {
        margin-bottom: 0;
        flex: 1;
        min-width: 200px;
    }
    .filter-row .form-control {
        border-radius: 50px;
        padding: 8px 15px;
        border: 1px solid #e3e6f0;
    }
    .filter-row .btn {
        padding: 8px 20px;
        border-radius: 50px;
    }
</style>

<div class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a></li>
                    <li class="breadcrumb-item">Enregistrements</li>
                    <li class="breadcrumb-item active" aria-current="page">Immobilisations</li>
                </ol>
            </nav>
        </div>
        <div class="ms-auto">
            <a href="{{ route('immobilisations.create') }}" class="btn btn-success btn-action">
                <i class="bx bx-plus"></i> Nouvelle Immobilisation
            </a>
        </div>
    </div>
    <!--end breadcrumb-->
    
    <div class="card animate__animated animate__fadeIn">
        <div class="card-header">
            <div>
                <i class="bx bx-building-house me-1"></i>
                <span class="fw-bold">Liste des Immobilisations</span>
            </div>
            <div>
                <button type="button" class="btn btn-light btn-sm" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bx bx-filter-alt"></i> Filtres
                </button>
                <button type="button" class="btn btn-light btn-sm" id="exportBtn">
                    <i class="bx bx-export"></i> Exporter
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="collapse mb-3" id="filterCollapse">
                <div class="filter-row">
                    <div class="form-group">
                        <label for="serviceFilter" class="form-label">Service</label>
                        <select class="form-select" id="serviceFilter">
                            <option value="">Tous les services</option>
                            <!-- Options seront ajoutées via JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="etatFilter" class="form-label">État physique</label>
                        <select class="form-select" id="etatFilter">
                            <option value="">Tous les états</option>
                            <option value="Bon">Bon</option>
                            <option value="Moyen">Moyen</option>
                            <option value="Mauvais">Mauvais</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="statutFilter" class="form-label">Statut</label>
                        <select class="form-select" id="statutFilter">
                            <option value="">Tous les statuts</option>
                            <option value="En service">En service</option>
                            <option value="Réformé">Réformé</option>
                            <option value="En panne">En panne</option>
                        </select>
                    </div>
                    <div class="form-group d-flex align-items-end">
                        <button type="button" class="btn btn-success" id="applyFilter">
                            <i class="bx bx-search"></i> Appliquer
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2" id="resetFilter">
                            <i class="bx bx-reset"></i> Réinitialiser
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="empty-state">
                <i class="bx bx-building-house"></i>
                <h4>Aucune immobilisation enregistrée</h4>
                <p>Vous n'avez pas encore enregistré d'immobilisations.</p>
                <a href="{{ route('immobilisations.create') }}" class="btn btn-success btn-action">
                    <i class="bx bx-plus"></i> Enregistrer une immobilisation
                </a>
            </div>
        </div>
    </div>
</div>

@endsection

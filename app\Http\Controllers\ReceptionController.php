<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Departement;
use App\Models\Reception;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Carbon\Carbon;

class ReceptionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function ListeReception()
    {
        $receptions = Reception::with('departement')->latest()->get();
        return view('reception.liste', compact('receptions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function AjoutReception()
    {
        //Etant donnée que sertaines données du département doivent afficher dans Employers
        $departements = Departement::all();
        return view('reception.ajouter');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function EnregReception(Request $request)
    {
        // Vérifie si un fichier PDF est présent dans la requête
        if ($request->file('pv_reception')) {
            
            // Génération d'un nom unique pour le fichier PDF
            $name_gen = hexdec(uniqid()).'.'.$request->file('pv_reception')->getClientOriginalExtension();
            
            // Enregistrement du fichier PDF dans le dossier 'upload/reception'
            $request->file('pv_reception')->move(public_path('upload/reception/'), $name_gen);
            
            // Création de l'URL pour sauvegarder dans la base de données
            $save_url = 'upload/reception/'.$name_gen;
            
            // Insertion des informations dans la base de données, incluant le chemin du fichier PDF
            Reception::insert([
                'date_enregistrement' => $request->date_enregistrement,
                'direction_structure' => $request->direction_structure,
                'reference_courier' => $request->reference_courier,
                'reference_marche' => $request->reference_marche,
                'objet' => $request->objet,
                'periode_execution' => $request->periode_execution,
                'executant' => $request->executant,
                'observation' => $request->observation,
                'pv_reception' => $save_url,  // Chemin du fichier PDF
                'created_at' => Carbon::now()
            ]);

            // Notification de succès
            $notification = array(
                'message' => 'L\'enregistrement de la réception a été effectué avec un PV de Réception',
                'alert-type' => 'success'
            );
        
            // Redirection vers la liste des réceptions avec la notification
            return redirect()->route('liste_reception')->with($notification);
        } else {
             // Insertion des informations dans la base de données, sans le chemin du fichier PDF
             Reception::insert([
                'date_enregistrement' => $request->date_enregistrement,
                'direction_structure' => $request->direction_structure,
                'reference_courier' => $request->reference_courier,
                'reference_marche' => $request->reference_marche,
                'objet' => $request->objet,
                'periode_execution' => $request->periode_execution,
                'executant' => $request->executant,
                'observation' => $request->observation,
                'created_at' => Carbon::now()
            ]);

            // Notification de succès
            $notification = array(
                'message' => 'L\'enregistrement de la réception a été effectué sans PV de Réception',
                'alert-type' => 'success'
            );
        
            // Redirection vers la liste des réceptions avec la notification
            return redirect()->route('liste_reception')->with($notification);
        }// Fin de if
    }
    
    

    /**
     * Display the specified resource.
     */
    public function showDetails($id)
    {
        // Récupérer la réception avec l'ID spécifié
        $reception = Reception::findOrFail($id);
        
        // Récupérer le département associé à cette réception
        $departement = Departement::find($reception->direction_structure);
        
        return view('reception.details', compact('reception', 'departement'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function EditReception($id)
    {
        //Etant donnée que sertaines données du département doivent afficher dans Employers
        $departements = Departement::all();
        $receptions = Reception::findOrFail($id);
        return view('reception.update', compact('receptions', 'departements'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function ModifReception(Request $request)
    {
        $reception_id = $request->id;
        // Vérifie si un fichier PDF est présent dans la requête
        if ($request->file('pv_reception')) {
            
            // Génération d'un nom unique pour le fichier PDF
            $name_gen = hexdec(uniqid()).'.'.$request->file('pv_reception')->getClientOriginalExtension();
            
            // Enregistrement du fichier PDF dans le dossier 'upload/reception'
            $request->file('pv_reception')->move(public_path('upload/reception/'), $name_gen);
            
            // Création de l'URL pour sauvegarder dans la base de données
            $save_url = 'upload/reception/'.$name_gen;
            
            // Insertion des informations dans la base de données, incluant le chemin du fichier PDF
            Reception::findOrFail($reception_id)->update([
                'date_enregistrement' => $request->date_enregistrement,
                'direction_structure' => $request->direction_structure,
                'reference_courier' => $request->reference_courier,
                'reference_marche' => $request->reference_marche,
                'objet' => $request->objet,
                'periode_execution' => $request->periode_execution,
                'executant' => $request->executant,
                'observation' => $request->observation,
                'pv_reception' => $save_url,  // Chemin du fichier PDF
                'created_at' => Carbon::now()
            ]);

            // Notification de succès
            $notification = array(
                'message' => 'L\'enregistrement de la réception a été mis à jour avec un PV de Réception',
                'alert-type' => 'success'
            );
        
            // Redirection vers la liste des réceptions avec la notification
            return redirect()->route('liste_reception')->with($notification);
        } else {
             // Insertion des informations dans la base de données, sans le chemin du fichier PDF
             Reception::findOrFail($reception_id)->update([
                'date_enregistrement' => $request->date_enregistrement,
                'direction_structure' => $request->direction_structure,
                'reference_courier' => $request->reference_courier,
                'reference_marche' => $request->reference_marche,
                'objet' => $request->objet,
                'periode_execution' => $request->periode_execution,
                'executant' => $request->executant,
                'observation' => $request->observation,
                'created_at' => Carbon::now()
            ]);

            // Notification de succès
            $notification = array(
                'message' => 'L\'enregistrement de la réception a été mis à jour sans PV de Réception',
                'alert-type' => 'success'
            );
        
            // Redirection vers la liste des réceptions avec la notification
            return redirect()->route('liste_reception')->with($notification);
        }// Fin de if
    }

    /**
     * Remove the specified resource from storage.
     */
    public function SuprReception($id){
        $reception = Reception::find($id);
        $reception->delete();

        // Notification de succès
        $notification = array(
            'message' => 'L\'enregistrement a été supprimé avec succès',
            'alert-type' => 'success'
        );
    
        // Redirection vers la liste des réceptions avec la notification
        return redirect()->route('liste_reception')->with($notification);
    }
}

@extends('admin.admin_dashboard')
@section('admin')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<!-- Ajout des styles personnalisés -->
<link rel="stylesheet" href="{{ asset('css/reception-style.css') }}">
<!-- Ajout de Choices.js pour la recherche dans les listes déroulantes -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css" />
<script src="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/scripts/choices.min.js"></script>
<!-- Style personnalisé pour les champs de formulaire -->
<style>
    /* Style uniforme pour tous les champs de formulaire */
    .form-control, 
    .form-select,
    .choices__inner {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        color: #000000;
        background-color: #fff;
        height: 46px; /* Hauteur fixe pour tous les champs */
        font-size: 0.95rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }
    
    /* Style spécifique pour Choices.js */
    .choices {
        margin-bottom: 0;
        width: 100%;
    }
    
    .choices__inner {
        background-color: #fff;
        border: 2px solid #ced4da !important;
        min-height: 46px;
        padding: 6px 12px;
    }
    
    /* Correction pour l'affichage des bordures dans l'input-group */
    .input-group .choices {
        position: relative;
        flex: 1 1 auto;
        width: 1%;
    }
    
    .input-group .choices__inner {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        border-left: none !important;
    }
    
    /* Style au survol pour mieux voir les bordures */
    .choices:hover .choices__inner {
        border-color: #adb5bd !important;
    }
    
    /* Style uniforme au focus */
    .form-control:focus, 
    .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        outline: none;
        transform: translateY(-2px);
    }
    
    /* Style spécifique pour Choices.js au focus */
    .choices.is-focused .choices__inner {
        border-color: #4e73df !important;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        transform: translateY(-2px);
    }
    
    /* Style pour l'état actif de Choices.js */
    .choices.is-open .choices__inner {
        border-color: #4e73df !important;
        border-radius: 0.375rem 0.375rem 0 0 !important;
    }
    
    /* Style pour le dropdown de Choices.js */
    .choices__list--dropdown {
        border: 2px solid #4e73df !important;
        border-top: 0 !important;
        border-radius: 0 0 0.375rem 0.375rem !important;
        margin-top: -1px !important;
    }
    
    /* Style pour la zone de recherche dans le dropdown */
    .choices__input {
        background-color: #fff !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.25rem !important;
        margin-bottom: 8px !important;
        padding: 8px !important;
    }

    /* Styles pour les groupes d'entrée */
    .input-group {
        display: flex;
        align-items: center;
        width: 100%;
    }
    
    .input-group-text {
        background-color: #eef2ff;
        border-color: #ced4da;
        color: #4e73df;
        padding: 0 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem 0 0 0.375rem;
        min-width: 45px;
        font-size: 1.1rem;
        height: 46px;
    }
    
    /* Correction pour l'alignement des select dans les input-group */
    .input-group .form-select,
    .input-group .form-control,
    .input-group .choices {
        flex: 1 1 auto;
        width: 1%;
    }
    
    /* Styles pour Choices.js */
    .choices__inner {
        min-height: 46px;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
    }
    
    .choices__list--single {
        padding: 0.25rem 0;
    }
    
    .choices__list--dropdown {
        border-radius: 0.375rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .choices__list--dropdown .choices__item--selectable {
        padding: 0.75rem 1rem;
    }
    
    .choices__list--dropdown .choices__item--selectable.is-highlighted {
        background-color: #4e73df;
        color: white;
    }
    
    .input-group .choices__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Assurer que tous les éléments ont la même hauteur */
    .input-group > * {
        height: 46px;
    }
</style>
<div class="page-content">
	<!--breadcrumb-->
	<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4 reception-breadcrumb">
		<div class="breadcrumb-title pe-3">
			<i class='bx bx-file-find me-2'></i>Enregistrement d'une Réception de Marché
		</div>
		<div class="ps-3">
			<nav aria-label="breadcrumb">
				<ol class="breadcrumb mb-0 p-0">
					<li class="breadcrumb-item">
						<a href="{{ route('liste_reception') }}"><i class="bx bx-home-alt"></i> Accueil</a>
					</li>
					<li class="breadcrumb-item active" aria-current="page">Nouvelle Réception</li>
				</ol>
			</nav>
		</div>
	</div>
	<!--end breadcrumb-->
	<div class="container">
		<div class="main-body">
			<div class="row">
				<div class="col-lg-12">
					<div class="card reception-card">
						<div class="card-header">
							<h4 class="mb-0"><i class='bx bx-plus-circle me-2'></i>Enregistrer une Nouvelle Réception de Marché</h4>
						</div>
						<div class="card-body p-4">
							<form class="row g-3 reception-form" id="myForm" action="{{ route('enregistrer_reception') }}" method="POST" enctype="multipart/form-data">
								@csrf
                                @method('POST')
                                <span hidden>{{ $departements = App\Models\Departement::all(); }}</span>
                                <!-- Section Informations Générales -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-info-circle me-2'></i>Informations Générales</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="date_enregistrement" class="form-label">Date d'Enregistrement</label>
                                            <div class="position-relative input-icon">
                                                <input type="date" class="form-control" id="date_enregistrement" name="date_enregistrement" value="{{ old('date_enregistrement') }}" required>
                                                <span class="position-absolute top-50 translate-middle-y"><i class='bx bx-calendar'></i></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="departement_id" class="form-label">Direction / Structure</label>
                                            <div class="input-group align-items-center">
                                                <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-buildings"></i></span>
                                                <select id="departement_id" name="direction_structure" class="form-select" required>
                                                    <option value="">Choisir la Direction / Structure...</option>
                                                    @foreach($departements as $departement)
                                                    <option value="{{$departement->id}}">{{$departement->nom_departement}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Section Références -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-link me-2'></i>Références</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="reference_courier" class="form-label">Références du Courier</label>
                                            <div class="position-relative input-icon">
                                                <input type="text" class="form-control" id="reference_courier" name="reference_courier" value="{{ old('reference_courier') }}" placeholder="Saisir les références du courier">
                                                <span class="position-absolute top-50 translate-middle-y"><i class='bx bx-envelope'></i></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="reference_marche" class="form-label">Références du Marché</label>
                                            <div class="position-relative input-icon">
                                                <input type="text" class="form-control" id="reference_marche" name="reference_marche" value="{{ old('reference_marche') }}" placeholder="Saisir les références du marché">
                                                <span class="position-absolute top-50 translate-middle-y"><i class='bx bx-purchase-tag'></i></span>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <label for="objet" class="form-label">Objet</label>
                                            <div class="position-relative input-icon">
                                                <input type="text" class="form-control" id="objet" name="objet" value="{{ old('objet') }}" placeholder="Saisir l'Objet">
                                                <span class="position-absolute top-50 translate-middle-y"><i class='bx bx-notepad'></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Section Exécution -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-time me-2'></i>Détails d'Exécution</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="periode_execution" class="form-label">Périodes d'Exécution</label>
                                            <div class="position-relative input-icon">
                                                <input type="text" class="form-control" id="periode_execution" name="periode_execution" value="{{ old('periode_execution') }}" placeholder="Saisir les périodes d'exécution">
                                                <span class="position-absolute top-50 translate-middle-y"><i class='bx bx-calendar-event'></i></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="executant" class="form-label">Exécutant</label>
                                            <div class="position-relative input-icon">
                                                <input type="text" class="form-control" id="executant" name="executant" value="{{ old('executant') }}" placeholder="Saisir le nom de l'exécutant">
                                                <span class="position-absolute top-50 translate-middle-y"><i class='bx bx-user'></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Section Documents -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-file me-2'></i>Documents</h5>
                                    <div class="row g-3">
                                        <div class="col-md-12">
                                            <label for="pv_reception" class="form-label">Document scanné du PV de Réception</label>
                                            <div class="file-upload-wrapper">
                                                <div id="file-label">
                                                    <i class='bx bx-upload upload-icon'></i>
                                                    <div>Cliquez ou glissez un fichier ici</div>
                                                </div>
                                                <div id="file-info" class="file-info"></div>
                                                <input class="form-control" type="file" name="pv_reception" id="pv_reception">
                                            </div>
                                            <div class="custom-checkbox">
                                                <input type="checkbox" id="pv_checked" disabled>
                                                <label for="pv_checked">PV de Réception téléchargé</label>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <label for="observation" class="form-label">Observations</label>
                                            <div class="position-relative input-icon">
                                                <textarea class="form-control" id="observation" name="observation" rows="3" placeholder="Saisir l'Observation si nécessaire">{{ old('observation') }}</textarea>
                                                <span class="position-absolute top-0 mt-2 ms-2"><i class='bx bx-comment-detail'></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Boutons d'action -->
                                <div class="col-md-12 mt-4">
                                    <div class="d-flex justify-content-between">
                                        <a href="{{ route('liste_reception') }}" class="btn btn-outline-secondary px-4">
                                            <i class='bx bx-arrow-back me-1'></i> Retour
                                        </a>
                                        <button type="submit" class="btn btn-reception px-5">
                                            <i class='bx bx-save me-1'></i> Enregistrer
                                        </button>
                                    </div>
                                </div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Inclusion du script personnalisé -->
<script src="{{ asset('js/reception-script.js') }}"></script>

<script type="text/javascript">
    $(document).ready(function() {
        // Configuration de base pour Choices.js
        const configChoices = {
            removeItemButton: true,
            searchEnabled: true,
            searchPlaceholderValue: 'Rechercher une direction/structure...',
            noResultsText: 'Aucun résultat trouvé',
            noChoicesText: 'Aucun choix disponible',
            itemSelectText: 'Cliquer pour sélectionner',
            shouldSort: false,
            position: 'bottom',
            searchResultLimit: 50,
            classNames: {
                containerOuter: 'choices',
                containerInner: 'choices__inner',
                input: 'choices__input',
                inputCloned: 'choices__input--cloned',
                list: 'choices__list',
                listItems: 'choices__list--multiple',
                listSingle: 'choices__list--single',
                listDropdown: 'choices__list--dropdown',
                item: 'choices__item',
                itemSelectable: 'choices__item--selectable',
                itemDisabled: 'choices__item--disabled',
                itemChoice: 'choices__item--choice',
                placeholder: 'choices__placeholder',
                group: 'choices__group',
                groupHeading: 'choices__heading',
                button: 'choices__button',
                activeState: 'is-active',
                focusState: 'is-focused',
                openState: 'is-open',
                disabledState: 'is-disabled',
                highlightedState: 'is-highlighted',
                selectedState: 'is-selected',
                flippedState: 'is-flipped',
                loadingState: 'is-loading',
                noResults: 'has-no-results',
                noChoices: 'has-no-choices'
            },
            fuseOptions: {
                includeScore: true,
                threshold: 0.3,
                distance: 100
            }
        };
        
        // Initialiser le select pour les départements avec Choices.js
        const departementSelect = new Choices('#departement_id', configChoices);
        
        // Ajouter une classe spéciale pour le style
        setTimeout(() => {
            $('.choices').addClass('is-focused');
            setTimeout(() => {
                $('.choices').removeClass('is-focused');
            }, 1000);
        }, 500);
        
        // Animation des champs du formulaire
        $('.reception-form .form-control').on('focus', function() {
            $(this).parent().addClass('input-focus');
        }).on('blur', function() {
            $(this).parent().removeClass('input-focus');
        });

        // Gestion de l'upload de fichier avec feedback visuel
        $('#pv_reception').on('change', function() {
            const fileInput = $(this);
            const fileLabel = $('#file-label');
            const fileInfo = $('#file-info');
            const checkbox = $('#pv_checked');
            
            if (fileInput[0].files.length > 0) {
                const fileName = fileInput[0].files[0].name;
                fileInfo.text(fileName).show();
                checkbox.prop('checked', true);
                fileLabel.html('<i class="bx bx-check-circle upload-icon text-success"></i><div>Fichier sélectionné</div>');
                $('.file-upload-wrapper').addClass('has-file');
            } else {
                fileInfo.hide();
                checkbox.prop('checked', false);
                fileLabel.html('<i class="bx bx-upload upload-icon"></i><div>Cliquez ou glissez un fichier ici</div>');
                $('.file-upload-wrapper').removeClass('has-file');
            }
        });

        // Validation du formulaire
        $('#myForm').validate({
            rules: {
                date_enregistrement: {
                    required: true,
                },
                direction_structure: {
                    required: true,
                },
            },
            messages: {
                date_enregistrement: {
                    required: 'Veuillez sélectionner une date d\'enregistrement',
                },
                direction_structure: {
                    required: 'Veuillez choisir une direction/structure',
                },
            },
            errorElement: 'span',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest('.position-relative').append(error);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid').removeClass('is-valid');
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },
            submitHandler: function(form) {
                // Animation du bouton lors de la soumission
                const submitBtn = $(form).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="bx bx-loader bx-spin me-1"></i> Enregistrement en cours...');
                submitBtn.prop('disabled', true);
                
                // Soumettre le formulaire après un court délai pour montrer l'animation
                setTimeout(function() {
                    form.submit();
                }, 500);
            }
        });

        // Initialisation des tooltips Bootstrap
        if (typeof bootstrap !== 'undefined' && typeof bootstrap.Tooltip !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Effet de transition pour les sections du formulaire
        $('.form-section').each(function(index) {
            $(this).css({
                'opacity': 0,
                'transform': 'translateY(20px)'
            }).delay(index * 100).animate({
                'opacity': 1,
                'transform': 'translateY(0)'
            }, 500);
        });
    });
</script>
@endsection
@extends('admin.admin_dashboard')
@section('admin')
<!-- Inclusion des fichiers CSS spécifiques à la page de détails des réceptions -->
<link rel="stylesheet" href="{{ asset('css/reception-details.css') }}">
<!-- Inclusion de SweetAlert2 pour les confirmations -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.19/dist/sweetalert2.min.css">

<div class="page-content">
    <!-- En-tête avec titre et actions -->
    <div class="details-header animate-fade-in">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h4>
                    <i class='bx bx-detail me-2'></i>
                    Détails de la Réception
                </h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 p-0 mt-2">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i> Accueil</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('liste_reception') }}">Liste des Réceptions</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Détails de la Réception</li>
                    </ol>
                </nav>
            </div>
            <div class="col-lg-4">
                <div class="text-lg-end mt-3 mt-lg-0">
                    <a href="{{ route('liste_reception') }}" class="btn btn-secondary btn-details">
                        <i class='bx bx-arrow-back'></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Méta-informations -->
    <div class="details-meta animate-fade-in">
        <div class="details-meta-item">
            <i class='bx bx-calendar'></i>
            <span>Créé le {{ \Carbon\Carbon::parse($reception->created_at)->format('d/m/Y à H:i') }}</span>
        </div>
        <div class="details-meta-item">
            <i class='bx bx-refresh'></i>
            <span>Dernière mise à jour le {{ \Carbon\Carbon::parse($reception->updated_at)->format('d/m/Y à H:i') }}</span>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Carte d'informations principales -->
            <div class="details-card animate-fade-in animate-delay-1">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class='bx bx-info-circle me-2'></i> Informations Générales
                    </h5>
                </div>
                <div class="card-body">
                    <div class="details-section">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="details-item">
                                    <div class="details-label">Date d'Enregistrement</div>
                                    <div class="details-value">{{ \Carbon\Carbon::parse($reception->date_enregistrement)->format('d/m/Y') }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="details-item">
                                    <div class="details-label">Direction / Structure</div>
                                    <div class="details-value">{{ $departement->nom_departement ?? 'Non défini' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="details-section">
                        <h6 class="details-section-title">
                            <i class='bx bx-link'></i> Références
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="details-item">
                                    <div class="details-label">Références du Courier</div>
                                    <div class="details-value">{{ $reception->reference_courier ?: 'Non spécifié' }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="details-item">
                                    <div class="details-label">Références du Marché</div>
                                    <div class="details-value">{{ $reception->reference_marche ?: 'Non spécifié' }}</div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="details-item">
                                    <div class="details-label">Objet</div>
                                    <div class="details-value">{{ $reception->objet ?: 'Non spécifié' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="details-section">
                        <h6 class="details-section-title">
                            <i class='bx bx-time'></i> Détails d'Exécution
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="details-item">
                                    <div class="details-label">Période d'Exécution</div>
                                    <div class="details-value">{{ $reception->periode_execution ?: 'Non spécifié' }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="details-item">
                                    <div class="details-label">Exécutant</div>
                                    <div class="details-value">{{ $reception->executant ?: 'Non spécifié' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($reception->observation)
                    <div class="details-section">
                        <h6 class="details-section-title">
                            <i class='bx bx-comment'></i> Observations
                        </h6>
                        <div class="details-item">
                            <div class="details-value">{{ $reception->observation }}</div>
                        </div>
                    </div>
                    @endif

                    <div class="details-actions">
                        <div>
                            <a href="{{ route('editer_reception', $reception->id) }}" class="btn btn-primary btn-details">
                                <i class='bx bx-edit'></i> Modifier
                            </a>
                            <a href="{{ route('supprimer_reception', $reception->id) }}" class="btn btn-secondary btn-details ms-2" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette réception ?')">
                                <i class='bx bx-trash'></i> Supprimer
                            </a>
                        </div>
                        <div>
                            <a href="{{ route('liste_reception') }}" class="btn btn-secondary btn-details">
                                <i class='bx bx-arrow-back'></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Carte d'état et documents -->
            <div class="details-card animate-fade-in animate-delay-2">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class='bx bx-file me-2'></i> Documents et Statut
                    </h5>
                </div>
                <div class="card-body">
                    <div class="details-item mb-4">
                        <div class="details-label">Statut du PV</div>
                        <div class="details-value">
                            @if($reception->pv_reception)
                                <span class="badge badge-success">
                                    <i class='bx bx-check'></i> PV disponible
                                </span>
                            @else
                                <span class="badge badge-warning">
                                    <i class='bx bx-x'></i> Pas de PV disponible
                                </span>
                            @endif
                        </div>
                    </div>

                    @if($reception->pv_reception)
                        <div class="details-item">
                            <div class="details-label">Document PV</div>
                            <div class="document-preview my-3">
                                @php
                                    $extension = pathinfo($reception->pv_reception, PATHINFO_EXTENSION);
                                    $isPdf = strtolower($extension) === 'pdf';
                                    $isImage = in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                    $filename = basename($reception->pv_reception);
                                @endphp

                                @if($isPdf)
                                    <div class="document-embed pdf-embed">
                                        <iframe src="{{ asset($reception->pv_reception) }}" class="pdf-viewer" frameborder="0"></iframe>
                                    </div>
                                @elseif($isImage)
                                    <div class="document-embed image-embed">
                                        <img src="{{ asset($reception->pv_reception) }}" alt="Aperçu du PV" class="img-fluid document-image">
                                    </div>
                                @else
                                    <div class="document-embed other-file-embed">
                                        <div class="file-icon">
                                            <i class='bx bxs-file' style="font-size: 4rem; color: #4e73df;"></i>
                                            <p class="file-extension">{{ strtoupper($extension) }}</p>
                                        </div>
                                        <p class="mt-2">Ce type de fichier ne peut pas être prévisualisé directement.</p>
                                    </div>
                                @endif

                                <div class="document-info mt-3">
                                    <div class="file-meta">
                                        <span class="file-name">{{ $filename }}</span>
                                        <span class="file-type">{{ strtoupper($extension) }}</span>
                                    </div>
                                </div>

                                <div class="document-actions mt-3">
                                    <a href="{{ route('documents.show_pv', $filename) }}" class="btn btn-primary btn-details w-100 mb-2" target="_blank">
                                        <i class='bx bx-fullscreen'></i> Voir en plein écran
                                    </a>
                                    <a href="{{ asset($reception->pv_reception) }}" class="btn btn-success btn-details w-100" download>
                                        <i class='bx bx-download'></i> Télécharger
                                    </a>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center my-4">
                            <div class="mb-3">
                                <i class='bx bx-file' style="font-size: 4rem; color: #adb5bd;"></i>
                            </div>
                            <p>Aucun document PV n'a été téléchargé pour cette réception.</p>
                            <a href="{{ route('editer_reception', $reception->id) }}" class="btn btn-primary btn-details">
                                <i class='bx bx-upload'></i> Ajouter un PV
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.19/dist/sweetalert2.all.min.js"></script>
<script src="{{ asset('js/reception-details.js') }}"></script>
@endsection

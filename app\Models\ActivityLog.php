<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActivityLog extends Model
{
    use HasFactory;

    /**
     * La table associée au modèle.
     *
     * @var string
     */
    protected $table = 'activity_logs';

    /**
     * Les attributs qui sont assignables en masse.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'module',
        'action',
        'description',
        'ip_address',
        'user_agent',
        'before_data',
        'after_data',
    ];

    /**
     * Les attributs qui doivent être castés.
     *
     * @var array
     */
    protected $casts = [
        'before_data' => 'array',
        'after_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Récupère l'utilisateur associé à cette activité.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Enregistre une nouvelle activité dans le journal.
     *
     * @param string $module Le module concerné (ex: 'comptabilite-matiere')
     * @param string $action L'action effectuée (ex: 'create', 'update', 'delete')
     * @param string $description Description détaillée de l'action
     * @param array|null $beforeData Données avant modification (pour les mises à jour)
     * @param array|null $afterData Données après modification (pour les créations et mises à jour)
     * @return ActivityLog
     */
    public static function log($module, $action, $description, $beforeData = null, $afterData = null)
    {
        $request = request();
        
        return self::create([
            'user_id' => auth()->id(),
            'module' => $module,
            'action' => $action,
            'description' => $description,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'before_data' => $beforeData,
            'after_data' => $afterData,
        ]);
    }
}

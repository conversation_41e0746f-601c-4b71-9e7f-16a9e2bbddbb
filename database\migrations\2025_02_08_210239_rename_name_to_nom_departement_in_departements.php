<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('departements', function (Blueprint $table) {
            $table->string('nom_departement')->after('id');
        });

        // Copier les données
        DB::statement('UPDATE departements SET nom_departement = name');

        Schema::table('departements', function (Blueprint $table) {
            $table->dropColumn('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('departements', function (Blueprint $table) {
            $table->string('name')->after('id');
        });

        // Copier les données en retour
        DB::statement('UPDATE departements SET name = nom_departement');

        Schema::table('departements', function (Blueprint $table) {
            $table->dropColumn('nom_departement');
        });
    }
};

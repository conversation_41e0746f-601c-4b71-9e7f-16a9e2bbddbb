<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Inventaire;
use App\Models\InventaireDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class InventaireController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $inventaires = Inventaire::with('user')
            ->orderBy('date_inventaire', 'desc')
            ->get();
        
        return view('comptabilite-matiere.inventaires.index', compact('inventaires'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('comptabilite-matiere.inventaires.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string|max:50|unique:inventaires',
            'date_inventaire' => 'required|date',
            'observations' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('inventaires.create')
                ->withErrors($validator)
                ->withInput();
        }

        // Créer l'inventaire
        $inventaire = Inventaire::create([
            'reference' => $request->reference,
            'date_inventaire' => $request->date_inventaire,
            'observations' => $request->observations,
            'user_id' => Auth::id(),
            'statut' => 'en_cours', // Statut initial
        ]);

        // Récupérer tous les articles pour les ajouter à l'inventaire
        $articles = Article::all();
        
        // Créer les détails d'inventaire pour chaque article
        foreach ($articles as $article) {
            $stockTheorique = $article->stockActuel();
            
            InventaireDetail::create([
                'inventaire_id' => $inventaire->id,
                'article_id' => $article->id,
                'stock_theorique' => $stockTheorique,
                'stock_reel' => 0, // Sera mis à jour lors de la saisie des quantités réelles
            ]);
        }

        return redirect()->route('inventaires.edit', $inventaire->id)
            ->with('success', 'Inventaire créé avec succès. Veuillez maintenant saisir les quantités réelles.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $inventaire = Inventaire::with(['details', 'details.article', 'details.article.unite', 'user'])
            ->findOrFail($id);
        
        $ecartTotal = $inventaire->ecartTotal();
        
        return view('comptabilite-matiere.inventaires.show', compact('inventaire', 'ecartTotal'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $inventaire = Inventaire::with(['details', 'details.article', 'details.article.unite'])
            ->findOrFail($id);
        
        // Vérifier si l'inventaire est encore en cours
        if ($inventaire->statut !== 'en_cours') {
            return redirect()->route('inventaires.show', $id)
                ->with('error', 'Cet inventaire est déjà clôturé et ne peut plus être modifié.');
        }
        
        return view('comptabilite-matiere.inventaires.edit', compact('inventaire'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $inventaire = Inventaire::findOrFail($id);
        
        // Vérifier si l'inventaire est encore en cours
        if ($inventaire->statut !== 'en_cours') {
            return redirect()->route('inventaires.show', $id)
                ->with('error', 'Cet inventaire est déjà clôturé et ne peut plus être modifié.');
        }
        
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string|max:50|unique:inventaires,reference,' . $id,
            'date_inventaire' => 'required|date',
            'observations' => 'nullable|string',
            'stock_reel' => 'required|array',
            'stock_reel.*' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->route('inventaires.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }

        // Mettre à jour les informations de l'inventaire
        $inventaire->update([
            'reference' => $request->reference,
            'date_inventaire' => $request->date_inventaire,
            'observations' => $request->observations,
        ]);
        
        // Mettre à jour les quantités réelles pour chaque article
        foreach ($request->stock_reel as $detailId => $stockReel) {
            $detail = InventaireDetail::findOrFail($detailId);
            $detail->update([
                'stock_reel' => $stockReel,
            ]);
        }

        // Si l'action est de clôturer l'inventaire
        if ($request->has('cloturer')) {
            $inventaire->update([
                'statut' => 'cloture',
                'date_cloture' => now(),
            ]);
            
            // Créer des ajustements de stock si nécessaire
            $this->creerAjustements($inventaire);
            
            return redirect()->route('inventaires.show', $id)
                ->with('success', 'Inventaire clôturé avec succès.');
        }

        return redirect()->route('inventaires.edit', $id)
            ->with('success', 'Inventaire mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $inventaire = Inventaire::findOrFail($id);
        
        // Vérifier si l'inventaire est clôturé
        if ($inventaire->statut === 'cloture') {
            return redirect()->route('inventaires.index')
                ->with('error', 'Impossible de supprimer un inventaire clôturé.');
        }
        
        // Supprimer les détails d'inventaire associés
        $inventaire->details()->delete();
        
        // Supprimer l'inventaire
        $inventaire->delete();

        return redirect()->route('inventaires.index')
            ->with('success', 'Inventaire supprimé avec succès.');
    }
    
    /**
     * Créer des ajustements de stock basés sur les écarts d'inventaire
     *
     * @param  \App\Models\Inventaire  $inventaire
     * @return void
     */
    private function creerAjustements(Inventaire $inventaire)
    {
        // Cette méthode pourrait être implémentée pour créer automatiquement des ajustements
        // de stock basés sur les écarts constatés lors de l'inventaire
        // Pour l'instant, nous laissons cette fonctionnalité à implémenter ultérieurement
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reforme extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'immobilisation_id',
        'date_reforme',
        'motif',
        'reference_decision',
        'user_id'
    ];
    
    protected $casts = [
        'date_reforme' => 'date',
    ];
    
    public function immobilisation()
    {
        return $this->belongsTo(Immobilisation::class);
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}

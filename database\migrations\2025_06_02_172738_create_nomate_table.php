<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nomate', function (Blueprint $table) {
            $table->id();
            $table->string('code')->index();
            $table->string('intitule');
            $table->boolean('is_header')->default(false)->comment('Indique si c\'est un titre de section');
            $table->string('statut')->default('actif');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nomate');
    }
};

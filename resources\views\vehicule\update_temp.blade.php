@extends('admin.admin_dashboard')
@section('admin')

@section('styles')
    <link rel="stylesheet" href="{{ asset('css/vehicule/form.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
    /* Style spécifique pour la marge inférieure des sections */
    #informations-generales .form-card-body,
    #caracteristiques-techniques .form-card-body,
    .form-card-body {
        padding-bottom: 150px !important;
    }
    
    /* Style spécifique pour la section Utilisation */
    .form-card:nth-child(3) .form-card-body {
        padding-bottom: 200px !important;
        margin-bottom: 50px;
    }
    
    /* Correction pour Bootstrap */
    .form-select:not([multiple]):not([size]) {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
        background-position: right 0.75rem center !important;
        background-repeat: no-repeat !important;
        background-size: 16px 12px !important;
    }

    .form-label {
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
    }

    /* Style uniforme pour tous les champs de formulaire */
    .form-control, 
    .form-select,
    .choices__inner,
    .bootstrap-select > .dropdown-toggle,
    .selectpicker {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        color: #000000;
        background-color: #fff;
        height: 46px; /* Hauteur fixe pour tous les champs */
        font-size: 0.95rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        display: flex;
        align-items: center;
    }
    
    /* Styles pour Select2 */
    .select2-container--default .select2-selection--single {
        height: 46px;
        padding: 0.5rem 1rem;
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 28px;
        color: #000000;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 44px;
    }
    
    .select2-dropdown {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid var(--primary);
        border-radius: 0.25rem;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        box-shadow: 0 0 5px rgba(67, 97, 238, 0.2);
    }
    
    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: var(--primary);
        color: white;
    }
    
    .select2-container--default .select2-results__option {
        padding: 8px 12px;
    }
    
    .select2-container--open .select2-dropdown--below {
        margin-top: 5px;
    }
    
    /* Correction pour l'input-group avec Select2 */
    .input-group .select2-container {
        flex: 1 1 auto;
        width: 1% !important;
    }
    
    .input-group .select2-container .select2-selection--single {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Style spécifique pour le conteneur Select2 dans la section Utilisation */
    .form-card:nth-child(3) .select2-container--open .select2-dropdown--below {
        margin-top: 5px;
        z-index: 9999;
    }
    
    /* Styles pour les boutons Bootstrap-select */
    .bootstrap-select .btn {
        border: 1px solid var(--gray-300);
        background-color: #fff;
        color: #000000;
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        font-weight: 400;
        line-height: 1.5;
        text-align: left;
        height: 46px;
    }
    
    .bootstrap-select .dropdown-toggle::after {
        margin-left: auto;
    }
    
    .bootstrap-select .dropdown-menu {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 0.5rem 0;
        margin: 0.125rem 0 0;
    }
    
    .bootstrap-select > .dropdown-toggle:focus,
    .bootstrap-select .dropdown-toggle:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        outline: 0;
    }

    /* Style pour les input-group */
    .input-group-text {
        background-color: var(--primary-light);
        border-color: var(--gray-300);
        color: var(--primary);
        padding: 0 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem 0 0 0.375rem;
        min-width: 45px;
        font-size: 1.1rem;
        height: 46px;
    }
    
    /* Titles */
    .form-card-title {
        margin: 0;
        color: var(--gray-700);
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .form-card-body {
        padding: 1.5rem;
        position: relative;
    }
    
    /* Style pour les éléments de choix (dropdown) */
    .choices__list--dropdown .choices__item {
        padding: 0.5rem;
        margin-top: 0.25rem;
    }
    
    .bootstrap-select .dropdown-item,
    .choices__item--choice {
        padding: 0.5rem 1rem;
        font-size: 0.95rem;
        color: var(--gray-700);
        transition: var(--transition);
    }
    
    .bootstrap-select .dropdown-item:hover,
    .bootstrap-select .dropdown-item:focus,
    .choices__item--choice.is-highlighted {
        background-color: var(--primary-light);
        color: var(--primary-dark);
    }
    
    /* Style pour les champs de formulaire focus */
    .form-control:focus, 
    .form-select:focus,
    .choices__inner:focus,
    .bootstrap-select > .dropdown-toggle.bs-placeholder:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        outline: 0;
    }
    
    /* Bouton transparent style "glass" */
    .btn-glass {
        background-color: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--primary);
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        transition: var(--transition);
        font-weight: 500;
    }

    .btn-glass:hover {
        background-color: rgba(255, 255, 255, 0.9);
        border-color: var(--primary-light);
        color: var(--primary-dark);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    /* Style pour la section actuelle */
    .nav-link {
        color: var(--gray-600);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        transition: var(--transition);
        margin-bottom: 0.5rem;
    }

    .nav-link i {
        margin-right: 0.5rem;
        font-size: 1.2rem;
    }

    .nav-link.active {
        background-color: var(--primary-light);
        color: var(--primary);
        box-shadow: 0 2px 5px rgba(67, 97, 238, 0.15);
    }

    .nav-link:hover:not(.active) {
        background-color: var(--gray-100);
        color: var(--gray-800);
    }

    /* Animation pour les cards */
    .form-card {
        animation: fadeInUp 0.6s both;
        margin-bottom: 2rem;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--gray-200);
    }

    /* Styles d'en-tête */
    .page-header {
        padding: 1.5rem 2rem;
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
        animation: fadeInDown 0.5s both;
        border: 1px solid var(--gray-200);
    }

    .page-title {
        color: var(--primary-dark);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .page-subtitle {
        color: var(--gray-600);
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .breadcrumb {
        margin-bottom: 0;
        padding: 0;
        background: transparent;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-content {
            padding: 1rem;
        }

        .page-header {
            padding: 1rem;
        }

        .form-card-body {
            padding: 1rem;
        }
    }
    </style>
@endsection

<div class="page-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-header animate__animated animate__fadeInDown">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="page-title"><i class='bx bxs-car me-2'></i> Modifier un Véhicule</h1>
                            <p class="page-subtitle">Modifiez les informations du véhicule dans le formulaire ci-dessous</p>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class='bx bx-home-alt me-1'></i>Accueil</a></li>
                                    <li class="breadcrumb-item"><a href="{{ route('liste.engin') }}">Véhicules</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">Modifier</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="card form-card animate__animated animate__fadeInUp">
                            <form action="{{ route('modifier.engin') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="id" value="{{ $vehicule->id }}">

                                <div class="card-body">
                                    <div class="form-card-header d-flex justify-content-between align-items-center p-3 border-bottom">
                                        <h3 class="form-card-title m-0"><i class='bx bx-edit me-2'></i>Informations du Véhicule</h3>
                                    </div>
                                    
                                    <div class="form-card-body">
                                        <div class="row g-3">
                                            <!-- Informations générales -->
                                            <div class="col-md-6">
                                                <label for="direction" class="form-label">
                                                    <i class='bx bx-building me-2'></i>Direction/Structure
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class='bx bx-buildings'></i></span>
                                                    <select class="form-select" id="direction" name="direction">
                                                        <option value="">Sélectionner...</option>
                                                        @foreach($directions as $direction)
                                                            <option value="{{ $direction->id }}" {{ $vehicule->Direction_id == $direction->id ? 'selected' : '' }}>
                                                                {{ $direction->nom_direction }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="immatriculation" class="form-label">
                                                    <i class='bx bx-id-card me-2'></i>Immatriculation
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class='bx bx-car'></i></span>
                                                    <input type="text" class="form-control" id="immatriculation" name="immatriculation" value="{{ $vehicule->immatriculation }}" placeholder="Entrez l'immatriculation">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="genre" class="form-label">
                                                    <i class='bx bx-category me-2'></i>Genre
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class='bx bx-category-alt'></i></span>
                                                    <input type="text" class="form-control" id="genre" name="genre" value="{{ $vehicule->genre }}" placeholder="Entrez le genre du véhicule">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="marque" class="form-label">
                                                    <i class='bx bx-badge me-2'></i>Marque
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class='bx bx-collection'></i></span>
                                                    <input type="text" class="form-control" id="marque" name="marque" value="{{ $vehicule->marque }}" placeholder="Entrez la marque">
                                                </div>
                                            </div>
                                            
                                            <!-- Caractéristiques techniques -->
                                            <div class="col-md-6">
                                                <label for="type" class="form-label">
                                                    <i class='bx bx-car me-2'></i>Type
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class='bx bx-detail'></i></span>
                                                    <input type="text" class="form-control" id="type" name="type" value="{{ $vehicule->type }}" placeholder="Entrez le type">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="puissance" class="form-label">
                                                    <i class='bx bx-bolt me-2'></i>Puissance
                                                </label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class='bx bx-power-off'></i></span>
                                                    <input type="text" class="form-control" id="puissance" name="puissance" value="{{ $vehicule->puissance }}" placeholder="Entrez la puissance">
                                                </div>
                                            </div>
                                            
                                            <!-- Image du véhicule -->
                                            <div class="col-md-12 mt-4">
                                                <label for="image" class="form-label mb-3">
                                                    <i class='bx bx-image-add me-2'></i>Photo du Véhicule
                                                </label>
                                                
                                                <div class="row">
                                                    <!-- Affichage de l'image actuelle -->
                                                    <div class="col-md-6 mb-3">
                                                        <div class="card">
                                                            <div class="card-header">
                                                                <h5 class="card-title"><i class='bx bx-image me-2'></i>Image Actuelle</h5>
                                                            </div>
                                                            <div class="card-body text-center">
                                                                @if($vehicule->photo && file_exists(public_path('upload/vehicule_images/'.$vehicule->photo)))
                                                                    <img src="{{ asset('upload/vehicule_images/'.$vehicule->photo) }}" 
                                                                         alt="Photo du véhicule" 
                                                                         class="img-fluid rounded" 
                                                                         style="max-height: 200px;">
                                                                    <p class="mt-2 mb-0 text-muted"><small>{{ $vehicule->photo }}</small></p>
                                                                @else
                                                                    <div class="alert alert-info mb-0">
                                                                        <i class='bx bx-info-circle me-2'></i>Aucune image disponible
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- Upload d'une nouvelle image -->
                                                    <div class="col-md-6">
                                                        <div class="card">
                                                            <div class="card-header">
                                                                <h5 class="card-title"><i class='bx bx-upload me-2'></i>Télécharger une nouvelle image</h5>
                                                            </div>
                                                            <div class="card-body">
                                                                <input type="file" class="form-control" id="image" name="photo" accept="image/*" onchange="previewImage(this)">
                                                                <div id="imageDetails" class="mt-3" style="display: none;">
                                                                    <p class="mb-1"><i class='bx bx-file'></i> Nom: <span id="fileName"></span></p>
                                                                    <p class="mb-1"><i class='bx bx-ruler'></i> Taille: <span id="fileSize"></span></p>
                                                                    <p class="mb-1"><i class='bx bx-file-blank'></i> Type: <span id="fileType"></span></p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Observation -->
                                            <div class="col-md-12 mt-3">
                                                <label for="observation" class="form-label">
                                                    <i class='bx bx-note me-2'></i>Observation
                                                </label>
                                                <textarea class="form-control" id="observation" name="observation" rows="4" placeholder="Entrez vos observations...">{{ $vehicule->observation }}</textarea>
                                            </div>
                                            
                                            <div class="col-12 mt-4 text-center">
                                                <button type="submit" class="btn btn-primary px-5 py-2">
                                                    <i class='bx bx-save me-2'></i>Enregistrer les Modifications
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Attendre que le DOM soit complètement chargé
window.addEventListener('load', function() {
    // Initialiser tous les select avec Choices.js
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        new Choices(select, {
            searchEnabled: true,
            itemSelectText: '',
            shouldSort: false,
            position: 'bottom'
        });
    });
});

// Prévisualisation de l'image
function previewImage(input) {
    const imageDetails = document.getElementById('imageDetails');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileType = document.getElementById('fileType');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        
        // Afficher les détails du fichier
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileType.textContent = file.type;
        
        imageDetails.style.display = 'block';
    } else {
        imageDetails.style.display = 'none';
    }
}

// Formater la taille du fichier
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

@endsection

<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Unite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UniteController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $unites = Unite::orderBy('libelle', 'asc')->get();
        return view('comptabilite-matiere.unites.index', compact('unites'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('comptabilite-matiere.unites.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:10|unique:unites',
            'libelle' => 'required|string|max:100|unique:unites',
        ]);

        if ($validator->fails()) {
            return redirect()->route('unites.create')
                ->withErrors($validator)
                ->withInput();
        }

        Unite::create([
            'code' => $request->code,
            'libelle' => $request->libelle,
        ]);

        return redirect()->route('unites.index')
            ->with('success', 'Unité de mesure ajoutée avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $unite = Unite::findOrFail($id);
        return view('comptabilite-matiere.unites.show', compact('unite'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $unite = Unite::findOrFail($id);
        return view('comptabilite-matiere.unites.edit', compact('unite'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $unite = Unite::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:10|unique:unites,code,' . $id,
            'libelle' => 'required|string|max:100|unique:unites,libelle,' . $id,
        ]);

        if ($validator->fails()) {
            return redirect()->route('unites.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }

        $unite->update([
            'code' => $request->code,
            'libelle' => $request->libelle,
        ]);

        return redirect()->route('unites.index')
            ->with('success', 'Unité de mesure mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $unite = Unite::findOrFail($id);
        
        // Vérifier si l'unité est utilisée par des articles
        if ($unite->articles()->count() > 0) {
            return redirect()->route('unites.index')
                ->with('error', 'Impossible de supprimer cette unité car elle est utilisée par des articles.');
        }
        
        $unite->delete();

        return redirect()->route('unites.index')
            ->with('success', 'Unité de mesure supprimée avec succès.');
    }
}

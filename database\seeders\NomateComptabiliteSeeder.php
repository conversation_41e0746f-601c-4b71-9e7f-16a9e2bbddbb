<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\IOFactory;

class NomateComptabiliteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Chemin vers le fichier Excel NOMATE.xlsx
        $filePath = public_path('backend/assets/NOMATE.xlsx');
        
        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            $this->command->error('Le fichier NOMATE.xlsx n\'existe pas à l\'emplacement spécifié.');
            return;
        }
        
        try {
            // Charger le fichier Excel
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            $this->command->info('Importation des données de comptabilité matière en cours...');
            
            $count = 0;
            $errors = 0;
            
            // Ignorer les deux premières lignes (vides ou en-têtes)
            $startRow = 2;
            
            // Parcourir les lignes du fichier Excel
            for ($i = $startRow; $i < count($rows); $i++) {
                $row = $rows[$i];
                
                // Vérifier si la ligne contient des données
                if (empty($row[0]) && empty($row[1])) continue;
                
                // Extraire les données de la ligne
                $code = trim($row[0] ?? '');
                $intitule = trim($row[1] ?? '');
                
                // Déterminer si c'est un en-tête de section
                $is_header = false;
                
                // Vérifier si c'est un en-tête (texte en majuscules et/ou contient 'PARTIE' ou 'LISTE')
                if (strtoupper($intitule) === $intitule && !empty($intitule)) {
                    $is_header = true;
                }
                
                // Vérifier que nous avons au moins le code ou l'intitulé
                if (empty($code) && empty($intitule)) {
                    continue;
                }
                
                // Vérifier si l'enregistrement existe déjà pour éviter les doublons
                $exists = DB::table('nomate')
                    ->where('code', $code)
                    ->where('intitule', $intitule)
                    ->exists();
                
                if (!$exists) {
                    // Insérer dans la table nomate
                    try {
                        DB::table('nomate')->insert([
                            'code' => $code,
                            'intitule' => $intitule,
                            'is_header' => $is_header,
                            'statut' => 'actif',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                        $count++;
                    } catch (\Exception $e) {
                        $this->command->error('Erreur lors de l\'insertion du compte: ' . $code . ' - ' . $intitule . ' - ' . $e->getMessage());
                        $errors++;
                    }
                }
            }
            
            $this->command->info('Importation terminée. ' . $count . ' comptes importés avec succès. ' . $errors . ' erreurs.');
            
        } catch (\Exception $e) {
            $this->command->error('Une erreur est survenue lors de la lecture du fichier Excel: ' . $e->getMessage());
        }
    }
}

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Grille Véhicules</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link href="css/vehicule-liste.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid p-4">
        <h2>Test de la Grille des Véhicules</h2>
        
        <!-- Contrôles de vue -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4>Test de la disposition en grille</h4>
            </div>
            
            <div class="d-flex align-items-center gap-3">
                <!-- Options de tri pour la grille -->
                <div class="grid-sort-options" id="gridSortOptions">
                    <label class="form-label mb-0 me-2">Trier par :</label>
                    <select class="form-select form-select-sm" id="gridSortSelect" style="width: auto; min-width: 150px;">
                        <option value="immatriculation">Immatriculation</option>
                        <option value="marque">Marque</option>
                        <option value="etat">État</option>
                    </select>
                    <button type="button" class="btn btn-sm btn-outline-secondary ms-1" id="sortOrderBtn">
                        <i class="bx bx-sort-up" id="sortOrderIcon"></i>
                    </button>
                </div>
                
                <!-- Switch vue tableau/grille -->
                <div class="view-switch">
                    <span class="view-switch-label">Vue :</span>
                    <div class="view-switch-buttons">
                        <button type="button" class="view-switch-btn" data-view="table">
                            <i class="bx bx-table"></i>
                        </button>
                        <button type="button" class="view-switch-btn active" data-view="grid">
                            <i class="bx bx-grid-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Vue en grille -->
        <div class="grid-view" id="gridView">
            <!-- Carte véhicule 1 -->
            <div class="vehicle-card" data-etat="bon" data-marque="Toyota" data-immatriculation="ABC-123">
                <div class="vehicle-card-img">
                    <img src="https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=Véhicule+1" alt="Véhicule 1">
                    <span class="vehicle-card-status good">Bon état</span>
                </div>
                <div class="vehicle-card-body">
                    <h5 class="vehicle-card-title">ABC-123-TG</h5>
                    <p class="vehicle-card-subtitle">Toyota Camry</p>
                    
                    <div class="vehicle-card-info">
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Acquisition</span>
                            <span class="vehicle-card-info-value">15/03/2020</span>
                        </div>
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Valeur</span>
                            <span class="vehicle-card-info-value">15 000 000 F</span>
                        </div>
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Genre</span>
                            <span class="vehicle-card-info-value">Voiture</span>
                        </div>
                    </div>
                </div>
                <div class="vehicle-card-footer">
                    <div class="vehicle-card-department">DAF</div>
                    <div class="vehicle-card-actions">
                        <a href="#" class="btn-action view">
                            <i class="bx bx-info-circle"></i>
                        </a>
                        <a href="#" class="btn-action edit">
                            <i class="bx bx-edit"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Carte véhicule 2 -->
            <div class="vehicle-card" data-etat="passable" data-marque="Honda" data-immatriculation="DEF-456">
                <div class="vehicle-card-img">
                    <img src="https://via.placeholder.com/300x200/FF9800/FFFFFF?text=Véhicule+2" alt="Véhicule 2">
                    <span class="vehicle-card-status fair">État passable</span>
                </div>
                <div class="vehicle-card-body">
                    <h5 class="vehicle-card-title">DEF-456-TG</h5>
                    <p class="vehicle-card-subtitle">Honda Civic</p>
                    
                    <div class="vehicle-card-info">
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Acquisition</span>
                            <span class="vehicle-card-info-value">22/08/2018</span>
                        </div>
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Valeur</span>
                            <span class="vehicle-card-info-value">12 500 000 F</span>
                        </div>
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Genre</span>
                            <span class="vehicle-card-info-value">Voiture</span>
                        </div>
                    </div>
                </div>
                <div class="vehicle-card-footer">
                    <div class="vehicle-card-department">DAEMA</div>
                    <div class="vehicle-card-actions">
                        <a href="#" class="btn-action view">
                            <i class="bx bx-info-circle"></i>
                        </a>
                        <a href="#" class="btn-action edit">
                            <i class="bx bx-edit"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Carte véhicule 3 -->
            <div class="vehicle-card" data-etat="panne" data-marque="Nissan" data-immatriculation="GHI-789">
                <div class="vehicle-card-img">
                    <img src="https://via.placeholder.com/300x200/F44336/FFFFFF?text=Véhicule+3" alt="Véhicule 3">
                    <span class="vehicle-card-status warning">En panne</span>
                </div>
                <div class="vehicle-card-body">
                    <h5 class="vehicle-card-title">GHI-789-TG</h5>
                    <p class="vehicle-card-subtitle">Nissan Sentra</p>
                    
                    <div class="vehicle-card-info">
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Acquisition</span>
                            <span class="vehicle-card-info-value">10/12/2019</span>
                        </div>
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Valeur</span>
                            <span class="vehicle-card-info-value">8 750 000 F</span>
                        </div>
                        <div class="vehicle-card-info-item">
                            <span class="vehicle-card-info-label">Genre</span>
                            <span class="vehicle-card-info-value">Voiture</span>
                        </div>
                    </div>
                </div>
                <div class="vehicle-card-footer">
                    <div class="vehicle-card-department">DFRT</div>
                    <div class="vehicle-card-actions">
                        <a href="#" class="btn-action view">
                            <i class="bx bx-info-circle"></i>
                        </a>
                        <a href="#" class="btn-action edit">
                            <i class="bx bx-edit"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Vue tableau (masquée) -->
        <div class="d-none" id="tableView">
            <p>Vue tableau (non implémentée dans ce test)</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Test de la grille initialisé');
            
            // Gestion du changement de vue
            $('.view-switch-btn').on('click', function() {
                var viewType = $(this).data('view');
                console.log('Changement de vue vers:', viewType);
                
                $('.view-switch-btn').removeClass('active');
                $(this).addClass('active');
                
                if (viewType === 'table') {
                    $('#gridView').addClass('d-none');
                    $('#tableView').removeClass('d-none');
                    $('#gridSortOptions').hide();
                } else {
                    $('#tableView').addClass('d-none');
                    $('#gridView').removeClass('d-none');
                    $('#gridSortOptions').show();
                }
            });
            
            // Test du tri
            let sortOrder = 'asc';
            
            $('#sortOrderBtn').on('click', function() {
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
                $('#sortOrderIcon').removeClass('bx-sort-up bx-sort-down')
                    .addClass(sortOrder === 'asc' ? 'bx-sort-up' : 'bx-sort-down');
                console.log('Ordre de tri changé:', sortOrder);
            });
            
            console.log('Grille de test prête');
        });
    </script>
</body>
</html>

<!-- Fenêtre modale ultra-moderne pour les détails du véhicule -->
<div class="modal vehicle-details-modal fade" id="detailsModal{{ $item->id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Structure en deux colonnes principales -->
            <div class="row g-0">
                <!-- Colonne de gauche avec image et informations visuelles -->
                <div class="col-lg-5 vehicle-image-column">
                    <!-- Bouton de fermeture -->
                    <button type="button" class="btn-close-custom" data-bs-dismiss="modal" aria-label="Close">
                        <i class='bx bx-x'></i>
                    </button>
                    
                    <!-- Image du véhicule avec overlay -->
                    <div class="vehicle-image-container">
                        <img src="{{ asset($item->image) }}" alt="{{ $item->marque }}" class="vehicle-image">
                        <div class="vehicle-image-overlay">
                            <div class="vehicle-brand-badge">
                                <i class='bx bxs-car'></i>
                                <span>{{ $item->marque }}</span>
                            </div>
                            
                            <div class="vehicle-status-container">
                                @if($item->etat == 'Bon')
                                    <div class="vehicle-status good">
                                        <i class='bx bxs-check-circle'></i>
                                        <span>Bon état</span>
                                    </div>
                                @elseif($item->etat == 'Moyen')
                                    <div class="vehicle-status fair">
                                        <i class='bx bxs-info-circle'></i>
                                        <span>État moyen</span>
                                    </div>
                                @else
                                    <div class="vehicle-status bad">
                                        <i class='bx bxs-error-circle'></i>
                                        <span>Hors service</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <!-- Informations clés en bas de l'image -->
                    <div class="vehicle-key-info">
                        <div class="vehicle-plate">
                            <span class="plate-label">Immatriculation</span>
                            <span class="plate-number">{{ $item->immatriculation }}</span>
                        </div>
                        
                        <div class="vehicle-acquisition">
                            <div class="acquisition-price">
                                <i class='bx bx-money'></i>
                                <span>{{ number_format($item->valeur_acquisition, 0, ',', ' ') }} FCFA</span>
                            </div>
                            <div class="acquisition-date">
                                <i class='bx bx-calendar'></i>
                                <span>{{ $item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Colonne de droite avec les détails et informations -->
                <div class="col-lg-7 vehicle-details-column">
                    <div class="vehicle-details-header">
                        <h3 class="vehicle-title">{{ $item->marque }} {{ $item->type }}</h3>
                        <p class="vehicle-subtitle">{{ $item->genre }}</p>
                    </div>
                    
                    <div class="vehicle-details-content">
                        <!-- Onglets pour organiser les informations -->
                        <ul class="nav nav-tabs vehicle-tabs" id="vehicleTab{{ $item->id }}" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="specs-tab{{ $item->id }}" data-bs-toggle="tab" data-bs-target="#specs{{ $item->id }}" type="button" role="tab" aria-selected="true">
                                    <i class='bx bx-info-circle'></i> Spécifications
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="usage-tab{{ $item->id }}" data-bs-toggle="tab" data-bs-target="#usage{{ $item->id }}" type="button" role="tab" aria-selected="false">
                                    <i class='bx bx-user'></i> Utilisation
                                </button>
                            </li>
                            @if($item->observation)
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="notes-tab{{ $item->id }}" data-bs-toggle="tab" data-bs-target="#notes{{ $item->id }}" type="button" role="tab" aria-selected="false">
                                    <i class='bx bx-message-detail'></i> Notes
                                </button>
                            </li>
                            @endif
                        </ul>
                        
                        <!-- Contenu des onglets -->
                        <div class="tab-content vehicle-tab-content" id="vehicleTabContent{{ $item->id }}">
                            <!-- Onglet Spécifications -->
                            <div class="tab-pane fade show active" id="specs{{ $item->id }}" role="tabpanel" aria-labelledby="specs-tab{{ $item->id }}">
                                <div class="specs-grid">
                                    <div class="specs-card">
                                        <div class="specs-icon">
                                            <i class='bx bx-car'></i>
                                        </div>
                                        <div class="specs-content">
                                            <span class="specs-label">Type</span>
                                            <span class="specs-value">{{ $item->type }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="specs-card">
                                        <div class="specs-icon">
                                            <i class='bx bx-category'></i>
                                        </div>
                                        <div class="specs-content">
                                            <span class="specs-label">Genre</span>
                                            <span class="specs-value">{{ $item->genre }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="specs-card">
                                        <div class="specs-icon">
                                            <i class='bx bx-tachometer'></i>
                                        </div>
                                        <div class="specs-content">
                                            <span class="specs-label">Puissance</span>
                                            <span class="specs-value">{{ $item->puissance }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="specs-card">
                                        <div class="specs-icon">
                                            <i class='bx bx-package'></i>
                                        </div>
                                        <div class="specs-content">
                                            <span class="specs-label">PTC</span>
                                            <span class="specs-value">{{ $item->ptc }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Onglet Utilisation -->
                            <div class="tab-pane fade" id="usage{{ $item->id }}" role="tabpanel" aria-labelledby="usage-tab{{ $item->id }}">
                                <div class="department-card">
                                    <div class="department-icon">
                                        <i class='bx bxs-building'></i>
                                    </div>
                                    <div class="department-content">
                                        <span class="department-label">Département</span>
                                        <span class="department-name">{{ $item->departement->nom_departement ?? 'Non assigné' }}</span>
                                    </div>
                                </div>
                                
                                <div class="usage-grid">
                                    <div class="usage-card">
                                        <span class="usage-label">Utilisateur</span>
                                        <span class="usage-value">{{ $item->utilisateur ?? 'Non assigné' }}</span>
                                    </div>
                                    
                                    <div class="usage-card">
                                        <span class="usage-label">Service</span>
                                        <span class="usage-value">{{ $item->service_utilisation ?? 'Non spécifié' }}</span>
                                    </div>
                                    
                                    <div class="usage-card">
                                        <span class="usage-label">Usage</span>
                                        <span class="usage-value">{{ $item->usage }}</span>
                                    </div>
                                    
                                    <div class="usage-card">
                                        <span class="usage-label">Date d'affectation</span>
                                        <span class="usage-value">{{ $item->date_affectation ? date('d/m/Y', strtotime($item->date_affectation)) : 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Onglet Notes (si des observations existent) -->
                            @if($item->observation)
                            <div class="tab-pane fade" id="notes{{ $item->id }}" role="tabpanel" aria-labelledby="notes-tab{{ $item->id }}">
                                <div class="notes-container">
                                    <div class="notes-icon">
                                        <i class='bx bxs-note'></i>
                                    </div>
                                    <div class="notes-content">
                                        <p>{{ $item->observation }}</p>
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    <!-- Pied de page avec actions -->
                    <div class="vehicle-details-footer">
                        <div class="vehicle-timestamp">
                            <i class='bx bx-time'></i>
                            <span>Mis à jour le {{ $item->updated_at ? date('d/m/Y', strtotime($item->updated_at)) : date('d/m/Y', strtotime($item->created_at)) }}</span>
                        </div>
                        
                        <div class="vehicle-actions">
                            <button type="button" class="btn-close-modal" data-bs-dismiss="modal">
                                <i class='bx bx-x'></i>
                                <span>Fermer</span>
                            </button>
                            
                            <a href="{{ route('editer.engin', $item->id) }}" class="btn-edit-vehicle">
                                <i class='bx bx-edit'></i>
                                <span>Modifier</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Styles pour la nouvelle fenêtre modale ultra-moderne */
.vehicle-details-modal .modal-dialog {
    max-width: 1000px;
}

.vehicle-details-modal .modal-content {
    border: none;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* Colonne de gauche avec image */
.vehicle-image-column {
    position: relative;
    background: #000;
    min-height: 600px;
}

.btn-close-custom {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 10;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.btn-close-custom:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.vehicle-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.vehicle-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: all 0.5s ease;
}

.vehicle-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
}

.vehicle-brand-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    padding: 8px 15px;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    margin-top: 50px;
    align-self: flex-start;
}

.vehicle-brand-badge i {
    margin-right: 8px;
    font-size: 18px;
}

.vehicle-status-container {
    align-self: flex-end;
}

.vehicle-status {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 14px;
}

.vehicle-status.good {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
    color: white;
}

.vehicle-status.fair {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.vehicle-status.bad {
    background: linear-gradient(135deg, #F44336, #C62828);
    color: white;
}

.vehicle-status i {
    margin-right: 8px;
    font-size: 16px;
}

.vehicle-key-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.vehicle-plate {
    margin-bottom: 15px;
}

.plate-label {
    display: block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 5px;
}

.plate-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 1px;
}

.vehicle-acquisition {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.acquisition-price, .acquisition-date {
    display: flex;
    align-items: center;
}

.acquisition-price i, .acquisition-date i {
    margin-right: 8px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
}

/* Colonne de droite avec détails */
.vehicle-details-column {
    background: #f8f9fa;
    padding: 0;
}

.vehicle-details-header {
    padding: 30px 30px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.vehicle-title {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    color: #333;
}

.vehicle-subtitle {
    font-size: 16px;
    color: #666;
    margin: 5px 0 0;
}

.vehicle-details-content {
    padding: 20px 30px;
}

/* Onglets stylisés */
.vehicle-tabs {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.vehicle-tabs .nav-link {
    border: none;
    color: #666;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.vehicle-tabs .nav-link i {
    margin-right: 8px;
}

.vehicle-tabs .nav-link.active {
    color: #4361ee;
    background: transparent;
}

.vehicle-tabs .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background: #4361ee;
    border-radius: 3px 3px 0 0;
}

.vehicle-tab-content {
    padding: 20px 0;
}

/* Grille de spécifications */
.specs-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.specs-card {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.specs-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.specs-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: #e7f0ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: #4361ee;
}

.specs-content {
    flex: 1;
}

.specs-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.specs-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Carte département */
.department-card {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.department-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #4361ee, #3a0ca3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 30px;
    color: white;
}

.department-content {
    flex: 1;
}

.department-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.department-name {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

/* Grille d'utilisation */
.usage-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.usage-card {
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.usage-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.usage-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Notes */
.notes-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
}

.notes-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: #fff5e5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 24px;
    color: #ff9800;
    align-self: flex-start;
}

.notes-content {
    flex: 1;
}

.notes-content p {
    margin: 0;
    line-height: 1.6;
    color: #333;
}

/* Pied de page */
.vehicle-details-footer {
    padding: 20px 30px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vehicle-timestamp {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;
}

.vehicle-timestamp i {
    margin-right: 8px;
    color: #999;
}

.vehicle-actions {
    display: flex;
    gap: 10px;
}

.btn-close-modal, .btn-edit-vehicle {
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-close-modal {
    background: #f1f3f5;
    color: #495057;
}

.btn-close-modal:hover {
    background: #e9ecef;
}

.btn-edit-vehicle {
    background: linear-gradient(135deg, #4361ee, #3a0ca3);
    color: white;
    text-decoration: none;
}

.btn-edit-vehicle:hover {
    background: linear-gradient(135deg, #3a56d4, #2f0b82);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
    color: white;
}

.btn-close-modal i, .btn-edit-vehicle i {
    margin-right: 8px;
    font-size: 16px;
}

/* Animation d'entrée pour la modale */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out, opacity 0.3s ease;
    transform: scale(0.95);
    opacity: 0;
}

.modal.fade.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
}

/* Responsive */
@media (max-width: 991.98px) {
    .vehicle-image-column {
        min-height: 400px;
    }
    
    .specs-grid, .usage-grid {
        grid-template-columns: 1fr;
    }
}
</style>

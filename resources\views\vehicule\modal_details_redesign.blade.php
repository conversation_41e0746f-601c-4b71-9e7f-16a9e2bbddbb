<!-- Fenêtre modale améliorée pour les détails du véhicule -->
<div class="modal fade vehicle-modal" id="detailsModal{{ $item->id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <!-- En-tête avec dégradé et image de fond -->
            <div class="modal-header position-relative p-0 border-0">
                <div class="vehicle-modal-header w-100 p-4" style="background: linear-gradient(135deg, #4361ee, #3a0ca3); border-radius: 0.5rem 0.5rem 0 0; min-height: 100px;">
                    <div class="d-flex justify-content-between align-items-center position-relative" style="z-index: 2;">
                        <h5 class="modal-title text-white mb-0 d-flex align-items-center">
                            <i class="bx bxs-car me-2" style="font-size: 1.5rem;"></i>
                            <span>
                                <span class="d-block" style="font-size: 1.5rem; font-weight: 700;">{{ $item->marque }} {{ $item->type }}</span>
                                <span class="d-block text-white-50" style="font-size: 1rem;">{{ $item->immatriculation }}</span>
                            </span>
                        </h5>
                        <div>
                            @if($item->etat == 'Bon')
                                <span class="badge bg-success px-3 py-2 rounded-pill"><i class="bx bx-check-circle me-1"></i> Bon état</span>
                            @elseif($item->etat == 'Moyen')
                                <span class="badge bg-warning px-3 py-2 rounded-pill"><i class="bx bx-info-circle me-1"></i> État moyen</span>
                            @else
                                <span class="badge bg-danger px-3 py-2 rounded-pill"><i class="bx bx-error-circle me-1"></i> Hors service</span>
                            @endif
                        </div>
                    </div>
                    <!-- Motif décoratif en arrière-plan -->
                    <div class="position-absolute top-0 end-0 bottom-0 start-0" style="background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTI4MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48cGF0aCBkPSJNMTI4MCAwTDY0MCAxNDBMMCAweiIvPjwvZz48L3N2Zz4='); background-size: 100% 100%; opacity: 0.3; z-index: 1; border-radius: 0.5rem 0.5rem 0 0;"></div>
                </div>
                <button type="button" class="btn-close btn-close-white position-absolute top-0 end-0 m-3" data-bs-dismiss="modal" aria-label="Close" style="z-index: 3;"></button>
            </div>
            
            <div class="modal-body p-0">
                <div class="row g-0">
                    <!-- Colonne de gauche avec image et informations générales -->
                    <div class="col-md-5 border-end">
                        <div class="vehicle-img-container position-relative" style="height: 250px; overflow: hidden; background-color: #f8f9fa;">
                            <img src="{{ asset($item->image) }}" alt="{{ $item->marque }}" class="img-fluid w-100 h-100 object-fit-cover">
                            <div class="position-absolute bottom-0 start-0 end-0 p-2" style="background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-white"><i class="bx bx-calendar me-1"></i> Acquisition: {{ $item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A' }}</span>
                                    <span class="text-white"><i class="bx bx-purchase-tag me-1"></i> {{ number_format($item->valeur_acquisition, 0, ',', ' ') }} F</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <div class="mb-4">
                                <h6 class="text-primary border-bottom pb-2 d-flex align-items-center">
                                    <i class="bx bx-info-circle me-2"></i>Informations générales
                                </h6>
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Immatriculation</span>
                                            <span class="info-value fw-bold">{{ $item->immatriculation }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Genre</span>
                                            <span class="info-value fw-bold">{{ $item->genre }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Marque</span>
                                            <span class="info-value fw-bold">{{ $item->marque }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Type</span>
                                            <span class="info-value fw-bold">{{ $item->type }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h6 class="text-primary border-bottom pb-2 d-flex align-items-center">
                                    <i class="bx bx-cog me-2"></i>Caractéristiques techniques
                                </h6>
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">PTC</span>
                                            <span class="info-value fw-bold">{{ $item->ptc }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Puissance</span>
                                            <span class="info-value fw-bold">{{ $item->puissance }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Colonne de droite avec utilisation et autres informations -->
                    <div class="col-md-7">
                        <div class="p-4">
                            <div class="mb-4">
                                <h6 class="text-primary border-bottom pb-2 d-flex align-items-center">
                                    <i class="bx bx-building me-2"></i>Affectation et utilisation
                                </h6>
                                <div class="department-card mb-3 p-3 rounded" style="background-color: #f8f9fa; border-left: 4px solid #4361ee;">
                                    <div class="d-flex align-items-center">
                                        <div class="department-icon me-3 d-flex align-items-center justify-content-center rounded-circle" style="width: 50px; height: 50px; background-color: #e7eaff;">
                                            <i class="bx bx-buildings text-primary" style="font-size: 1.5rem;"></i>
                                        </div>
                                        <div>
                                            <span class="text-muted d-block">Département</span>
                                            <span class="fw-bold fs-5">{{ $item->departement->nom_departement ?? 'Non assigné' }}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Utilisateur</span>
                                            <span class="info-value fw-bold">{{ $item->utilisateur ?? 'Non assigné' }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Service</span>
                                            <span class="info-value fw-bold">{{ $item->service_utilisation ?? 'Non spécifié' }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Usage</span>
                                            <span class="info-value fw-bold">{{ $item->usage }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-item-card">
                                            <span class="info-label text-muted d-block">Date d'affectation</span>
                                            <span class="info-value fw-bold">{{ $item->date_affectation ? date('d/m/Y', strtotime($item->date_affectation)) : 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            @if($item->observation)
                            <div>
                                <h6 class="text-primary border-bottom pb-2 d-flex align-items-center">
                                    <i class="bx bx-message-detail me-2"></i>Observations
                                </h6>
                                <div class="observation-card p-3 rounded" style="background-color: #f8f9fa; border-left: 4px solid #f72585;">
                                    <p class="mb-0">{{ $item->observation }}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer border-0 bg-light">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <span class="text-muted small">Dernière mise à jour: {{ $item->updated_at ? date('d/m/Y à H:i', strtotime($item->updated_at)) : date('d/m/Y à H:i', strtotime($item->created_at)) }}</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="bx bx-x me-1"></i>Fermer
                        </button>
                        <a href="{{ route('editer.engin', $item->id) }}" class="btn btn-primary ms-2">
                            <i class="bx bx-edit me-1"></i>Modifier
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Styles pour la fenêtre modale améliorée */
.vehicle-modal .modal-content {
    overflow: hidden;
    border-radius: 0.5rem;
}

.vehicle-modal .info-item-card {
    margin-bottom: 0.5rem;
}

.vehicle-modal .info-label {
    font-size: 0.75rem;
    color: #6c757d;
}

.vehicle-modal .info-value {
    font-size: 0.95rem;
}

.vehicle-modal .modal-header .badge {
    font-weight: 500;
    font-size: 0.85rem;
}

.vehicle-modal .object-fit-cover {
    object-fit: cover;
    object-position: center;
}

/* Animation d'entrée pour la modale */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

.modal.fade.show .modal-dialog {
    transform: scale(1);
}

.modal.fade:not(.show) .modal-dialog {
    transform: scale(0.9);
}

/* Styles pour les badges d'état */
.badge.bg-success {
    background-color: #4caf50 !important;
}

.badge.bg-warning {
    background-color: #ff9800 !important;
    color: #212529;
}

.badge.bg-danger {
    background-color: #f44336 !important;
}

/* Effet de survol sur les cartes d'information */
.department-card:hover, .observation-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}
</style>

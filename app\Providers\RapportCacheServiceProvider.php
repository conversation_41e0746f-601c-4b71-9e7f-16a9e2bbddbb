<?php

namespace App\Providers;

use App\Services\RapportCacheService;
use Illuminate\Support\ServiceProvider;

class RapportCacheServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(RapportCacheService::class, function ($app) {
            return new RapportCacheService();
        });

        // Alias pour faciliter l'accès au service
        $this->app->alias(RapportCacheService::class, 'rapport.cache');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

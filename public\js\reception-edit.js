// Script pour la page d'édition des réceptions
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation de Choices.js pour les sélecteurs
    const configChoices = {
        removeItemButton: true,
        searchEnabled: true,
        searchPlaceholderValue: 'Rechercher...',
        noResultsText: 'Aucun résultat trouvé',
        itemSelectText: 'Appuyez pour sélectionner',
        classNames: {
            containerOuter: 'choices',
            containerInner: 'choices__inner',
            input: 'choices__input',
            inputCloned: 'choices__input--cloned',
            list: 'choices__list',
            listItems: 'choices__list--multiple',
            listSingle: 'choices__list--single',
            listDropdown: 'choices__list--dropdown',
            item: 'choices__item',
            itemSelectable: 'choices__item--selectable',
            itemDisabled: 'choices__item--disabled',
            itemOption: 'choices__item--choice',
            group: 'choices__group',
            groupHeading: 'choices__heading',
            button: 'choices__button',
            activeState: 'is-active',
            focusState: 'is-focused',
            openState: 'is-open',
            disabledState: 'is-disabled',
            highlightedState: 'is-highlighted',
            selectedState: 'is-selected',
            flippedState: 'is-flipped',
            loadingState: 'is-loading',
            noResults: 'has-no-results',
            noChoices: 'has-no-choices'
        }
    };

    // Initialiser Choices.js sur le select de direction/structure
    if (document.getElementById('departement_id')) {
        const departementSelect = new Choices('#departement_id', configChoices);
        
        // Ajouter un champ de recherche au-dessus du select
        const departementWrapper = document.querySelector('.departement-wrapper');
        if (departementWrapper) {
            const searchInput = document.createElement('div');
            searchInput.className = 'choices-search mb-2';
            searchInput.innerHTML = `
                <div class="input-group">
                    <span class="input-group-text"><i class='bx bx-search'></i></span>
                    <input type="text" class="form-control" placeholder="Rechercher une direction/structure..." id="departement-search">
                    <button class="btn btn-outline-secondary" type="button" id="clear-departement-search">
                        <i class='bx bx-x'></i>
                    </button>
                </div>
            `;
            departementWrapper.insertBefore(searchInput, departementWrapper.firstChild);
            
            // Ajouter la fonctionnalité de recherche
            const searchField = document.getElementById('departement-search');
            const clearButton = document.getElementById('clear-departement-search');
            
            searchField.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const options = document.querySelectorAll('.choices__item--choice');
                let matchCount = 0;
                
                options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    const match = text.includes(query);
                    
                    if (match) {
                        option.style.display = '';
                        matchCount++;
                        
                        // Mettre en surbrillance les termes de recherche
                        if (query) {
                            const regex = new RegExp(`(${query})`, 'gi');
                            const highlighted = option.textContent.replace(regex, '<mark>$1</mark>');
                            option.innerHTML = highlighted;
                        }
                    } else {
                        option.style.display = 'none';
                    }
                });
                
                // Afficher le nombre de résultats
                const resultsInfo = document.querySelector('.choices-results-info');
                if (resultsInfo) {
                    if (query) {
                        resultsInfo.textContent = `${matchCount} résultat(s) trouvé(s)`;
                        resultsInfo.style.display = 'block';
                    } else {
                        resultsInfo.style.display = 'none';
                    }
                }
                
                // Si un seul résultat, le sélectionner automatiquement
                if (matchCount === 1 && query.length > 2) {
                    const visibleOption = Array.from(options).find(opt => opt.style.display !== 'none');
                    if (visibleOption) {
                        visibleOption.click();
                    }
                }
            });
            
            // Effacer la recherche
            clearButton.addEventListener('click', function() {
                searchField.value = '';
                searchField.dispatchEvent(new Event('input'));
                searchField.focus();
            });
            
            // Ajouter un élément pour afficher le nombre de résultats
            const resultsInfo = document.createElement('div');
            resultsInfo.className = 'choices-results-info small text-muted mt-1';
            resultsInfo.style.display = 'none';
            departementWrapper.insertBefore(resultsInfo, departementWrapper.querySelector('.choices'));
        }
    }

    // Gestion de l'affichage du fichier PV
    const pvInput = document.getElementById('pv_reception');
    const pvCheckbox = document.getElementById('pv_checked');
    const currentPvContainer = document.getElementById('current-pv-container');
    
    if (pvInput && pvCheckbox) {
        // Vérifier si un PV existe déjà
        const hasPv = currentPvContainer && currentPvContainer.dataset.hasPv === 'true';
        
        // Mettre à jour la case à cocher en fonction de l'existence du PV
        pvCheckbox.checked = hasPv;
        
        // Gérer le changement de fichier
        pvInput.addEventListener('change', function() {
            pvCheckbox.checked = this.files.length > 0;
            
            // Afficher le nom du fichier sélectionné
            const fileNameDisplay = document.getElementById('selected-file-name');
            if (fileNameDisplay) {
                if (this.files.length > 0) {
                    fileNameDisplay.textContent = this.files[0].name;
                    fileNameDisplay.parentElement.classList.remove('d-none');
                } else {
                    fileNameDisplay.parentElement.classList.add('d-none');
                }
            }
        });
    }
    
    // Prévisualisation du PV existant
    const previewPvBtn = document.getElementById('preview-pv-btn');
    if (previewPvBtn) {
        previewPvBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const pvUrl = this.getAttribute('href');
            const isPdf = pvUrl.toLowerCase().endsWith('.pdf');
            
            const modal = new bootstrap.Modal(document.getElementById('pvPreviewModal'));
            const modalTitle = document.querySelector('#pvPreviewModal .modal-title');
            const modalBody = document.querySelector('#pvPreviewModal .modal-body');
            
            modalTitle.textContent = 'Prévisualisation du PV de Réception';
            
            if (isPdf) {
                modalBody.innerHTML = `
                    <div class="ratio ratio-16x9">
                        <iframe src="${pvUrl}" allowfullscreen></iframe>
                    </div>
                `;
            } else {
                modalBody.innerHTML = `
                    <div class="text-center">
                        <img src="${pvUrl}" class="img-fluid" alt="PV Preview">
                    </div>
                `;
            }
            
            modal.show();
        });
    }
    
    // Animation d'entrée
    const formSections = document.querySelectorAll('.form-section');
    formSections.forEach((section, index) => {
        section.classList.add('animate-fade-in');
        section.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Validation du formulaire
    if ($.validator) {
        $('#myForm').validate({
            rules: {
                date_enregistrement: {
                    required: true,
                },
                direction_structure: {
                    required: true,
                },
                reference_courier: {
                    required: true,
                },
                reference_marche: {
                    required: true,
                },
                objet: {
                    required: true,
                },
                periode_execution: {
                    required: true,
                },
                executant: {
                    required: true,
                }
            },
            messages: {
                date_enregistrement: {
                    required: 'Veuillez sélectionner une date d\'enregistrement',
                },
                direction_structure: {
                    required: 'Veuillez sélectionner une direction/structure',
                },
                reference_courier: {
                    required: 'Veuillez saisir la référence du courier',
                },
                reference_marche: {
                    required: 'Veuillez saisir la référence du marché',
                },
                objet: {
                    required: 'Veuillez saisir l\'objet',
                },
                periode_execution: {
                    required: 'Veuillez saisir la période d\'exécution',
                },
                executant: {
                    required: 'Veuillez saisir l\'exécutant',
                }
            },
            errorElement: 'span',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid');
            }
        });
    }
});

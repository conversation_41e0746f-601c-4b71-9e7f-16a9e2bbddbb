<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mutation extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'immobilisation_id',
        'service_origine_id',
        'service_destination_id',
        'date_mutation',
        'reference_doc',
        'observations',
        'user_id'
    ];
    
    protected $casts = [
        'date_mutation' => 'date',
    ];
    
    public function immobilisation()
    {
        return $this->belongsTo(Immobilisation::class);
    }
    
    public function serviceOrigine()
    {
        return $this->belongsTo(Departement::class, 'service_origine_id');
    }
    
    public function serviceDestination()
    {
        return $this->belongsTo(Departement::class, 'service_destination_id');
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}

<?php
// Fichier de test pour vérifier l'accès aux images

// Liste des images dans le dossier
$images = glob('upload/vehicules/*.{jpg,jpeg,png,gif}', GLOB_BRACE);

echo "<h1>Test d'affichage des images</h1>";

// Afficher les informations sur le serveur
echo "<h2>Informations serveur</h2>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";

// Afficher les images trouvées
echo "<h2>Images trouvées (" . count($images) . ")</h2>";
echo "<ul>";
foreach ($images as $image) {
    echo "<li>$image</li>";
}
echo "</ul>";

// Afficher les images
echo "<h2>Test d'affichage</h2>";
foreach ($images as $image) {
    echo "<div style='margin-bottom: 20px'>";
    echo "<h3>$image</h3>";
    
    // Méthode 1: Chemin relatif direct
    echo "<p>Méthode 1 (chemin relatif direct):</p>";
    echo "<img src='/$image' style='max-width:200px; border:1px solid blue;'>";
    
    // Méthode 2: Chemin relatif sans slash initial
    echo "<p>Méthode 2 (sans slash initial):</p>";
    echo "<img src='$image' style='max-width:200px; border:1px solid red;'>";
    
    echo "</div>";
}
?>

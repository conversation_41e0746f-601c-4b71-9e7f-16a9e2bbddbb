{"version": 3, "file": "cylinder.js.map", "lineCount": 16, "mappings": "A;;;;;;;;;AAUC,SAAS,CAACA,CAAD,CAAU,CACM,QAAtB,GAAI,MAAOC,OAAX,EAAkCA,MAAAC,QAAlC,EACIF,CAAA,CAAQ,SAAR,CACA,CADqBA,CACrB,CAAAC,MAAAC,QAAA,CAAiBF,CAFrB,EAG6B,UAAtB,GAAI,MAAOG,OAAX,EAAoCA,MAAAC,IAApC,CACHD,MAAA,CAAO,6BAAP,CAAsC,CAAC,YAAD,CAAe,0BAAf,CAAtC,CAAkF,QAAS,CAACE,CAAD,CAAa,CACpGL,CAAA,CAAQK,CAAR,CACAL,EAAAK,WAAA,CAAqBA,CACrB,OAAOL,EAH6F,CAAxG,CADG,CAOHA,CAAA,CAA8B,WAAtB,GAAA,MAAOK,WAAP,CAAoCA,UAApC,CAAiDC,IAAAA,EAAzD,CAXY,CAAnB,CAAA,CAaC,QAAS,CAACD,CAAD,CAAa,CAEpBE,QAASA,EAAe,CAACC,CAAD,CAAMC,CAAN,CAAYC,CAAZ,CAAkBC,CAAlB,CAAsB,CACrCH,CAAAI,eAAA,CAAmBH,CAAnB,CAAL,GACID,CAAA,CAAIC,CAAJ,CADJ,CACgBE,CAAAE,MAAA,CAAS,IAAT,CAAeH,CAAf,CADhB,CAD0C,CAD1CI,CAAAA,CAAWT,CAAA,CAAaA,CAAAS,SAAb,CAAmC,EAMlDP,EAAA,CAAgBO,CAAhB,CAA0B,0BAA1B,CAAsD,CAACA,CAAA,CAAS,iBAAT,CAAD,CAA8BA,CAAA,CAAS,eAAT,CAA9B,CAAyDA,CAAA,CAAS,sBAAT,CAAzD,CAA2FA,CAAA,CAAS,mBAAT,CAA3F,CAAtD;AAAiL,QAAS,CAACC,CAAD,CAAIC,CAAJ,CAAWC,CAAX,CAAmBC,CAAnB,CAAsB,CAc5M,IAAIC,EAAQH,CAAAI,MAAZ,CACIC,EAAcJ,CAAAI,YACdC,EAAAA,CAAQJ,CAAAI,MAhBgM,KAiBxMC,EAAOL,CAAAK,KACPC,EAAAA,CAAaN,CAAAM,WAlB2L,KAmBxMC,EAASV,CAAAU,OAnB+L,CAoBxMC,EAAUX,CAAAW,QAEVC,EAAAA,CAAgBZ,CAAAa,SAAAC,UAtBwL,KAuBxMC,EAAaH,CAAAG,WAvB2L,CA2BxMC,EAAeA,QAAS,CAACtB,CAAD,CAAO,CAC3B,MAAO,CAACA,CAAAuB,KAAA,CAAU,QAAS,CAACC,CAAD,CAAM,CAAE,MAAkB,GAAlB,GAAOA,CAAA,CAAI,CAAJ,CAAT,CAAzB,CADmB,CAenCT,EAAA,CAAW,UAAX,CAAuB,QAAvB,CAgBA,EAhBA,CAgBI,EAhBJ,CAkBA,CACIU,UAAW,UADf,CAEIC,gBAAiBpB,CAAAqB,YAAAC,OAAAR,UAAAS,WAAAT,UAAAM,gBAFrB,CAlBA,CAkGAI,EAAA,CAAkBjB,CAAA,CAAMK,CAAAa,WAAAC,OAAN,CAAuC,CACrDC,MAAO,CAAC,KAAD,CAAQ,QAAR,CAAkB,OAAlB,CAA2B,MAA3B,CAD8C,CAErDC,SAAU,UAF2C,CAGrDC,WAAYA,QAAS,CAACC,CAAD,CAAO,CACxB,IAAAC,qBAAA,CAA0B,MAA1B,CAAkC,IAAlC,CAAwC,CACpCC,MAAOF,CAD6B;AAEpCG,KAAMH,CAF8B,CAGpCI,IAAK9B,CAAA,CAAM0B,CAAN,CAAAK,SAAA,CAAqB,EAArB,CAAAC,IAAA,EAH+B,CAIpCC,OAAQjC,CAAA,CAAM0B,CAAN,CAAAK,SAAA,CAAqB,GAArB,CAAAC,IAAA,EAJ4B,CAAxC,CAOA,KAAAhC,MAAA,CAAa,IAAA0B,KAAb,CAAyBA,CACzB,OAAO,KATiB,CAHyB,CAAvC,CAelBlB,EAAAa,WAAAa,SAAA,CAAoCd,CACpCZ,EAAA0B,SAAA,CAAyBC,QAAS,CAACC,CAAD,CAAY,CAC1C,MAAO,KAAAC,UAAA,CAAe,UAAf,CAA2BD,CAA3B,CADmC,CAI9C5B,EAAA8B,aAAA,CAA6BC,QAAS,CAACH,CAAD,CAAY,CAAA,IAE1CI,EAAQlC,CAAA,CADGmC,IACIC,WAAP,CAFkC,CAI1CC,EAAahC,CAAAiC,KAAA,CAHFH,IAGE,CACbL,CADa,CAJ6B,CAM1CS,EAAa,CAACF,CAAAG,MAN4B,CAO1CC,EAAc,CAACJ,CAAAK,QAP2B,CAQ1ClB,EAPWW,IAOLQ,eAAA,CAAwBT,CAAxB,CACNJ,CADM,CAENH,EAAAA,CATWQ,IASFQ,eAAA,CAAwBT,CAAxB,CACTJ,CADS,CAET,CAAA,CAFS,CAGb,OAAO,CACHR,MAbWa,IAaJS,iBAAA,CAA0BpB,CAA1B,CAA+BG,CAA/B,CADJ,CAEHJ,KAdWY,IAcLU,gBAAA,CAAyBrB,CAAzB,CAA8BG,CAA9B,CAFH,CAGHH,IAAKA,CAHF,CAIHG,OAAQA,CAJL,CAKHmB,SAAU,CACNtB,IAAKe,CAAA,CAAa,CAAb,CAAiB,CADhB,CAENZ,OAAQY,CAAA,CAAa,CAAb,CAAiB,CAFnB,CAGNjB,MAAOmB,CAAA,CAAc,CAAd,CAAkB,CAHnB,CAINlB,KAAMkB,CAAA,CAAc,CAAd,CAAkB,CAJlB,CAKNM,MAAOV,CAAAS,SAAAC,MALD,CALP,CAbuC,CA4BlD7C;CAAA0C,iBAAA,CAAiCI,QAAS,CAACC,CAAD,CAAUC,CAAV,CAAsB,CACxDlE,CAAAA,CAAOiE,CAAAE,MAAA,CAAc,CAAd,CAAiB,CAAjB,CACX,IAAI7C,CAAA,CAAa4C,CAAb,CAAJ,CAA8B,CAC1B,IAAIE,EAAOF,CAAA,CAAW,CAAX,CACK,IAAhB,GAAIE,CAAA,CAAK,CAAL,CAAJ,GACIpE,CAAAqE,KAAA,CAAUH,CAAA,CAAW,CAAX,CAAV,CAEA,CADAlE,CAAAqE,KAAA,CAAUH,CAAA,CAAW,CAAX,CAAV,CACA,CAAAlE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMD,CAAA,CAAK,CAAL,CAAN,CAAeA,CAAA,CAAK,CAAL,CAAf,CAAV,CAHJ,CAF0B,CAA9B,IAQK,CACGA,CADH,CACUF,CAAA,CAAW,CAAX,CADV,KAEGI,EAASJ,CAAA,CAAW,CAAX,CACTK,EAAAA,CAASL,CAAA,CAAW,CAAX,CACG,IAAhB,GAAIE,CAAA,CAAK,CAAL,CAAJ,EAAqC,GAArC,GAAuBE,CAAA,CAAO,CAAP,CAAvB,EAA0D,GAA1D,GAA4CC,CAAA,CAAO,CAAP,CAA5C,GACIvE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAME,CAAA,CAAO,CAAP,CAAN,CAAiBA,CAAA,CAAO,CAAP,CAAjB,CAAV,CAEA,CADAvE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAME,CAAA,CAAO,CAAP,CAAN,CAAiBA,CAAA,CAAO,CAAP,CAAjB,CAA4BA,CAAA,CAAO,CAAP,CAA5B,CAAuCA,CAAA,CAAO,CAAP,CAAvC,CAAkDD,CAAA,CAAO,CAAP,CAAlD,CAA6DA,CAAA,CAAO,CAAP,CAA7D,CAAV,CACA,CAAAtE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMC,CAAA,CAAO,CAAP,CAAN,CAAiBA,CAAA,CAAO,CAAP,CAAjB,CAA4BA,CAAA,CAAO,CAAP,CAA5B,CAAuCA,CAAA,CAAO,CAAP,CAAvC,CAAkDF,CAAA,CAAK,CAAL,CAAlD,CAA2DA,CAAA,CAAK,CAAL,CAA3D,CAAV,CAHJ,CAJC,CAULpE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAV,CACA,OAAOrE,EArBqD,CAwBhEkB,EAAA2C,gBAAA,CAAgCW,QAAS,CAACP,CAAD,CAAUC,CAAV,CAAsB,CAC3D,IAAIlE,EAAO,EACX,IAAIsB,CAAA,CAAa2C,CAAb,CAAJ,CAA2B,CAAA,IACnBG,EAAOH,CAAA,CAAQ,CAAR,CADY,CAEnBQ,EAAQR,CAAA,CAAQ,CAAR,CACI,IAAhB,GAAIG,CAAA,CAAK,CAAL,CAAJ,EAAoC,GAApC,GAAuBK,CAAA,CAAM,CAAN,CAAvB,GACIzE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMI,CAAA,CAAM,CAAN,CAAN,CAAgBA,CAAA,CAAM,CAAN,CAAhB,CAAV,CAGA,CAFAzE,CAAAqE,KAAA,CAAUJ,CAAA,CAAQ,CAAR,CAAV,CAEA,CAAAjE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMD,CAAA,CAAK,CAAL,CAAN,CAAeA,CAAA,CAAK,CAAL,CAAf,CAAV,CAJJ,CAHuB,CAA3B,IAW0B,GAGtB;AAHIH,CAAA,CAAQ,CAAR,CAAA,CAAW,CAAX,CAGJ,EAFIjE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMJ,CAAA,CAAQ,CAAR,CAAA,CAAW,CAAX,CAAN,CAAqBA,CAAA,CAAQ,CAAR,CAAA,CAAW,CAAX,CAArB,CAAV,CAEJ,CAAAjE,CAAAqE,KAAA,CAAUJ,CAAA,CAAQ,CAAR,CAAV,CAAsBA,CAAA,CAAQ,CAAR,CAAtB,CAEA3C,EAAA,CAAa4C,CAAb,CAAJ,EACQE,CACJ,CADWF,CAAA,CAAW,CAAX,CACX,CAAgB,GAAhB,GAAIE,CAAA,CAAK,CAAL,CAAJ,GACIpE,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMD,CAAA,CAAK,CAAL,CAAN,CAAeA,CAAA,CAAK,CAAL,CAAf,CAAV,CAEA,CADApE,CAAAqE,KAAA,CAAUH,CAAA,CAAW,CAAX,CAAV,CACA,CAAAlE,CAAAqE,KAAA,CAAUH,CAAA,CAAW,CAAX,CAAV,CAHJ,CAFJ,GASQK,CAGJ,CAHaL,CAAA,CAAW,CAAX,CAGb,CAFIQ,CAEJ,CAFaR,CAAA,CAAW,CAAX,CAEb,CADIS,CACJ,CADaT,CAAA,CAAW,CAAX,CACb,CAAkB,GAAlB,GAAIK,CAAA,CAAO,CAAP,CAAJ,EAAuC,GAAvC,GAAyBG,CAAA,CAAO,CAAP,CAAzB,EAA4D,GAA5D,GAA8CC,CAAA,CAAO,CAAP,CAA9C,GACI3E,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMM,CAAA,CAAO,CAAP,CAAN,CAAiBA,CAAA,CAAO,CAAP,CAAjB,CAAV,CAEA,CADA3E,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMM,CAAA,CAAO,CAAP,CAAN,CAAiBA,CAAA,CAAO,CAAP,CAAjB,CAA4BA,CAAA,CAAO,CAAP,CAA5B,CAAuCA,CAAA,CAAO,CAAP,CAAvC,CAAkDD,CAAA,CAAO,CAAP,CAAlD,CAA6DA,CAAA,CAAO,CAAP,CAA7D,CAAV,CACA,CAAA1E,CAAAqE,KAAA,CAAU,CAAC,GAAD,CAAMK,CAAA,CAAO,CAAP,CAAN,CAAiBA,CAAA,CAAO,CAAP,CAAjB,CAA4BA,CAAA,CAAO,CAAP,CAA5B,CAAuCA,CAAA,CAAO,CAAP,CAAvC,CAAkDH,CAAA,CAAO,CAAP,CAAlD,CAA6DA,CAAA,CAAO,CAAP,CAA7D,CAAV,CAHJ,CAZJ,CAkBAvE,EAAAqE,KAAA,CAAU,CAAC,GAAD,CAAV,CACA,OAAOrE,EArCoD,CAwC/DkB,EAAAyC,eAAA,CAA+BiB,QAAS,CAAC1B,CAAD,CAAQJ,CAAR,CAAmB+B,CAAnB,CAA6B,CAAA,IAG7DC,EAAQhE,CAAA,CAAKgC,CAAAgC,MAAL,CACRhC,CAAAiC,MADQ,CAHqD,CAK7DC,EAASC,IAAAC,IAAA,CAASpC,CAAAiC,MAAT,CACTD,CADS,CAATE,CACS,CANoD,CAQ7DG,EAAclE,CAAdkE,EAAyBjC,CAAAkC,QAAAlC,MAAAmC,UAAAC,KAAzBH,CAA8D,EAA9DA,EACKrC,CAAAyC,gBADLJ,EACkC,CADlCA,EAGAK,EAAAA,CAAI1C,CAAA0C,EAAJA,EAAmBX,CAAA,CAAW/B,CAAA2C,OAAX,CAA8B,CAAjDD,CAX6D,KAc7DE;AAAI,KAAJA,CAAaV,CAdgD,CAe7DW,EAAU7C,CAAAiC,MAAVY,CAA4B,CAA5BA,CAAgC7C,CAAA8C,EAf6B,CAgB7DC,EAAUf,CAAVe,CAAkB,CAAlBA,CAAsB/C,CAAAgD,EAhBuC,CAkB7DC,EAAS,CAAC,CACFH,EAAG,CADD,CAEFJ,EAAGA,CAFD,CAGFM,EAAGd,CAHD,CAAD,CAIF,CACCY,EAAGF,CADJ,CAECF,EAAGA,CAFJ,CAGCM,EAAGd,CAHJ,CAJE,CAQF,CACCY,EAAGZ,CADJ,CAECQ,EAAGA,CAFJ,CAGCM,EAAGJ,CAHJ,CARE,CAYF,CACCE,EAAGZ,CADJ,CAECQ,EAAGA,CAFJ,CAGCM,EAAG,CAHJ,CAZE,CAgBF,CACCF,EAAGZ,CADJ,CAECQ,EAAGA,CAFJ,CAGCM,EAAG,CAACJ,CAHL,CAhBE,CAoBF,CACCE,EAAGF,CADJ,CAECF,EAAGA,CAFJ,CAGCM,EAAG,CAACd,CAHL,CApBE,CAwBF,CACCY,EAAG,CADJ,CAECJ,EAAGA,CAFJ,CAGCM,EAAG,CAACd,CAHL,CAxBE,CA4BF,CACCY,EAAG,CAACF,CADL,CAECF,EAAGA,CAFJ,CAGCM,EAAG,CAACd,CAHL,CA5BE,CAgCF,CACCY,EAAG,CAACZ,CADL,CAECQ,EAAGA,CAFJ,CAGCM,EAAG,CAACJ,CAHL,CAhCE,CAoCF,CACCE,EAAG,CAACZ,CADL,CAECQ,EAAGA,CAFJ,CAGCM,EAAG,CAHJ,CApCE,CAwCF,CACCF,EAAG,CAACZ,CADL,CAECQ,EAAGA,CAFJ,CAGCM,EAAGJ,CAHJ,CAxCE,CA4CF,CACCE,EAAG,CAACF,CADL,CAECF,EAAGA,CAFJ,CAGCM,EAAGd,CAHJ,CA5CE,CAgDF,CACCY,EAAG,CADJ,CAECJ,EAAGA,CAFJ,CAGCM,EAAGd,CAHJ,CAhDE,CAlBoD,CAuE7DgB,EAAWf,IAAAgB,IAAA,CAASd,CAAT,CAvEkD,CAwE7De,EAAWjB,IAAAkB,IAAA,CAAShB,CAAT,CAxEkD,CA2E7DS,CA3E6D,CA4E7DE,CAEJC,EAAAK,QAAA,CAAe,QAAS,CAACC,CAAD,CAAQC,CAAR,CAAW,CAC/BV,CAAA,CAAIS,CAAAT,EACJE,EAAA,CAAIO,CAAAP,EAGJC,EAAA,CAAOO,CAAP,CAAAV,EAAA,CAAeA,CAAf,CAAmBI,CAAnB,CAA8BF,CAA9B,CAAkCI,CAAlC,CAA8CP,CAC9CI,EAAA,CAAOO,CAAP,CAAAR,EAAA,CAAeA,CAAf,CAAmBE,CAAnB,CAA8BJ,CAA9B,CAAkCM,CAAlC,CAA8CL,CANf,CAAnC,CAQAU,EAAA,CAAoB3F,CAAA,CAAYmF,CAAZ,CAAoB7C,CAApB,CAA2B,CAAA,CAA3B,CAgBpB,OAdgE,IAAhElD,CAAIiF,IAAAuB,IAAA,CAASD,CAAA,CAAkB,CAAlB,CAAAf,EAAT,CAAkCe,CAAA,CAAkB,CAAlB,CAAAf,EAAlC,CAAJxF,EACgE,GADhEA,CACIiF,IAAAuB,IAAA,CAASD,CAAA,CAAkB,CAAlB,CAAAf,EAAT,CAAkCe,CAAA,CAAkB,CAAlB,CAAAf,EAAlC,CADJxF,CAGW,IAAAyG,WAAA,CAAgB,CACnBF,CAAA,CAAkB,CAAlB,CADmB,CAEnBA,CAAA,CAAkB,CAAlB,CAFmB,CAGnBA,CAAA,CAAkB,CAAlB,CAHmB,CAInBA,CAAA,CAAkB,CAAlB,CAJmB,CAAhB,CAKJ,CAAA,CALI,CAHXvG,CAYW,IAAA0G,cAAA,CAAmBH,CAAnB,CApGsD,CA2GrErF,EAAAwF,cAAA,CAA8BC,QAAS,CAACZ,CAAD,CAAS,CAAA,IACxC/F;AAAO,CAAC,CAAC,GAAD,CACR+F,CAAA,CAAO,CAAP,CAAAH,EADQ,CAERG,CAAA,CAAO,CAAP,CAAAP,EAFQ,CAAD,CADiC,CAIxCoB,EAAQb,CAAAc,OAARD,CAAwB,CAJgB,CAKxCN,CACJ,KAAKA,CAAL,CAAS,CAAT,CAAYA,CAAZ,CAAgBM,CAAhB,CAAuBN,CAAvB,EAA4B,CAA5B,CACItG,CAAAqE,KAAA,CAAU,CACN,GADM,CAEN0B,CAAA,CAAOO,CAAP,CAAAV,EAFM,CAEOG,CAAA,CAAOO,CAAP,CAAAd,EAFP,CAGNO,CAAA,CAAOO,CAAP,CAAW,CAAX,CAAAV,EAHM,CAGWG,CAAA,CAAOO,CAAP,CAAW,CAAX,CAAAd,EAHX,CAINO,CAAA,CAAOO,CAAP,CAAW,CAAX,CAAAV,EAJM,CAIWG,CAAA,CAAOO,CAAP,CAAW,CAAX,CAAAd,EAJX,CAAV,CAOJ,OAAOxF,EAdqC,CAvW4J,CAAhN,CAyXAF,EAAA,CAAgBO,CAAhB,CAA0B,iCAA1B,CAA6D,EAA7D,CAAiE,QAAS,EAAG,EAA7E,CAhYoB,CAbvB;", "sources": ["cylinder.src.js"], "names": ["factory", "module", "exports", "define", "amd", "Highcharts", "undefined", "_registerModule", "obj", "path", "args", "fn", "hasOwnProperty", "apply", "_modules", "H", "Color", "Math3D", "U", "color", "parse", "perspective", "merge", "pick", "seriesType", "charts", "deg2rad", "RendererProto", "<PERSON><PERSON><PERSON>", "prototype", "cuboidPath", "isSimplified", "some", "seg", "shapeType", "hasNewShapeType", "seriesTypes", "column", "pointClass", "cylinderMethods", "elements3d", "cuboid", "parts", "pathType", "fillSetter", "fill", "singleSetterForParts", "front", "back", "top", "brighten", "get", "bottom", "cylinder", "RendererProto.cylinder", "shapeArgs", "element3d", "cylinderPath", "RendererProto.cylinderPath", "chart", "renderer", "chartIndex", "cuboidData", "call", "isTopFirst", "isTop", "isFronFirst", "isFront", "getCylinderEnd", "getCylinderFront", "getCylinderBack", "zIndexes", "group", "RendererProto.getCylinderFront", "topPath", "bottomPath", "slice", "move", "push", "curve1", "curve2", "RendererProto.getCylinderBack", "line2", "curve3", "curve4", "RendererProto.getCylinderEnd", "isBottom", "depth", "width", "radius", "Math", "min", "angleOffset", "options", "options3d", "beta", "alphaCorrection", "y", "height", "c", "centerX", "x", "centerZ", "z", "points", "cosTheta", "cos", "sinTheta", "sin", "for<PERSON>ach", "point", "i", "perspectivePoints", "abs", "to<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON>ved<PERSON><PERSON>", "RendererProto.getCurvedPath", "limit", "length"]}
@extends('admin.admin_dashboard')
@section('admin')

<div class="page-content">
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Comptabilit<PERSON></div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-calculator"></i></a>
                    </li>
                    <li class="breadcrumb-item"><a href="{{ route('nomate.index') }}">Nomenclature Comptable</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Modifier un Compte</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card shadow-sm border-0">
        <div class="card-header bg-gradient {{ $nomate->is_header ? 'bg-primary' : 'bg-info' }} text-white py-3">
            <div class="d-flex align-items-center">
                <div class="rounded-circle bg-white text-{{ $nomate->is_header ? 'primary' : 'info' }} d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                    <i class="bx {{ $nomate->is_header ? 'bx-folder' : 'bx-file' }} fs-4"></i>
                </div>
                <div>
                    <h5 class="mb-0 fw-bold">Modifier le compte</h5>
                    <p class="mb-0 opacity-75">{{ $nomate->code }} - {{ $nomate->intitule }}</p>
                </div>
            </div>
        </div>
        <div class="card-body p-4">
            @if ($errors->any())
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
            @endif

            <form action="{{ route('nomate.update', $nomate->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="code" class="form-label">Code <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text bg-light"><i class="bx bx-code"></i></span>
                                <input type="text" class="form-control @error('code') is-invalid @enderror" id="code" name="code" value="{{ old('code', $nomate->code) }}" required>
                                @error('code')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label d-block">Type de compte</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_header" name="is_header" {{ old('is_header', $nomate->is_header) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_header">
                                    En-tête de compte
                                </label>
                                <small class="form-text text-muted d-block">
                                    Cochez cette case si ce compte est un en-tête qui regroupe d'autres comptes.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="intitule" class="form-label">Intitulé <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text bg-light"><i class="bx bx-text"></i></span>
                        <input type="text" class="form-control @error('intitule') is-invalid @enderror" id="intitule" name="intitule" value="{{ old('intitule', $nomate->intitule) }}" required>
                        @error('intitule')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="form-label">Statut <span class="text-danger">*</span></label>
                    <div class="d-flex">
                        <div class="form-check me-3">
                            <input class="form-check-input" type="radio" name="statut" id="statut_actif" value="actif" {{ old('statut', $nomate->statut) == 'actif' ? 'checked' : '' }} required>
                            <label class="form-check-label" for="statut_actif">
                                <span class="badge bg-success">Actif</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="statut" id="statut_inactif" value="inactif" {{ old('statut', $nomate->statut) == 'inactif' ? 'checked' : '' }} required>
                            <label class="form-check-label" for="statut_inactif">
                                <span class="badge bg-danger">Inactif</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('nomate.show', $nomate->id) }}" class="btn btn-outline-secondary">
                        <i class="bx bx-arrow-back me-1"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bx bx-save me-1"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

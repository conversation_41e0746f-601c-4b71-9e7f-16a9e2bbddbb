# DevBook - Système de Comptabilité Matière

## 1. Vue d'ensemble

Ce document technique détaille l'implémentation du module de Comptabilité Matière au sein de l'application GestionParkAutoFinal_ok. Le système sera intégré à l'interface existante, notamment dans le menu "Comptabilité Matière" de la sidebar, tout en préservant les fonctionnalités actuelles de l'application.

## 2. Architecture du système

### 2.1 Structure MVC

Le système suivra l'architecture MVC de Laravel avec:
- **Modèles**: Représentation des tables de la base de données
- **Vues**: Interfaces utilisateur en Blade avec Bootstrap et JavaScript
- **Contrôleurs**: Logique métier et traitement des requêtes

### 2.2 Intégration avec le système existant

- Utilisation de la structure d'authentification existante
- Réutilisation des composants UI déjà en place (layout, sidebar, etc.)
- Extension du système de rôles et permissions

## 3. Modèles de données

### 3.1 Nouvelles tables à créer

```php
// Articles
Schema::create('articles', function (Blueprint $table) {
    $table->id();
    $table->string('code_article')->unique();
    $table->string('designation');
    $table->enum('categorie', ['consommable', 'immobilisation', 'autre']);
    $table->foreignId('unite_id')->constrained('unites');
    $table->decimal('prix_unitaire_estime', 15, 2);
    $table->integer('stock_minimum')->default(0);
    $table->boolean('actif')->default(true);
    $table->timestamps();
    $table->softDeletes();
});

// Unités de mesure
Schema::create('unites', function (Blueprint $table) {
    $table->id();
    $table->string('code')->unique();
    $table->string('libelle');
    $table->timestamps();
});

// Entrées en stock
Schema::create('entrees', function (Blueprint $table) {
    $table->id();
    $table->foreignId('article_id')->constrained('articles');
    $table->decimal('quantite', 15, 2);
    $table->decimal('prix_unitaire', 15, 2);
    $table->date('date_entree');
    $table->string('reference_doc');
    $table->string('source');
    $table->text('observations')->nullable();
    $table->foreignId('user_id')->constrained('users');
    $table->timestamps();
    $table->softDeletes();
});

// Sorties de stock
Schema::create('sorties', function (Blueprint $table) {
    $table->id();
    $table->foreignId('article_id')->constrained('articles');
    $table->decimal('quantite', 15, 2);
    $table->date('date_sortie');
    $table->foreignId('beneficiaire_id')->constrained('users');
    $table->enum('type_sortie', ['affectation', 'consommation', 'mutation', 'autre']);
    $table->text('motif')->nullable();
    $table->foreignId('user_id')->constrained('users');
    $table->timestamps();
    $table->softDeletes();
});

// Immobilisations
Schema::create('immobilisations', function (Blueprint $table) {
    $table->id();
    $table->foreignId('article_id')->constrained('articles');
    $table->string('numero_inventaire')->unique();
    $table->date('date_acquisition');
    $table->decimal('valeur_acquisition', 15, 2);
    $table->foreignId('service_affecte_id')->constrained('departements');
    $table->enum('etat_physique', ['bon', 'moyen', 'mauvais', 'hors_service']);
    $table->enum('statut', ['en_service', 'reforme', 'perdu', 'autre']);
    $table->text('description')->nullable();
    $table->timestamps();
    $table->softDeletes();
});

// Ajustements de stock
Schema::create('ajustements', function (Blueprint $table) {
    $table->id();
    $table->foreignId('article_id')->constrained('articles');
    $table->decimal('quantite_avant', 15, 2);
    $table->decimal('quantite_apres', 15, 2);
    $table->date('date_ajustement');
    $table->text('motif');
    $table->foreignId('user_id')->constrained('users');
    $table->timestamps();
});

// Réformes
Schema::create('reformes', function (Blueprint $table) {
    $table->id();
    $table->foreignId('immobilisation_id')->constrained('immobilisations');
    $table->date('date_reforme');
    $table->text('motif');
    $table->string('reference_decision');
    $table->foreignId('user_id')->constrained('users');
    $table->timestamps();
});

// Mutations
Schema::create('mutations', function (Blueprint $table) {
    $table->id();
    $table->foreignId('immobilisation_id')->constrained('immobilisations');
    $table->foreignId('service_origine_id')->constrained('departements');
    $table->foreignId('service_destination_id')->constrained('departements');
    $table->date('date_mutation');
    $table->string('reference_doc');
    $table->text('observations')->nullable();
    $table->foreignId('user_id')->constrained('users');
    $table->timestamps();
});

// Inventaires
Schema::create('inventaires', function (Blueprint $table) {
    $table->id();
    $table->string('reference');
    $table->date('date_debut');
    $table->date('date_fin')->nullable();
    $table->enum('statut', ['en_cours', 'termine', 'valide', 'rejete']);
    $table->text('observations')->nullable();
    $table->foreignId('user_id')->constrained('users');
    $table->timestamps();
});

// Détails d'inventaire
Schema::create('inventaire_details', function (Blueprint $table) {
    $table->id();
    $table->foreignId('inventaire_id')->constrained('inventaires');
    $table->foreignId('article_id')->constrained('articles');
    $table->decimal('stock_theorique', 15, 2);
    $table->decimal('stock_physique', 15, 2);
    $table->decimal('ecart', 15, 2);
    $table->text('observations')->nullable();
    $table->timestamps();
});
```

### 3.2 Modèles Eloquent

Chaque table aura son modèle Eloquent correspondant avec les relations appropriées. Voici quelques exemples:

```php
// app/Models/Article.php
class Article extends Model
{
    use HasFactory, SoftDeletes;
    
    protected $fillable = [
        'code_article', 'designation', 'categorie', 'unite_id',
        'prix_unitaire_estime', 'stock_minimum', 'actif'
    ];
    
    public function unite()
    {
        return $this->belongsTo(Unite::class);
    }
    
    public function entrees()
    {
        return $this->hasMany(Entree::class);
    }
    
    public function sorties()
    {
        return $this->hasMany(Sortie::class);
    }
    
    public function immobilisations()
    {
        return $this->hasMany(Immobilisation::class);
    }
    
    public function stockActuel()
    {
        $entrees = $this->entrees()->sum('quantite');
        $sorties = $this->sorties()->sum('quantite');
        return $entrees - $sorties;
    }
}
```

## 4. Contrôleurs

### 4.1 Structure des contrôleurs

```
app/Http/Controllers/ComptabiliteMatiere/
├── ArticleController.php
├── EntreeController.php
├── SortieController.php
├── ImmobilisationController.php
├── AjustementController.php
├── ReformeController.php
├── MutationController.php
├── InventaireController.php
└── RapportController.php
```

### 4.2 Exemple de contrôleur

```php
// app/Http/Controllers/ComptabiliteMatiere/ArticleController.php
namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Unite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ArticleController extends Controller
{
    public function index()
    {
        $articles = Article::with('unite')->paginate(15);
        return view('comptabilite-matiere.articles.index', compact('articles'));
    }
    
    public function create()
    {
        $unites = Unite::all();
        return view('comptabilite-matiere.articles.create', compact('unites'));
    }
    
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code_article' => 'required|unique:articles',
            'designation' => 'required',
            'categorie' => 'required|in:consommable,immobilisation,autre',
            'unite_id' => 'required|exists:unites,id',
            'prix_unitaire_estime' => 'required|numeric|min:0',
            'stock_minimum' => 'required|integer|min:0',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        Article::create($request->all());
        
        return redirect()->route('comptabilite-matiere.articles.index')
            ->with('success', 'Article créé avec succès');
    }
    
    // Autres méthodes (show, edit, update, destroy)
}
```

## 5. Vues

### 5.1 Structure des vues

```
resources/views/comptabilite-matiere/
├── layout/
│   └── sidebar.blade.php (à modifier pour ajouter les liens)
├── articles/
│   ├── index.blade.php
│   ├── create.blade.php
│   ├── edit.blade.php
│   └── show.blade.php
├── entrees/
│   ├── index.blade.php
│   ├── create.blade.php
│   └── show.blade.php
├── sorties/
│   ├── index.blade.php
│   ├── create.blade.php
│   └── show.blade.php
├── immobilisations/
│   ├── index.blade.php
│   ├── create.blade.php
│   ├── edit.blade.php
│   └── show.blade.php
├── inventaires/
│   ├── index.blade.php
│   ├── create.blade.php
│   ├── details.blade.php
│   └── validation.blade.php
└── rapports/
    ├── index.blade.php
    ├── fiches-stock.blade.php
    ├── journal-matiere.blade.php
    ├── grand-livre.blade.php
    └── balance.blade.php
```

### 5.2 Modification de la sidebar

```php
<!-- resources/views/layout/sidebar.blade.php -->
<!-- Ajouter dans le menu existant -->
<li class="nav-item">
    <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseComptabiliteMatiere" aria-expanded="true" aria-controls="collapseComptabiliteMatiere">
        <i class="fas fa-fw fa-calculator"></i>
        <span>Comptabilité Matière</span>
    </a>
    <div id="collapseComptabiliteMatiere" class="collapse" aria-labelledby="headingComptabiliteMatiere" data-parent="#accordionSidebar">
        <div class="bg-white py-2 collapse-inner rounded">
            <h6 class="collapse-header">Référentiel:</h6>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.articles.index') }}">Articles</a>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.unites.index') }}">Unités</a>
            
            <h6 class="collapse-header">Mouvements:</h6>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.entrees.index') }}">Entrées</a>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.sorties.index') }}">Sorties</a>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.ajustements.index') }}">Ajustements</a>
            
            <h6 class="collapse-header">Immobilisations:</h6>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.immobilisations.index') }}">Registre</a>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.reformes.index') }}">Réformes</a>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.mutations.index') }}">Mutations</a>
            
            <h6 class="collapse-header">Inventaires:</h6>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.inventaires.index') }}">Gestion</a>
            
            <h6 class="collapse-header">Rapports:</h6>
            <a class="collapse-item" href="{{ route('comptabilite-matiere.rapports.index') }}">Tous les rapports</a>
        </div>
    </div>
</li>
```

### 5.3 Exemple de vue

```php
<!-- resources/views/comptabilite-matiere/articles/index.blade.php -->
@extends('layouts.admin')

@section('title', 'Gestion des Articles')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des Articles</h1>
        <a href="{{ route('comptabilite-matiere.articles.create') }}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Nouvel Article
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recherche et Filtres</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('comptabilite-matiere.articles.index') }}" method="GET">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <input type="text" class="form-control" name="search" placeholder="Rechercher..." value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <select class="form-control" name="categorie">
                            <option value="">Toutes les catégories</option>
                            <option value="consommable" {{ request('categorie') == 'consommable' ? 'selected' : '' }}>Consommable</option>
                            <option value="immobilisation" {{ request('categorie') == 'immobilisation' ? 'selected' : '' }}>Immobilisation</option>
                            <option value="autre" {{ request('categorie') == 'autre' ? 'selected' : '' }}>Autre</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <select class="form-control" name="actif">
                            <option value="">Tous les statuts</option>
                            <option value="1" {{ request('actif') == '1' ? 'selected' : '' }}>Actif</option>
                            <option value="0" {{ request('actif') == '0' ? 'selected' : '' }}>Inactif</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="submit" class="btn btn-primary btn-block">Filtrer</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Articles Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Liste des Articles</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Désignation</th>
                            <th>Catégorie</th>
                            <th>Unité</th>
                            <th>Prix Estimé</th>
                            <th>Stock Actuel</th>
                            <th>Stock Min.</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($articles as $article)
                        <tr>
                            <td>{{ $article->code_article }}</td>
                            <td>{{ $article->designation }}</td>
                            <td>
                                @if($article->categorie == 'consommable')
                                    <span class="badge badge-info">Consommable</span>
                                @elseif($article->categorie == 'immobilisation')
                                    <span class="badge badge-primary">Immobilisation</span>
                                @else
                                    <span class="badge badge-secondary">Autre</span>
                                @endif
                            </td>
                            <td>{{ $article->unite->libelle }}</td>
                            <td>{{ number_format($article->prix_unitaire_estime, 2, ',', ' ') }}</td>
                            <td>
                                @php
                                    $stockActuel = $article->stockActuel();
                                    $alertClass = $stockActuel <= $article->stock_minimum ? 'text-danger font-weight-bold' : '';
                                @endphp
                                <span class="{{ $alertClass }}">{{ number_format($stockActuel, 2, ',', ' ') }}</span>
                            </td>
                            <td>{{ number_format($article->stock_minimum, 0, ',', ' ') }}</td>
                            <td>
                                @if($article->actif)
                                    <span class="badge badge-success">Actif</span>
                                @else
                                    <span class="badge badge-danger">Inactif</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('comptabilite-matiere.articles.show', $article) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('comptabilite-matiere.articles.edit', $article) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('comptabilite-matiere.articles.destroy', $article) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet article?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $articles->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>
@endsection
```

## 6. Routes

```php
// routes/web.php (ajouter ces routes)

// Groupe de routes pour la comptabilité matière
Route::prefix('admin/comptabilite-matiere')->name('comptabilite-matiere.')->middleware(['auth', 'role:admin,magasinier,comptable'])->group(function () {
    // Référentiel
    Route::resource('articles', 'App\Http\Controllers\ComptabiliteMatiere\ArticleController');
    Route::resource('unites', 'App\Http\Controllers\ComptabiliteMatiere\UniteController');
    
    // Mouvements
    Route::resource('entrees', 'App\Http\Controllers\ComptabiliteMatiere\EntreeController');
    Route::resource('sorties', 'App\Http\Controllers\ComptabiliteMatiere\SortieController');
    Route::resource('ajustements', 'App\Http\Controllers\ComptabiliteMatiere\AjustementController');
    
    // Immobilisations
    Route::resource('immobilisations', 'App\Http\Controllers\ComptabiliteMatiere\ImmobilisationController');
    Route::resource('reformes', 'App\Http\Controllers\ComptabiliteMatiere\ReformeController');
    Route::resource('mutations', 'App\Http\Controllers\ComptabiliteMatiere\MutationController');
    
    // Inventaires
    Route::resource('inventaires', 'App\Http\Controllers\ComptabiliteMatiere\InventaireController');
    Route::get('inventaires/{inventaire}/details', 'App\Http\Controllers\ComptabiliteMatiere\InventaireController@details')->name('inventaires.details');
    Route::post('inventaires/{inventaire}/validation', 'App\Http\Controllers\ComptabiliteMatiere\InventaireController@validation')->name('inventaires.validation');
    
    // Rapports
    Route::get('rapports', 'App\Http\Controllers\ComptabiliteMatiere\RapportController@index')->name('rapports.index');
    Route::get('rapports/fiches-stock', 'App\Http\Controllers\ComptabiliteMatiere\RapportController@fichesStock')->name('rapports.fiches-stock');
    Route::get('rapports/journal-matiere', 'App\Http\Controllers\ComptabiliteMatiere\RapportController@journalMatiere')->name('rapports.journal-matiere');
    Route::get('rapports/grand-livre', 'App\Http\Controllers\ComptabiliteMatiere\RapportController@grandLivre')->name('rapports.grand-livre');
    Route::get('rapports/balance', 'App\Http\Controllers\ComptabiliteMatiere\RapportController@balance')->name('rapports.balance');
    Route::get('rapports/immobilisations', 'App\Http\Controllers\ComptabiliteMatiere\RapportController@immobilisations')->name('rapports.immobilisations');
    
    // Export PDF/Excel
    Route::get('export/{type}/{format}', 'App\Http\Controllers\ComptabiliteMatiere\ExportController@export')->name('export');
});
```

## 7. Migrations

Créer les fichiers de migration pour chaque table définie dans la section 3.1:

```bash
php artisan make:migration create_unites_table
php artisan make:migration create_articles_table
php artisan make:migration create_entrees_table
php artisan make:migration create_sorties_table
php artisan make:migration create_immobilisations_table
php artisan make:migration create_ajustements_table
php artisan make:migration create_reformes_table
php artisan make:migration create_mutations_table
php artisan make:migration create_inventaires_table
php artisan make:migration create_inventaire_details_table
```

## 8. Seeders

Créer des seeders pour les données initiales:

```php
// database/seeders/UnitesTableSeeder.php
namespace Database\Seeders;

use App\Models\Unite;
use Illuminate\Database\Seeder;

class UnitesTableSeeder extends Seeder
{
    public function run()
    {
        $unites = [
            ['code' => 'U', 'libelle' => 'Unité'],
            ['code' => 'KG', 'libelle' => 'Kilogramme'],
            ['code' => 'L', 'libelle' => 'Litre'],
            ['code' => 'M', 'libelle' => 'Mètre'],
            ['code' => 'M2', 'libelle' => 'Mètre carré'],
            ['code' => 'M3', 'libelle' => 'Mètre cube'],
            ['code' => 'RAME', 'libelle' => 'Rame'],
            ['code' => 'CARTON', 'libelle' => 'Carton'],
            ['code' => 'BOITE', 'libelle' => 'Boîte'],
        ];
        
        foreach ($unites as $unite) {
            Unite::create($unite);
        }
    }
}
```

## 9. Plan d'implémentation

### 9.1 Phase 1: Configuration et Référentiel
1. [x] Créer les migrations et modèles
2. [x] Implémenter les contrôleurs et vues du référentiel (Articles, Unités)
3. [x] Modifier la sidebar pour ajouter les liens
4. [ ] Tester les fonctionnalités CRUD du référentiel

### 9.2 Phase 2: Mouvements de Stock
1. [x] Implémenter les contrôleurs et vues pour les entrées et sorties
2. [x] Développer la logique de mise à jour automatique des stocks
3. [x] Créer les interfaces pour les ajustements de stock
4. [ ] Tester les mouvements et vérifier les calculs de stock

### 9.3 Phase 3: Immobilisations
1. [x] Implémenter les contrôleurs et vues pour les immobilisations
2. [x] Développer les fonctionnalités de réforme et mutation
3. [x] Créer les fiches individuelles d'immobilisation
4. [ ] Tester le suivi des immobilisations

### 9.4 Phase 4: Inventaires
1. [x] Implémenter le système d'inventaire
2. [x] Développer les interfaces de saisie et validation
3. [x] Créer la logique de comparaison entre stock théorique et physique
4. [ ] Tester le processus complet d'inventaire

### 9.5 Phase 5: Rapports et Comptabilité
1. [x] Implémenter les contrôleurs et vues pour les rapports
2. [x] Développer les algorithmes de génération des documents comptables
3. [x] Créer les fonctionnalités d'export PDF/Excel
4. [ ] Tester l'ensemble des rapports réglementaires

## 10. Considérations techniques

### 10.1 Sécurité
- [x] Implémenter un système RBAC (Role-Based Access Control) spécifique au module
- [x] Journaliser toutes les actions sensibles (création, modification, suppression)
- [x] Valider toutes les entrées utilisateur côté serveur

### 10.2 Performance
- [x] Optimiser les requêtes SQL avec des index appropriés
- [x] Mettre en cache les données fréquemment accédées
- [x] Paginer les résultats des listes

### 10.3 UX/UI
- [x] Utiliser des composants cohérents avec le reste de l'application
- [x] Implémenter des validations côté client pour une meilleure expérience utilisateur
- [x] Ajouter des confirmations pour les actions critiques
- [x] Utiliser des notifications pour informer l'utilisateur du résultat des actions

### 10.4 Tests
- [ ] Écrire des tests unitaires pour les modèles et les méthodes de calcul
- [ ] Créer des tests d'intégration pour les flux principaux
- [ ] Tester les scénarios critiques (ajustements, inventaires, etc.)

## 11. Conclusion

Ce DevBook fournit un plan détaillé pour l'implémentation du module de Comptabilité Matière conformément au cahier des charges. L'approche progressive par phases permettra de développer et tester chaque composant de manière méthodique tout en s'intégrant harmonieusement à l'application existante.

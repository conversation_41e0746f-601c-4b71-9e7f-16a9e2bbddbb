<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Unite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ArticleController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $articles = Article::with('unite')->orderBy('designation', 'asc')->get();
        return view('comptabilite-matiere.articles.index', compact('articles'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $unites = Unite::orderBy('libelle', 'asc')->pluck('libelle', 'id');
        return view('comptabilite-matiere.articles.create', compact('unites'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:20|unique:articles',
            'designation' => 'required|string|max:255',
            'description' => 'nullable|string',
            'unite_id' => 'required|exists:unites,id',
            'stock_alerte' => 'required|numeric|min:0',
            'type' => 'required|in:consommable,immobilisable',
            'categorie' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return redirect()->route('articles.create')
                ->withErrors($validator)
                ->withInput();
        }

        Article::create([
            'code' => $request->code,
            'designation' => $request->designation,
            'description' => $request->description,
            'unite_id' => $request->unite_id,
            'stock_alerte' => $request->stock_alerte,
            'type' => $request->type,
            'categorie' => $request->categorie,
            'slug' => Str::slug($request->designation),
        ]);

        return redirect()->route('articles.index')
            ->with('success', 'Article ajouté avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $article = Article::with(['unite', 'entrees', 'sorties', 'immobilisations'])->findOrFail($id);
        $stockActuel = $article->stockActuel();
        
        return view('comptabilite-matiere.articles.show', compact('article', 'stockActuel'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $article = Article::findOrFail($id);
        $unites = Unite::orderBy('libelle', 'asc')->pluck('libelle', 'id');
        
        return view('comptabilite-matiere.articles.edit', compact('article', 'unites'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $article = Article::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:20|unique:articles,code,' . $id,
            'designation' => 'required|string|max:255',
            'description' => 'nullable|string',
            'unite_id' => 'required|exists:unites,id',
            'stock_alerte' => 'required|numeric|min:0',
            'type' => 'required|in:consommable,immobilisable',
            'categorie' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return redirect()->route('articles.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }

        $article->update([
            'code' => $request->code,
            'designation' => $request->designation,
            'description' => $request->description,
            'unite_id' => $request->unite_id,
            'stock_alerte' => $request->stock_alerte,
            'type' => $request->type,
            'categorie' => $request->categorie,
            'slug' => Str::slug($request->designation),
        ]);

        return redirect()->route('articles.index')
            ->with('success', 'Article mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $article = Article::findOrFail($id);
        
        // Vérifier si l'article a des mouvements associés
        if ($article->entrees()->count() > 0 || $article->sorties()->count() > 0 || $article->immobilisations()->count() > 0) {
            return redirect()->route('articles.index')
                ->with('error', 'Impossible de supprimer cet article car il a des mouvements ou des immobilisations associés.');
        }
        
        $article->delete();

        return redirect()->route('articles.index')
            ->with('success', 'Article supprimé avec succès.');
    }
    
    /**
     * Affiche la fiche de stock d'un article
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function ficheStock($id)
    {
        $article = Article::with(['unite', 'entrees', 'sorties'])->findOrFail($id);
        $stockActuel = $article->stockActuel();
        
        // Récupérer tous les mouvements (entrées et sorties) triés par date
        $entrees = $article->entrees()->orderBy('date_entree', 'asc')->get();
        $sorties = $article->sorties()->orderBy('date_sortie', 'asc')->get();
        
        return view('comptabilite-matiere.articles.fiche-stock', compact('article', 'stockActuel', 'entrees', 'sorties'));
    }
}

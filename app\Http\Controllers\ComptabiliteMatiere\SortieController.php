<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Sortie;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SortieController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $sorties = Sortie::with(['article', 'beneficiaire', 'user'])
            ->orderBy('date_sortie', 'desc')
            ->get();
        
        return view('comptabilite-matiere.sorties.index', compact('sorties'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $articles = Article::orderBy('designation', 'asc')->pluck('designation', 'id');
        $beneficiaires = User::orderBy('name', 'asc')->pluck('name', 'id');
        $typesSortie = [
            'consommation' => 'Consommation',
            'transfert' => 'Transfert',
            'perte' => 'Perte/Casse',
            'autre' => 'Autre'
        ];
        
        return view('comptabilite-matiere.sorties.create', compact('articles', 'beneficiaires', 'typesSortie'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'quantite' => 'required|numeric|min:0.01',
            'date_sortie' => 'required|date',
            'beneficiaire_id' => 'required|exists:users,id',
            'type_sortie' => 'required|string|in:consommation,transfert,perte,autre',
            'motif' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->route('sorties.create')
                ->withErrors($validator)
                ->withInput();
        }
        
        // Vérifier si le stock est suffisant
        $article = Article::findOrFail($request->article_id);
        $stockActuel = $article->stockActuel();
        
        if ($stockActuel < $request->quantite) {
            return redirect()->route('sorties.create')
                ->with('error', 'Stock insuffisant. Stock actuel: ' . $stockActuel . ' ' . $article->unite->libelle)
                ->withInput();
        }

        Sortie::create([
            'article_id' => $request->article_id,
            'quantite' => $request->quantite,
            'date_sortie' => $request->date_sortie,
            'beneficiaire_id' => $request->beneficiaire_id,
            'type_sortie' => $request->type_sortie,
            'motif' => $request->motif,
            'user_id' => Auth::id(),
        ]);

        return redirect()->route('sorties.index')
            ->with('success', 'Sortie de stock enregistrée avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $sortie = Sortie::with(['article', 'article.unite', 'beneficiaire', 'user'])->findOrFail($id);
        return view('comptabilite-matiere.sorties.show', compact('sortie'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $sortie = Sortie::findOrFail($id);
        $articles = Article::orderBy('designation', 'asc')->pluck('designation', 'id');
        $beneficiaires = User::orderBy('name', 'asc')->pluck('name', 'id');
        $typesSortie = [
            'consommation' => 'Consommation',
            'transfert' => 'Transfert',
            'perte' => 'Perte/Casse',
            'autre' => 'Autre'
        ];
        
        return view('comptabilite-matiere.sorties.edit', compact('sortie', 'articles', 'beneficiaires', 'typesSortie'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $sortie = Sortie::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'quantite' => 'required|numeric|min:0.01',
            'date_sortie' => 'required|date',
            'beneficiaire_id' => 'required|exists:users,id',
            'type_sortie' => 'required|string|in:consommation,transfert,perte,autre',
            'motif' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->route('sorties.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }
        
        // Si l'article ou la quantité ont changé, vérifier le stock
        if ($sortie->article_id != $request->article_id || $sortie->quantite != $request->quantite) {
            $article = Article::findOrFail($request->article_id);
            $stockActuel = $article->stockActuel();
            
            // Ajouter la quantité actuelle de la sortie si c'est le même article
            if ($sortie->article_id == $request->article_id) {
                $stockActuel += $sortie->quantite;
            }
            
            if ($stockActuel < $request->quantite) {
                return redirect()->route('sorties.edit', $id)
                    ->with('error', 'Stock insuffisant. Stock disponible: ' . $stockActuel . ' ' . $article->unite->libelle)
                    ->withInput();
            }
        }

        $sortie->update([
            'article_id' => $request->article_id,
            'quantite' => $request->quantite,
            'date_sortie' => $request->date_sortie,
            'beneficiaire_id' => $request->beneficiaire_id,
            'type_sortie' => $request->type_sortie,
            'motif' => $request->motif,
        ]);

        return redirect()->route('sorties.index')
            ->with('success', 'Sortie de stock mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $sortie = Sortie::findOrFail($id);
        $sortie->delete();

        return redirect()->route('sorties.index')
            ->with('success', 'Sortie de stock supprimée avec succès.');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->string('code_article')->unique();
            $table->string('designation');
            $table->enum('categorie', ['consommable', 'immobilisation', 'autre']);
            $table->foreignId('unite_id')->constrained('unites');
            $table->decimal('prix_unitaire_estime', 15, 2);
            $table->integer('stock_minimum')->default(0);
            $table->boolean('actif')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};

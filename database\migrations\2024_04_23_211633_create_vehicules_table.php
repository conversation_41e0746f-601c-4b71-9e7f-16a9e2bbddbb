<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicules', function (Blueprint $table) {
            $table->id();
            $table->string('immatriculation')->unique();
            $table->integer('direction_structure')->nullable();
            $table->string('genre');
            $table->string('marque')->nullable();
            $table->string('type')->nullable();
            $table->string('date_acquisition')->nullable();
            $table->string('date_affectation')->nullable();
            $table->Integer('valeur_acquisition')->nullable();
            $table->string('usage')->nullable();
            $table->string('etat');
            $table->string('ptc')->nullable();
            $table->string('puissance')->nullable();
            $table->string('utilisateur')->nullable();
            $table->string('observation')->nullable();
            $table->string('image')->nullable();
            $table->string('service_utilisation')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicules');
    }
};

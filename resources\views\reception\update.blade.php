@extends('admin.admin_dashboard')
@section('admin')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<!-- Ajout des styles personnalisés -->
<link rel="stylesheet" href="{{ asset('css/reception-edit.css') }}">
<!-- Ajout de Choices.js pour la recherche dans les listes déroulantes -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css" />
<script src="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/scripts/choices.min.js"></script>

<div class="page-content">
	<!--breadcrumb-->
	<div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4 reception-breadcrumb">
		<div class="breadcrumb-title pe-3">
			<i class='bx bx-edit-alt me-2'></i>Modification d'une Réception de Marché
		</div>
		<div class="ps-3">
			<nav aria-label="breadcrumb">
				<ol class="breadcrumb mb-0 p-0">
					<li class="breadcrumb-item">
						<a href="{{ route('liste_reception') }}"><i class="bx bx-home-alt"></i> Accueil</a>
					</li>
					<li class="breadcrumb-item active" aria-current="page">Modifier la Réception</li>
				</ol>
			</nav>
		</div>
	</div>
	<!--end breadcrumb-->
	<div class="container">
		<div class="main-body">
			<div class="row">
				<div class="col-lg-12">
					<div class="card reception-card">
						<div class="card-header">
							<h4 class="mb-0"><i class='bx bx-edit me-2'></i>Modifier la Réception de Marché</h4>
						</div>
						<div class="card-body p-4">
							<form class="row g-3 reception-form" id="myForm" action="{{ route('modifier_reception') }}" method="POST" enctype="multipart/form-data">
								@csrf
                                @method('PUT')
                                <span hidden>{{ $departements = App\Models\Departement::all() }}</span>
                                <input type="hidden" name="id" value="{{ $receptions->id }}">
                                
                                <!-- Section Informations Générales -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-info-circle me-2'></i>Informations Générales</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6 form-group">
                                            <label for="date_enregistrement" class="form-label">Date d'Enregistrement</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                                                <input type="date" class="form-control" id="date_enregistrement" name="date_enregistrement" value="{{ $receptions->date_enregistrement }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="direction_structure" class="form-label">Direction / Structure</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-buildings'></i></span>
                                                <select id="direction_structure" name="direction_structure" class="form-select" required>
                                                    <option value="">Choisir la Direction / Structure...</option>
                                                    @foreach($departements as $departement)
                                                    <option value="{{$departement->id}}" {{ $receptions->direction_structure == $departement->id ? 'selected' : '' }}>{{$departement->nom_departement}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Section Références -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-link me-2'></i>Références</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6 form-group">
                                            <label for="reference_courier" class="form-label">Références du Courier</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-envelope'></i></span>
                                                <input type="text" class="form-control" id="reference_courier" name="reference_courier" value="{{ $receptions->reference_courier }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="reference_marche" class="form-label">Références du Marché</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-purchase-tag'></i></span>
                                                <input type="text" class="form-control" id="reference_marche" name="reference_marche" value="{{ $receptions->reference_marche }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-12 form-group">
                                            <label for="objet" class="form-label">Objet</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-notepad'></i></span>
                                                <input type="text" class="form-control" id="objet" name="objet" value="{{ $receptions->objet }}" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Section Exécution -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-time me-2'></i>Détails d'Exécution</h5>
                                    <div class="row g-3">
                                        <div class="col-md-6 form-group">
                                            <label for="periode_execution" class="form-label">Période d'Exécution</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-calendar-event'></i></span>
                                                <input type="text" class="form-control" id="periode_execution" name="periode_execution" value="{{ $receptions->periode_execution }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="executant" class="form-label">Exécutant</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-user'></i></span>
                                                <input type="text" class="form-control" id="executant" name="executant" value="{{ $receptions->executant }}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-12 form-group">
                                            <label for="observation" class="form-label">Observations</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class='bx bx-comment'></i></span>
                                                <textarea class="form-control" id="observation" name="observation" rows="3">{{ $receptions->observation }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Section Documents -->
                                <div class="form-section">
                                    <h5 class="form-section-title"><i class='bx bx-file me-2'></i>Documents</h5>
                                    <div class="row g-3">
                                        <!-- Affichage du PV existant s'il y en a un -->
                                        @if ($receptions->pv_reception)
                                        <div class="col-md-12" id="current-pv-container" data-has-pv="true">
                                            <div class="current-file">
                                                <div class="current-file-icon">
                                                    <i class='bx bxs-file-pdf'></i>
                                                </div>
                                                <div class="current-file-info">
                                                    <div class="current-file-name">PV de réception actuel</div>
                                                    <div class="current-file-meta">Vous pouvez remplacer ce fichier en sélectionnant un nouveau document ci-dessous</div>
                                                </div>
                                                <div class="current-file-actions">
                                                    @php
                                                        $filename = basename($receptions->pv_reception);
                                                    @endphp
                                                    <a href="{{ route('documents.show_pv', $filename) }}" class="view-btn" id="preview-pv-btn" data-bs-toggle="tooltip" title="Voir le PV">
                                                        <i class='bx bx-show'></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        @endif
                                        
                                        <!-- Champ pour télécharger un nouveau PV -->
                                        <div class="col-md-12">
                                            <label for="pv_reception" class="form-label">Document scanné du PV de Réception</label>
                                            <div class="file-upload">
                                                <div class="file-upload-icon">
                                                    <i class='bx bx-upload'></i>
                                                </div>
                                                <div class="file-upload-text">Glissez-déposez un fichier ou cliquez pour parcourir</div>
                                                <div class="file-upload-info">Formats acceptés: PDF, JPG, PNG (Max: 5MB)</div>
                                                <input type="file" name="pv_reception" id="pv_reception" accept=".pdf,.jpg,.jpeg,.png">
                                            </div>
                                            <div class="selected-file d-none mt-2">
                                                <span class="badge badge-primary"><i class='bx bx-file me-1'></i> <span id="selected-file-name"></span></span>
                                            </div>
                                        </div>
                                        
                                        <!-- Case à cocher pour le statut du PV -->
                                        <div class="col-md-12 mt-2">
                                            <label class="custom-checkbox">
                                                <input type="checkbox" id="pv_checked" disabled>
                                                <span class="checkmark"></span>
                                                <span class="custom-checkbox-label">PV de Réception disponible</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Boutons d'action -->
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between">
                                                <a href="{{ route('liste_reception') }}" class="btn btn-secondary">
                                                    <i class='bx bx-arrow-back me-1'></i> Retour à la liste
                                                </a>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class='bx bx-save me-1'></i> Enregistrer les modifications
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Modal de prévisualisation PV -->
<div class="modal fade" id="pvPreviewModal" tabindex="-1" aria-labelledby="pvPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pvPreviewModalLabel">Prévisualisation du PV</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Le contenu sera chargé dynamiquement -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Script personnalisé pour la page d'édition des réceptions -->
<script src="{{ asset('js/reception-edit.js') }}"></script>

<!-- Script pour cocher/décocher en fonction de la sélection du fichier -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialiser la case à cocher en fonction de l'existence d'un PV
        const pvContainer = document.getElementById('current-pv-container');
        const pvCheckbox = document.getElementById('pv_checked');
        
        if (pvContainer && pvCheckbox) {
            pvCheckbox.checked = true;
        }
        
        // Gérer le changement de fichier
        const pvInput = document.getElementById('pv_reception');
        if (pvInput && pvCheckbox) {
            pvInput.addEventListener('change', function() {
                pvCheckbox.checked = this.files.length > 0;
                
                // Afficher le nom du fichier sélectionné
                const fileNameDisplay = document.getElementById('selected-file-name');
                const selectedFileContainer = document.querySelector('.selected-file');
                
                if (fileNameDisplay && selectedFileContainer) {
                    if (this.files.length > 0) {
                        fileNameDisplay.textContent = this.files[0].name;
                        selectedFileContainer.classList.remove('d-none');
                    } else {
                        selectedFileContainer.classList.add('d-none');
                    }
                }
            });
        }
    });
</script>

<script type="text/javascript">
    $(document).ready(function (){
        // Pas d'initialisation de Choices.js pour le sélecteur de département
        // On utilise le select natif pour éviter les problèmes d'affichage
        
        // Validation du formulaire
        $('#myForm').validate({
            rules: {
                departement_id: {
                    required : true,
                }, 
                nom: {
                    required : true,
                }, 
                prenom: {
                    required : true,
                }, 
                email: {
                    required : true,
                }, 
                sexe: {
                    required : true,
                }, 
                contact: {
                    required : true,
                }, 
                fonctionOccupe: {
                    required : true,
                }, 
                dateDeNaissance: {
                    required : true,
                }, 
                dateEngagement: {
                    required : true,
                }, 
                montantJournalier: {
                    required : true,
                }, 
                
            },
            messages :{
                departement_id: {
                    required : 'Veuillez choisir le département',
                },
                nom: {
                    required : 'Veuillez saisir le nom',
                },
                prenom: {
                    required : 'Veuillez saisir le prénom',
                },
                email: {
                    required : 'Veuillez saisir l\'email',
                },
                sexe: {
                    required : 'Veuillez choisir le sexe',
                },
                contact: {
                    required : 'Veuillez saisir le contact',
                },
                fonctionOccupe: {
                    required : 'Veuillez saisir la fonction occupée',
                },
                dateDeNaissance: {
                    required : 'Veuillez renseigner la date de naissance',
                },
                dateEngagement: {
                    required : 'Veuillez renseigner la date d\engagement',
                },
                montantJournalier: {
                    required : 'Veuillez saisir le salaire',
                },
                 

            },
            errorElement : 'span', 
            errorPlacement: function (error,element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight : function(element, errorClass, validClass){
                $(element).addClass('is-invalid');
            },
            unhighlight : function(element, errorClass, validClass){
                $(element).removeClass('is-invalid');
            },
        });
    });
    
</script>
<script type="text/javascript">
    $(document).ready(function(){
        $('#image').change(function(e){
            let reader = new FileReader();
            reader.onload = function(e){
                $('#showImage').attr('src',e.target.result);
            }
            reader.readAsDataURL(e.target.files['0']);
        });
    });
</script>
@endsection
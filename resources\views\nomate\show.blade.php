@extends('admin.admin_dashboard')
@section('admin')

<div class="page-content">
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Comptabilit<PERSON></div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-calculator"></i></a>
                    </li>
                    <li class="breadcrumb-item"><a href="{{ route('nomate.index') }}">Nomenclature Comptable</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Détails du Compte</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Carte principale avec les détails -->  
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header {{ $nomate->is_header ? 'bg-primary' : 'bg-info' }} bg-gradient text-white py-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-white text-{{ $nomate->is_header ? 'primary' : 'info' }} d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <i class="bx {{ $nomate->is_header ? 'bx-folder' : 'bx-file' }} fs-4"></i>
                            </div>
                            <div>
                                <h5 class="mb-0 fw-bold">{{ $nomate->code }}</h5>
                                <p class="mb-0 opacity-75">{{ $nomate->intitule }}</p>
                            </div>
                        </div>
                        <a href="{{ route('nomate.edit', $nomate->id) }}" class="btn btn-light btn-sm">
                            <i class="bx bx-edit me-1"></i> Modifier
                        </a>
                    </div>
                </div>
                <div class="card-body p-4">
                    @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bx bx-check-circle me-1"></i> {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    @endif
                    
                    <div class="row g-4">
                        <div class="col-md-12">
                            <div class="p-3 bg-light rounded-3 mb-3">
                                <h6 class="fw-bold mb-3 text-dark">Informations du compte</h6>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="text-muted small">Code</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bx bx-code"></i></span>
                                                <input type="text" class="form-control" value="{{ $nomate->code }}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="text-muted small">Type</label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-white"><i class="bx bx-category"></i></span>
                                                <input type="text" class="form-control {{ $nomate->is_header ? 'text-primary fw-bold' : 'text-info' }}" value="{{ $nomate->is_header ? 'En-tête' : 'Compte' }}" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="text-muted small">Intitulé</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-white"><i class="bx bx-text"></i></span>
                                        <input type="text" class="form-control" value="{{ $nomate->intitule }}" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="text-muted small">Statut</label>
                                    <div class="d-flex align-items-center">
                                        <div class="form-check form-switch ms-2">
                                            <input class="form-check-input" type="checkbox" {{ $nomate->statut == 'actif' ? 'checked' : '' }} disabled>
                                            <label class="form-check-label {{ $nomate->statut == 'actif' ? 'text-success' : 'text-danger' }}">
                                                {{ ucfirst($nomate->statut) }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-white border-0 p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted small">
                            <i class="bx bx-calendar me-1"></i> Créé le {{ $nomate->created_at->format('d/m/Y à H:i') }}
                            <span class="mx-2">|</span>
                            <i class="bx bx-refresh me-1"></i> Mis à jour le {{ $nomate->updated_at->format('d/m/Y à H:i') }}
                        </div>
                        <a href="{{ route('nomate.index') }}" class="btn btn-outline-primary">
                            <i class="bx bx-arrow-back me-1"></i> Retour à la liste
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Carte latérale avec informations supplémentaires -->  
        <div class="col-lg-4">
            <div class="card shadow-sm border-0 mb-3">
                <div class="card-header bg-light py-3">
                    <h6 class="mb-0 fw-bold"><i class="bx bx-info-circle me-1"></i> Informations complémentaires</h6>
                </div>
                <div class="card-body p-3">
                    <div class="alert alert-{{ $nomate->is_header ? 'primary' : 'info' }} bg-light border-0 mb-3">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="bx {{ $nomate->is_header ? 'bx-folder' : 'bx-file' }} fs-1 text-{{ $nomate->is_header ? 'primary' : 'info' }}"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">Type de compte</h6>
                                <p class="mb-0">
                                    @if($nomate->is_header)
                                    Ce compte est un <strong>en-tête</strong> qui peut regrouper plusieurs comptes.
                                    @else
                                    Ce compte est un <strong>compte standard</strong> qui peut être utilisé pour les opérations comptables.
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-light border-0 mb-0">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="bx bx-help-circle fs-1 text-secondary"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">Aide</h6>
                                <p class="mb-0">
                                    La nomenclature comptable (NOMATE) est utilisée pour classer les opérations comptables selon un plan comptable standardisé.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm border-0">
                <div class="card-header bg-light py-3">
                    <h6 class="mb-0 fw-bold"><i class="bx bx-link-alt me-1"></i> Actions rapides</h6>
                </div>
                <div class="card-body p-3">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('nomate.index') }}" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bx bx-list-ul me-2 text-primary"></i> Voir tous les comptes
                        </a>
                        <a href="{{ route('nomate.edit', $nomate->id) }}" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bx bx-edit me-2 text-primary"></i> Modifier ce compte
                        </a>
                        <a href="{{ route('nomate.search') }}?search={{ $nomate->code }}" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bx bx-search me-2 text-primary"></i> Rechercher des comptes similaires
                        </a>
                        @if($nomate->is_header)
                        <a href="#" class="list-group-item list-group-item-action d-flex align-items-center">
                            <i class="bx bx-folder-open me-2 text-primary"></i> Voir les sous-comptes
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

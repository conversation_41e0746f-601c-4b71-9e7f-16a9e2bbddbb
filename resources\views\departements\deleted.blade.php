@extends('admin.admin_dashboard')
@section('admin')

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

<style>
    :root {
        --primary: #4361ee;
        --primary-light: #4895ef;
        --secondary: #3f37c9;
        --accent: #4cc9f0;
        --success: #4ade80;
        --danger: #f87171;
        --warning: #fbbf24;
        --info: #60a5fa;
        --dark: #374151;
        --light: #f9fafb;
    }

    /* Cards et conteneurs */
    .card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        padding: 1.2rem;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header .header-icon {
        font-size: 1.4rem;
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 1.8rem;
    }

    /* Fil d'ariane */
    .page-breadcrumb {
        background: linear-gradient(to right, #f9fafb, #f3f4f6);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        border-left: 4px solid var(--primary);
    }

    .breadcrumb-item a {
        color: var(--primary);
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .breadcrumb-item a:hover {
        color: var(--secondary);
        text-decoration: none;
    }

    .breadcrumb-item a i {
        margin-right: 0.5rem;
    }

    .breadcrumb-item.active {
        color: var(--dark);
        font-weight: 600;
    }

    /* Tableaux */
    .table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
    }

    .table th {
        font-weight: 600;
        color: var(--dark);
        background-color: #f9fafb;
        padding: 1rem;
        text-align: left;
        border-bottom: 2px solid #e5e7eb;
    }

    .table td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #e5e7eb;
        color: #4b5563;
    }

    .table tbody tr:hover {
        background-color: #f9fafb;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Boutons d'action */
    .btn-action {
        width: 36px;
        height: 36px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        transition: all 0.3s ease;
        margin-right: 0.5rem;
    }

    .btn-action:last-child {
        margin-right: 0;
    }

    .btn-action i {
        font-size: 1.2rem;
    }

    .btn-info {
        background-color: rgba(96, 165, 250, 0.1);
        color: var(--info);
        border: none;
    }

    .btn-info:hover {
        background-color: var(--info);
        color: white;
        transform: translateY(-2px);
    }

    .btn-success {
        background-color: rgba(74, 222, 128, 0.1);
        color: var(--success);
        border: none;
    }

    .btn-success:hover {
        background-color: var(--success);
        color: white;
        transform: translateY(-2px);
    }

    .btn-danger {
        background-color: rgba(248, 113, 113, 0.1);
        color: var(--danger);
        border: none;
    }

    .btn-danger:hover {
        background-color: var(--danger);
        color: white;
        transform: translateY(-2px);
    }

    /* Couleurs des départements */
    .dept-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }

    /* Animation d'entrée */
    .animate__fadeIn {
        animation-duration: 0.6s;
    }

    .animate__fadeInUp {
        animation-duration: 0.8s;
    }
</style>

<div class="page-content animate__animated animate__fadeIn">
    <!-- Fil d'ariane amélioré -->
    <div class="page-breadcrumb d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3 d-flex align-items-center">
            <i class='bx bx-building-house me-2 text-primary fs-5'></i>
            <div>
                <h4 class="mb-0">Directions et Structures Désactivées</h4>
                <p class="mb-0 text-secondary small">Liste des directions et structures désactivées</p>
            </div>
        </div>
        <div class="ms-auto">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class='bx bx-home-alt'></i> Tableau de bord</a></li>
                <li class="breadcrumb-item"><a href="{{ route('departements.index') }}"><i class='bx bx-building-house'></i> Directions</a></li>
                <li class="breadcrumb-item active" aria-current="page">Directions Désactivées</li>
            </ol>
        </div>
    </div>

    <!-- Section d'affichage des messages -->
    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class='bx bx-error-circle me-2 fs-4'></i>
            <strong>Erreur!</strong> {{ session('error') }}
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class='bx bx-check-circle me-2 fs-4'></i>
            <strong>Succès!</strong> {{ session('success') }}
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <!-- Carte principale avec tableau -->
    <div class="card animate__animated animate__fadeInUp">
        <div class="card-header">
            <div>
                <i class='bx bx-building-house header-icon'></i>
                Directions et Structures Désactivées
            </div>
            <div>
                <a href="{{ route('departements.index') }}" class="btn btn-light btn-sm">
                    <i class='bx bx-arrow-back'></i> Retour à la liste active
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Tableau des départements désactivés -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50px">
                                <i class='bx bx-hash me-1 text-primary'></i> #
                            </th>
                            <th>
                                <i class='bx bx-building me-1 text-primary'></i> Nom du Département
                            </th>
                            <th>
                                <i class='bx bx-calendar me-1 text-primary'></i> Date Création
                            </th>
                            <th>
                                <i class='bx bx-calendar-edit me-1 text-primary'></i> Date Modification
                            </th>
                            <th width="150px" class="text-center">
                                <i class='bx bx-cog me-1 text-primary'></i> Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(count($departements) > 0)
                            @foreach($departements as $key => $item)
                            <tr style="animation-delay: {{ $key * 0.05 }}s">
                                <td class="fw-bold">
                                    {{ $key+1 }}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="dept-color" style="background-color: {{ '#' . substr(md5($item->nom_departement), 0, 6) }}"></span>
                                        <span class="fw-medium">{{ $item->nom_departement }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        <i class='bx bx-calendar me-1'></i>
                                        {{ \Illuminate\Support\Carbon::parse($item->created_at)->format('d-m-Y') }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        <i class='bx bx-refresh me-1'></i>
                                        {{ \Illuminate\Support\Carbon::parse($item->updated_at)->format('d-m-Y') }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <button type="button" title="Réactiver" class="btn btn-success btn-action restore-btn" data-id="{{ $item->id }}" data-name="{{ $item->nom_departement }}">
                                        <i class='bx bx-refresh'></i>
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class='bx bx-info-circle text-secondary mb-2' style="font-size: 2rem;"></i>
                                        <p class="mb-0">Aucune direction ou structure désactivée trouvée</p>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion de la réactivation d'un département
        const restoreBtns = document.querySelectorAll('.restore-btn');
        
        restoreBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const deptId = this.getAttribute('data-id');
                const deptName = this.getAttribute('data-name');
                
                Swal.fire({
                    title: 'Réactiver cette direction?',
                    html: `Voulez-vous vraiment réactiver <strong>${deptName}</strong> ?<br>Cette direction sera à nouveau visible dans la liste principale.`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#4ade80',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Oui, réactiver',
                    cancelButtonText: 'Annuler',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Créer une requête AJAX pour réactiver le département
                        const xhr = new XMLHttpRequest();
                        xhr.open('POST', `/departements/restore/${deptId}`);
                        xhr.setRequestHeader('Content-Type', 'application/json');
                        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                        xhr.responseType = 'json';
                        
                        // Ajouter le paramètre _method pour simuler une requête PUT
                        const data = JSON.stringify({
                            _method: 'PUT'
                        });
                        
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                Swal.fire({
                                    title: 'Réactivé!',
                                    text: `La direction ${deptName} a été réactivée avec succès.`,
                                    icon: 'success',
                                    confirmButtonColor: '#4361ee'
                                }).then(() => {
                                    // Recharger la page pour mettre à jour la liste
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Erreur!',
                                    text: xhr.response?.message || 'Une erreur est survenue lors de la réactivation.',
                                    icon: 'error',
                                    confirmButtonColor: '#4361ee'
                                });
                            }
                        };
                        
                        xhr.onerror = function() {
                            Swal.fire({
                                title: 'Erreur!',
                                text: 'Une erreur de connexion est survenue.',
                                icon: 'error',
                                confirmButtonColor: '#4361ee'
                            });
                        };
                        
                        xhr.send(data);
                    }
                });
            });
        });
    });
</script>

@endsection

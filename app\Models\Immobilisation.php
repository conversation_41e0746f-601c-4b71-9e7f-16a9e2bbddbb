<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Immobilisation extends Model
{
    use HasFactory;
    use \Illuminate\Database\Eloquent\SoftDeletes;
    
    protected $fillable = [
        'article_id',
        'numero_inventaire',
        'date_acquisition',
        'valeur_acquisition',
        'service_affecte_id',
        'etat_physique',
        'statut',
        'description'
    ];
    
    protected $casts = [
        'date_acquisition' => 'date',
    ];
    
    public function article()
    {
        return $this->belongsTo(Article::class);
    }
    
    public function serviceAffecte()
    {
        return $this->belongsTo(Departement::class, 'service_affecte_id');
    }
    
    public function reformes()
    {
        return $this->hasMany(Reforme::class);
    }
    
    public function mutations()
    {
        return $this->hasMany(Mutation::class);
    }
}

<!doctype html>
<html lang="fr">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" href="{{ asset('backend/assets/images/favicon-32x32.png') }}" type="image/png" />
	<link href="{{ asset('backend/assets/css/bootstrap.min.css') }}" rel="stylesheet">
	<link href="{{ asset('backend/assets/css/bootstrap-extended.css') }}" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
	<link href="{{ asset('backend/assets/css/app.css') }}" rel="stylesheet">
	<link href="{{ asset('backend/assets/css/icons.css') }}" rel="stylesheet">
	<title>Connexion des Administrateurs</title>
	<style>
		body {
			min-height: 100vh;
			background-color: #f8f9fa;
			margin: 0;
			padding: 0;
		}
		.wrapper {
			min-height: 100vh;
			display: flex;
			align-items: center;
		}
		.auth-cover-left {
			background-image: url('{{ asset('backend/assets/images/agriculture.png') }}');
			background-size: cover;
			background-position: center;
			position: relative;
			min-height: 100vh;
		}
		.auth-cover-left::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.4);
		}
		.login-card {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 15px;
			box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
			margin: 1rem;
			padding: 2rem;
		}
		.form-control:focus {
			border-color: #0d6efd;
			box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
		}
		.btn-primary {
			padding: 0.8rem;
			font-size: 1.1rem;
		}
		.logo-img {
			max-width: 110px;
			height: auto;
		}
		
		/* Responsive Design */
		@media (max-width: 1199.98px) {
			.auth-cover-left {
				display: none;
			}
			.auth-cover-right {
				width: 100%;
				max-width: 600px;
				margin: 0 auto;
			}
		}
		
		@media (max-width: 767.98px) {
			.login-card {
				margin: 1rem;
				padding: 1.5rem;
			}
			.logo-img {
				max-width: 90px;
			}
		}
		
		@media (max-width: 575.98px) {
			.login-card {
				margin: 0.5rem;
				padding: 1rem;
			}
			.form-label {
				font-size: 0.9rem;
			}
			.btn-primary {
				padding: 0.6rem;
				font-size: 1rem;
			}
		}
		
		/* Amélioration de l'accessibilité */
		.form-control {
			height: 45px;
		}
		.input-group-text {
			cursor: pointer;
			padding: 0.5rem 0.75rem;
		}
		.form-check-input {
			cursor: pointer;
		}
		.form-check-label {
			cursor: pointer;
			user-select: none;
		}
	</style>
</head>

<body>
	<div class="wrapper">
		<div class="container-fluid p-0">
			<div class="row g-0">
				<div class="col-12 col-xl-7 col-xxl-8">
					<div class="auth-cover-left"></div>
				</div>

				<div class="col-12 col-xl-5 col-xxl-4">
					<div class="auth-cover-right d-flex align-items-center justify-content-center">
						<div class="login-card w-100">
							<div class="text-center mb-4">
								<img src="{{ asset('backend/assets/images/logo_armoirie1.png') }}" class="logo-img mb-3" alt="Logo">
								<h4 class="mb-2">MAHVDR Admin</h4>
								<p class="mb-0 text-muted">Veuillez vous connecter à votre compte</p>
							</div>

							<form method="POST" action="{{ route('login') }}">
								@csrf
								<div class="mb-3">
									<label for="login" class="form-label">Email/Nom/Téléphone</label>
									<input type="text" class="form-control @error('login') is-invalid @enderror" 
										id="login" name="login" value="{{ old('login') }}" 
										required autofocus>
									@error('login')
										<div class="invalid-feedback">{{ $message }}</div>
									@enderror
								</div>

								<div class="mb-4">
									<label for="password" class="form-label">Mot de Passe</label>
									<div class="input-group" id="show_hide_password">
										<input type="password" class="form-control border-end-0" 
											id="password" name="password" required>
										<span class="input-group-text bg-transparent">
											<i class="bx bx-hide"></i>
										</span>
									</div>
								</div>

								<div class="mb-4">
									<div class="form-check form-switch">
										<input class="form-check-input" type="checkbox" id="remember_me" name="remember">
										<label class="form-check-label" for="remember_me">Se souvenir de moi</label>
									</div>
								</div>

								<div class="d-grid">
									<button type="submit" class="btn btn-primary">
										Connexion
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script src="{{ asset('backend/assets/js/jquery.min.js') }}"></script>
	<script src="{{ asset('backend/assets/js/bootstrap.bundle.min.js') }}"></script>
	<script>
		$(document).ready(function () {
			$("#show_hide_password .input-group-text").on('click', function (event) {
				event.preventDefault();
				var input = $('#show_hide_password input');
				var icon = $('#show_hide_password i');
				
				if (input.attr("type") == "text") {
					input.attr('type', 'password');
					icon.addClass("bx-hide").removeClass("bx-show");
				} else if (input.attr("type") == "password") {
					input.attr('type', 'text');
					icon.removeClass("bx-hide").addClass("bx-show");
				}
			});
		});
	</script>
</body>
</html>
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ajustement extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'article_id',
        'quantite_avant',
        'quantite_apres',
        'date_ajustement',
        'motif',
        'user_id'
    ];
    
    protected $casts = [
        'date_ajustement' => 'date',
    ];
    
    public function article()
    {
        return $this->belongsTo(Article::class);
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function ecart()
    {
        return $this->quantite_apres - $this->quantite_avant;
    }
}

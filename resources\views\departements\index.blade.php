@extends('admin.admin_dashboard')
@section('admin')

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

<style>
    :root {
        --primary: #4361ee;
        --primary-light: #4895ef;
        --secondary: #3f37c9;
        --accent: #4cc9f0;
        --success: #4ade80;
        --danger: #f87171;
        --warning: #fbbf24;
        --info: #60a5fa;
        --dark: #374151;
        --light: #f9fafb;
    }

    /* Cards et conteneurs */
    .card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        padding: 1.2rem;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header .header-icon {
        font-size: 1.4rem;
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 1.8rem;
    }

    /* Fil d'ariane */
    .page-breadcrumb {
        background: linear-gradient(to right, #f9fafb, #f3f4f6);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        border-left: 4px solid var(--primary);
    }

    .breadcrumb-item a {
        color: var(--primary);
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .breadcrumb-item a:hover {
        color: var(--secondary);
        text-decoration: none;
    }

    .breadcrumb-item a i {
        margin-right: 0.5rem;
    }

    .breadcrumb-item.active {
        color: var(--dark);
        font-weight: 600;
    }

    /* Boutons */
    .btn {
        border-radius: 10px;
        padding: 0.6rem 1.2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn i {
        font-size: 1.1rem;
    }

    .btn-primary {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(145deg, var(--secondary), var(--primary));
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .btn-success {
        background-color: var(--success);
        border: none;
    }

    .btn-danger {
        background-color: var(--danger);
        border: none;
    }

    .btn-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .btn-action {
        width: 36px;
        height: 36px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        margin-right: 0.4rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-action i {
        font-size: 1.2rem;
    }

    /* Tableau */
    .table {
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: -8px;
    }

    .table th {
        background-color: #f8fafc;
        padding: 1rem;
        font-weight: 600;
        color: var(--dark);
        border: none;
        vertical-align: middle;
        white-space: nowrap;
    }

    .table td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid #f1f5f9;
        border-bottom: 1px solid #f1f5f9;
        color: #4b5563;
    }

    .table tr {
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    }

    .table tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        background-color: #f9fafb;
    }

    .table td:first-child {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        border-left: 1px solid #f1f5f9;
    }

    .table td:last-child {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        border-right: 1px solid #f1f5f9;
    }

    /* Badge de statut */
    .badge {
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.85rem;
    }

    /* Animations */
    .animate-fade-in {
        animation: fadeIn 0.6s ease-out forwards;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Champs de recherche */
    .search-container {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .search-input {
        padding: 0.6rem 1rem 0.6rem 3rem;
        border-radius: 10px;
        border: 1px solid #e2e8f0;
        width: 100%;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #94a3b8;
    }

    /* Indicateur de nombre d'entrées */
    .entries-info {
        color: #64748b;
        margin-bottom: 1rem;
    }

    /* Couleur de département */
    .dept-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }

    /* Animation des lignes du tableau */
    .table tbody tr {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
    }

    @media (max-width: 767.98px) {
        .card-body {
            padding: 1rem;
        }

        .page-breadcrumb {
            padding: 0.75rem 1rem;
        }

        .table td, .table th {
            padding: 0.75rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
        }
    }
</style>

<div class="page-content animate__animated animate__fadeIn">
    <!-- Fil d'ariane amélioré -->
    <div class="page-breadcrumb d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3 d-flex align-items-center">
            <i class='bx bx-building-house me-2 text-primary fs-5'></i>
            <span class="fw-bold">Directions / Structures</span>
        </div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}">
                            <i class="bx bx-home-alt"></i> Accueil
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Liste des Directions / Structures
                    </li>
                </ol>
            </nav>
        </div>
        <div class="ms-auto">
            <a href="{{ route('departements.create') }}" class="btn btn-primary animate__animated animate__pulse animate__infinite animate__slow">
                <i class='bx bx-plus-circle'></i> Ajouter une Direction
            </a>
        </div>
    </div>

    <!-- Carte principale des départements -->
    <div class="card animate__animated animate__fadeInUp">
        <div class="card-header">
            <div>
                <i class='bx bx-list-ul header-icon'></i>
                Liste des Directions et Structures
            </div>
            <div class="d-flex align-items-center">
                <a href="{{ route('departements.deleted') }}" class="btn btn-light btn-sm me-2">
                    <i class='bx bx-building-x'></i> Directions désactivées
                </a>
                <span class="badge bg-light text-dark">
                    Total: {{ $departements->count() }}
                </span>
            </div>
        </div>
        <div class="card-body">
            <!-- Barre de recherche -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="search-container">
                        <i class='bx bx-search search-icon'></i>
                        <input type="text" id="searchInput" class="search-input" placeholder="Rechercher une direction...">
                    </div>
                </div>
                <div class="col-md-6 d-flex justify-content-md-end align-items-center">
                    <div class="entries-info">
                        Affichage de <span class="fw-bold">{{ $departements->count() }}</span> directions
                    </div>
                </div>
            </div>

            <!-- Tableau amélioré -->
            <div class="table-responsive">
                <table id="departmentsTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th width="70px">
                                <i class='bx bx-hash me-1 text-primary'></i> ID
                            </th>
                            <th>
                                <i class='bx bx-building me-1 text-primary'></i> Nom Direction/Structure
                            </th>
                            <th>
                                <i class='bx bx-calendar-plus me-1 text-primary'></i> Date Création
                            </th>
                            <th>
                                <i class='bx bx-calendar-edit me-1 text-primary'></i> Date Modification
                            </th>
                            <th width="150px" class="text-center">
                                <i class='bx bx-cog me-1 text-primary'></i> Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($departements as $key => $item)
                        <tr style="animation-delay: {{ $key * 0.05 }}s">
                            <td class="fw-bold">
                                {{ $key+1 }}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="dept-color" style="background-color: {{ '#' . substr(md5($item->nom_departement), 0, 6) }}"></span>
                                    <span class="fw-medium">{{ $item->nom_departement }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    <i class='bx bx-calendar me-1'></i>
                                    {{ \Illuminate\Support\Carbon::parse($item->created_at)->format('d-m-Y') }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">
                                    <i class='bx bx-refresh me-1'></i>
                                    {{ \Illuminate\Support\Carbon::parse($item->updated_at)->format('d-m-Y') }}
                                </span>
                            </td>
                            <td class="text-center">
                                <a href="{{ route('departement.edit', $item->id) }}" title="Modifier" class="btn btn-success btn-action">
                                    <i class='bx bx-edit-alt'></i>
                                </a>
                                <button type="button" title="Supprimer" class="btn btn-danger btn-action delete-btn" data-id="{{ $item->id }}" data-name="{{ $item->nom_departement }}">
                                    <i class='bx bx-trash-alt'></i>
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Message si aucun département -->
            @if($departements->count() == 0)
            <div class="text-center py-5">
                <i class='bx bx-folder-open text-muted' style="font-size: 4rem"></i>
                <p class="mt-3 text-muted">Aucune direction ou structure n'a été trouvée</p>
                <a href="{{ route('departements.create') }}" class="btn btn-primary mt-2">
                    <i class='bx bx-plus-circle'></i> Ajouter une Direction
                </a>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Arrêter l'animation infinie après quelques secondes
        setTimeout(function() {
            document.querySelector('.animate__infinite').classList.remove('animate__infinite');
        }, 3000);
        
        // Fonctionnalité de recherche
        const searchInput = document.getElementById('searchInput');
        const table = document.getElementById('departmentsTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        
        searchInput.addEventListener('keyup', function() {
            const query = searchInput.value.toLowerCase();
            
            for (let i = 0; i < rows.length; i++) {
                const deptName = rows[i].getElementsByTagName('td')[1].textContent.toLowerCase();
                
                if (deptName.indexOf(query) > -1) {
                    rows[i].style.display = "";
                } else {
                    rows[i].style.display = "none";
                }
            }
        });
        
        // Effet hover sur les boutons d'action
        const actionButtons = document.querySelectorAll('.btn-action');
        actionButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.querySelector('i').classList.add('animate__animated', 'animate__heartBeat');
            });
            
            button.addEventListener('mouseleave', function() {
                this.querySelector('i').classList.remove('animate__animated', 'animate__heartBeat');
            });
        });
    });
</script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion de la suppression d'un département
        const deleteBtns = document.querySelectorAll('.delete-btn');
        
        deleteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const deptId = this.getAttribute('data-id');
                const deptName = this.getAttribute('data-name');
                
                Swal.fire({
                    title: 'Désactiver cette direction?',
                    html: `Voulez-vous vraiment désactiver <strong>${deptName}</strong> ?<br>Cette direction sera déplacée vers la liste des directions désactivées.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#f87171',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Oui, désactiver',
                    cancelButtonText: 'Annuler',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Créer une requête AJAX pour désactiver le département
                        const xhr = new XMLHttpRequest();
                        xhr.open('POST', `/departements/delete/${deptId}`);
                        xhr.setRequestHeader('Content-Type', 'application/json');
                        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                        xhr.responseType = 'json';
                        
                        // Ajouter le paramètre _method pour simuler une requête DELETE
                        const data = JSON.stringify({
                            _method: 'DELETE'
                        });
                        
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                Swal.fire({
                                    title: 'Désactivé!',
                                    text: `La direction ${deptName} a été désactivée avec succès.`,
                                    icon: 'success',
                                    confirmButtonColor: '#4361ee'
                                }).then(() => {
                                    // Recharger la page pour mettre à jour la liste
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Erreur!',
                                    text: xhr.response?.message || 'Une erreur est survenue lors de la désactivation.',
                                    icon: 'error',
                                    confirmButtonColor: '#4361ee'
                                });
                            }
                        };
                        
                        xhr.onerror = function() {
                            Swal.fire({
                                title: 'Erreur!',
                                text: 'Une erreur de connexion est survenue.',
                                icon: 'error',
                                confirmButtonColor: '#4361ee'
                            });
                        };
                        
                        xhr.send(data);
                    }
                });
            });
        });
    });
</script>

@endsection
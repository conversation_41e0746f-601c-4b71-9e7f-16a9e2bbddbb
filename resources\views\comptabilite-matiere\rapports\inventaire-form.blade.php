<!doctype html>
<html lang="fr">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!--favicon-->
    <link rel="icon" href="{{ asset('backend/assets/images/favicon-32x32.png') }}" type="image/png"/>
    <!--plugins-->
    <link href="{{ asset('backend/assets/plugins/vectormap/jquery-jvectormap-2.0.2.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/simplebar/css/simplebar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/metismenu/css/metisMenu.min.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet"/>
    <!-- loader-->
    <link href="{{ asset('backend/assets/css/pace.min.css') }}" rel="stylesheet"/>
    <script src="{{ asset('backend/assets/js/pace.min.js') }}"></script>
    <!-- Bootstrap CSS -->
    <link href="{{ asset('backend/assets/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/bootstrap-extended.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/icons.css') }}" rel="stylesheet">
    <!-- Theme Style CSS -->
    <link rel="stylesheet" href="{{ asset('backend/assets/css/dark-theme.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/semi-dark.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/header-colors.css') }}"/>
    <!-- Toster CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" >
    <!-- Animate CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <!-- Custom Sidebar CSS -->
    <link rel="stylesheet" href="{{ asset('css/custom-sidebar.css') }}">
    <!-- End Custom Sidebar CSS -->
    <title>Rapport d'Inventaire</title>
    <style>
        .form-card {
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            border: none;
            transition: all 0.3s ease;
        }
        .form-card:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .form-card .card-header {
            background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px 25px;
            border-bottom: none;
        }
        .info-card .card-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        }
        .form-card .card-body {
            padding: 30px;
        }
        .btn-back {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 50px;
            transition: all 0.3s;
            background-color: #f8f9fc;
            color: #5a5c69;
            border: 1px solid #e2e8f0;
        }
        .btn-back:hover {
            background-color: #eaecf4;
            color: #3a3b45;
            transform: translateY(-2px);
        }
        .btn-action {
            padding: 8px 15px;
            border-radius: 50px;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-danger {
            background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
            border: none;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #858796 0%, #5a5c69 100%);
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border: none;
        }
        .badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.75rem;
        }
        .badge-success {
            background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        }
        .badge-warning {
            background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
            color: #fff;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .table thead th {
            background-color: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
            font-weight: 600;
            color: #4e73df;
            padding: 15px;
        }
        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-color: #e3e6f0;
        }
        .table tbody tr:hover {
            background-color: #f8f9fc;
        }
        .info-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(231, 74, 59, 0.1);
            color: #e74a3b;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        .info-list li {
            margin-bottom: 10px;
            position: relative;
            padding-left: 25px;
        }
        .info-list li:before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: #e74a3b;
        }
        .alert-custom {
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: none;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .alert-warning {
            background-color: rgba(246, 194, 62, 0.1);
            border-left: 4px solid #f6c23e;
            color: #533f03;
        }
        .alert-info {
            background-color: rgba(54, 185, 204, 0.1);
            border-left: 4px solid #36b9cc;
            color: #1a4a52;
        }
        .dataTables_wrapper .dataTables_length, 
        .dataTables_wrapper .dataTables_filter {
            margin-bottom: 20px;
        }
        .dataTables_wrapper .dataTables_length select, 
        .dataTables_wrapper .dataTables_filter input {
            border-radius: 10px;
            padding: 8px 12px;
            border: 1px solid #e3e6f0;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button {
            border-radius: 5px;
            margin: 0 2px;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #4e73df;
            border-color: #4e73df;
            color: white !important;
        }
    </style>
</head>

<body>
    <!--wrapper-->
    <div class="wrapper">
        <!--sidebar wrapper -->
        @include('admin.body.sidebar')
        <!--end sidebar wrapper -->
        <!--start header -->
        @include('admin.body.header')
        <!--end header -->
        <!--start page wrapper -->
        <div class="page-wrapper">
            <div class="page-content">
                <!--breadcrumb-->
                <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
                    <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
                    <div class="ps-3">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0 p-0">
                                <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a></li>
                                <li class="breadcrumb-item"><a href="{{ route('rapports.index') }}">Rapports</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Rapport d'Inventaire</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ route('rapports.index') }}" class="btn btn-back">
                            <i class="bx bx-arrow-back"></i> Retour aux rapports
                        </a>
                    </div>
                </div>
                <!--end breadcrumb-->

                <div class="row">
                    <div class="col-12">
                        <div class="card form-card animate__animated animate__fadeIn">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 text-white"><i class="bx bx-list-check me-2"></i>Sélection de l'inventaire</h5>
                            </div>
                            <div class="card-body">
                                @if(session('error'))
                                    <div class="alert alert-danger border-0 bg-danger alert-dismissible fade show">
                                        <div class="text-white">{{ session('error') }}</div>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                @endif
                                
                                @if($inventaires->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="dataTable" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th>Référence</th>
                                                    <th>Date de début</th>
                                                    <th>Date de fin</th>
                                                    <th>Statut</th>
                                                    <th>Responsable</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($inventaires as $inventaire)
                                                <tr>
                                                    <td><strong>{{ $inventaire->reference }}</strong></td>
                                                    <td>{{ \Carbon\Carbon::parse($inventaire->date_debut)->format('d/m/Y') }}</td>
                                                    <td>
                                                        @if($inventaire->date_fin)
                                                            {{ \Carbon\Carbon::parse($inventaire->date_fin)->format('d/m/Y') }}
                                                        @else
                                                            <span class="text-muted">Non clôturé</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($inventaire->statut === 'cloture')
                                                            <span class="badge bg-success">Clôturé</span>
                                                        @elseif($inventaire->statut === 'en_cours')
                                                            <span class="badge bg-warning text-dark">En cours</span>
                                                        @else
                                                            <span class="badge bg-secondary">{{ $inventaire->statut }}</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $inventaire->user->name }}</td>
                                                    <td>
                                                        @if($inventaire->statut === 'cloture')
                                                            <a href="{{ route('rapports.inventaire', $inventaire->id) }}" class="btn btn-sm btn-danger btn-action">
                                                                <i class="bx bx-file"></i> Générer
                                                            </a>
                                                            <a href="{{ route('rapports.inventaire', ['id' => $inventaire->id, 'export_pdf' => 1]) }}" class="btn btn-sm btn-secondary btn-action ms-1">
                                                                <i class="bx bx-file-pdf"></i> PDF
                                                            </a>
                                                        @else
                                                            <button class="btn btn-sm btn-secondary btn-action" disabled>
                                                                <i class="bx bx-lock"></i> Non disponible
                                                            </button>
                                                        @endif
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="alert alert-custom alert-info">
                                        <div class="info-icon">
                                            <i class="bx bx-info-circle"></i>
                                        </div>
                                        <div>Aucun inventaire disponible. Veuillez d'abord créer et clôturer un inventaire.</div>
                                    </div>
                                    <a href="{{ route('inventaires.create') }}" class="btn btn-primary btn-action mt-3">
                                        <i class="bx bx-plus"></i> Créer un nouvel inventaire
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card form-card info-card animate__animated animate__fadeIn">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 text-white"><i class="bx bx-info-circle me-2"></i>Informations sur le Rapport d'Inventaire</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-7">
                                        <h5 class="mb-3">Fonctionnalités du rapport</h5>
                                        <p class="mb-3">Le <strong>Rapport d'Inventaire</strong> présente les résultats d'un inventaire clôturé, mettant en évidence les écarts entre le stock théorique et le stock réel.</p>
                                        
                                        <h6 class="mb-2">Ce rapport permet :</h6>
                                        <ul class="info-list">
                                            <li>D'identifier les articles présentant des écarts de stock</li>
                                            <li>De quantifier les excédents et les manquants</li>
                                            <li>D'analyser les causes potentielles des écarts</li>
                                            <li>De prendre des mesures correctives appropriées</li>
                                            <li>De conserver un historique des inventaires pour des fins d'audit</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="alert alert-custom alert-warning mb-4">
                                            <div class="info-icon">
                                                <i class="bx bx-error-circle"></i>
                                            </div>
                                            <div>
                                                <strong>Note importante :</strong> Seuls les inventaires clôturés peuvent faire l'objet d'un rapport. Un inventaire en cours ne peut pas être analysé car les données sont susceptibles de changer.
                                            </div>
                                        </div>
                                        
                                        <div class="card border-0 shadow-sm p-3 bg-light">
                                            <h6 class="mb-3"><i class="bx bx-bulb text-warning me-2"></i>Conseils d'utilisation</h6>
                                            <ol class="mb-0 ps-3">
                                                <li class="mb-2">Sélectionnez un inventaire clôturé dans la liste</li>
                                                <li class="mb-2">Cliquez sur "Générer" pour visualiser le rapport</li>
                                                <li>Utilisez l'option "PDF" pour exporter le rapport</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end page wrapper -->
        <!--start overlay-->
        <div class="overlay toggle-icon"></div>
        <!--end overlay-->
        <!--Start Back To Top Button-->
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
        <!--End Back To Top Button-->
        <!--start footer -->
        @include('admin.body.footer')
        <!--end footer -->
    </div>
    <!--end wrapper-->

    <!-- Bootstrap JS -->
    <script src="{{ asset('backend/assets/js/bootstrap.bundle.min.js') }}"></script>
    <!--plugins-->
    <script src="{{ asset('backend/assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/simplebar/js/simplebar.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/metismenu/js/metisMenu.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
    <!--app JS-->
    <script src="{{ asset('backend/assets/js/app.js') }}"></script>
    <!-- Toastr js -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        @if(Session::has('message'))
        var type = "{{ Session::get('alert-type','info') }}"
        switch(type){
            case 'info':
            toastr.info(" {{ Session::get('message') }} ");
            break;

            case 'success':
            toastr.success(" {{ Session::get('message') }} ");
            break;

            case 'warning':
            toastr.warning(" {{ Session::get('message') }} ");
            break;

            case 'error':
            toastr.error(" {{ Session::get('message') }} ");
            break; 
        }
        @endif
    </script>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "order": [[ 2, "desc" ]],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
                },
                "responsive": true,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Tous"]]
            });
            
            // Animation des cartes au scroll
            function animateOnScroll() {
                $('.animate__animated').each(function() {
                    var position = $(this).offset().top;
                    var scroll = $(window).scrollTop();
                    var windowHeight = $(window).height();
                    
                    if (scroll + windowHeight > position) {
                        $(this).addClass('animate__fadeIn');
                    }
                });
            }
            
            // Exécuter au chargement et au scroll
            animateOnScroll();
            $(window).scroll(animateOnScroll);
        });
    </script>
</body>
</html>

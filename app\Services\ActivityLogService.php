<?php

namespace App\Services;

use App\Models\ActivityLog;
use Illuminate\Support\Facades\Auth;

class ActivityLogService
{
    /**
     * Module actuel
     * 
     * @var string
     */
    protected $module;

    /**
     * Constructeur
     * 
     * @param string $module
     */
    public function __construct($module = 'general')
    {
        $this->module = $module;
    }

    /**
     * Définir le module
     * 
     * @param string $module
     * @return $this
     */
    public function setModule($module)
    {
        $this->module = $module;
        return $this;
    }

    /**
     * Journaliser une action de création
     * 
     * @param string $description
     * @param mixed $data
     * @return ActivityLog
     */
    public function logCreation($description, $data = null)
    {
        return $this->log('create', $description, null, $data);
    }

    /**
     * Journaliser une action de mise à jour
     * 
     * @param string $description
     * @param mixed $beforeData
     * @param mixed $afterData
     * @return ActivityLog
     */
    public function logUpdate($description, $beforeData = null, $afterData = null)
    {
        return $this->log('update', $description, $beforeData, $afterData);
    }

    /**
     * Journaliser une action de suppression
     * 
     * @param string $description
     * @param mixed $data
     * @return ActivityLog
     */
    public function logDeletion($description, $data = null)
    {
        return $this->log('delete', $description, $data, null);
    }

    /**
     * Journaliser une action d'accès
     * 
     * @param string $description
     * @param mixed $data
     * @return ActivityLog
     */
    public function logAccess($description, $data = null)
    {
        return $this->log('access', $description, null, $data);
    }

    /**
     * Journaliser une action d'export
     * 
     * @param string $description
     * @param mixed $data
     * @return ActivityLog
     */
    public function logExport($description, $data = null)
    {
        return $this->log('export', $description, null, $data);
    }

    /**
     * Journaliser une action personnalisée
     * 
     * @param string $action
     * @param string $description
     * @param mixed $beforeData
     * @param mixed $afterData
     * @return ActivityLog
     */
    public function log($action, $description, $beforeData = null, $afterData = null)
    {
        return ActivityLog::log(
            $this->module,
            $action,
            $description,
            $beforeData,
            $afterData
        );
    }
}

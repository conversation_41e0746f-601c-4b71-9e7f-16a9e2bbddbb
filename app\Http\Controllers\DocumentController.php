<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Reception;

class DocumentController extends Controller
{
    /**
     * Affiche un document PV de réception avec une mise en page améliorée
     */
    public function showPv($filename)
    {
        // Récupérer le chemin complet du fichier
        $filePath = 'upload/reception/' . $filename;
        
        // Vérifier si le fichier existe
        if (!file_exists(public_path($filePath))) {
            abort(404, 'Document non trouvé');
        }
        
        // Déterminer le type de fichier
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        $isPdf = strtolower($extension) === 'pdf';
        
        // Récupérer la réception associée à ce PV (si disponible)
        $reception = Reception::where('pv_reception', 'LIKE', '%' . $filename . '%')->first();
        
        return view('documents.show_pv', [
            'filePath' => asset($filePath),
            'fileName' => $filename,
            'isPdf' => $isPdf,
            'reception' => $reception
        ]);
    }
}

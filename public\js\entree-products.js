/**
 * Script de gestion des produits pour le formulaire d'entrée groupée
 */

// S'assurer que jQuery est chargé
document.addEventListener('DOMContentLoaded', function() {
    if (typeof jQuery === 'undefined') {
        console.error("jQuery n'est pas chargé!");
        return;
    }
    
    // Variable globale pour stocker les produits
    window.addedProducts = [];
    
    // Référence aux éléments du DOM
    const designationSelect = $('#designation_matiere');
    const natureSelect = $('#nature');
    const formatSelect = $('#format');
    const etatSelect = $('#etat');
    const quantiteInput = $('#quantite');
    const prixInput = $('#prix_unitaire');
    const compteInput = $('#compte_matiere');
    const dateOemInput = $('#date_oem');
    
    // Boutons
    const addProductBtn = $('#add-product');
    const validateAllBtn = $('#validate-all');
    const finalSubmitBtn = $('#final-submit');
    
    // Tableau de produits
    const productsTable = $('#products-table');
    const productsCount = $('#products-count');
    const totalAmount = $('#total-amount');
    
    // Événement d'ajout d'un produit
    addProductBtn.on('click', function() {
        console.log('Bouton Ajouter ce produit cliqué');
        
        // Vérifier que tous les champs requis sont remplis
        const requiredFields = ['designation_matiere', 'nature', 'format', 'etat', 'quantite', 'prix_unitaire'];
        let isValid = true;
        
        requiredFields.forEach(field => {
            const input = $('#' + field);
            console.log('Vérification du champ:', field, input.length ? input.val() : 'champ non trouvé');
            
            if (!input.length) {
                console.error(`Le champ ${field} n'existe pas dans le DOM`);
                isValid = false;
                return;
            }
            
            if (!input.val()) {
                isValid = false;
                input.addClass('is-invalid');
            } else {
                input.removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            showAlert('Veuillez remplir tous les champs obligatoires du produit.', 'danger');
            return;
        }
        
        // Récupérer les valeurs du produit
        const quantite = parseFloat(quantiteInput.val());
        const prix_unitaire = parseFloat(prixInput.val());
        const montant_total = quantite * prix_unitaire;
        
        const product = {
            id: Date.now(),
            date_oem: dateOemInput.val(),
            designation_matiere: designationSelect.val(),
            compte_matiere: compteInput.val(),
            nature: natureSelect.val(),
            format: formatSelect.val(),
            etat: etatSelect.val(),
            quantite: quantite,
            prix_unitaire: prix_unitaire,
            montant_total: montant_total
        };
        
        console.log('Produit à ajouter:', product);
        
        // Ajouter le produit au tableau
        window.addedProducts.push(product);
        console.log('Produits après ajout:', window.addedProducts);
        
        // Rafraîchir le tableau des produits
        refreshProductsTable();
        
        // Réinitialiser le formulaire de produit
        resetProductForm();
        
        showAlert('Produit ajouté avec succès!', 'success');
        
        // Passer à l'onglet récapitulatif après avoir ajouté un produit
        setTimeout(() => {
            document.getElementById('summary-tab').click();
        }, 500);
    });
    
    // Fonction pour rafraîchir le tableau des produits
    function refreshProductsTable() {
        console.log('refreshProductsTable appelé', window.addedProducts);
        const tbody = $('#products-table tbody');
        if (!tbody.length) {
            console.error("Le tableau #products-table tbody n'existe pas dans le DOM");
            return;
        }
        
        tbody.empty();
        
        if (window.addedProducts && window.addedProducts.length > 0) {
            $('#products-container').removeClass('d-none');
            $('#validate-all').removeClass('d-none');
            
            let totalAmount = 0;
            
            window.addedProducts.forEach((product, index) => {
                const productTotal = product.montant_total;
                totalAmount += productTotal;
                
                const row = `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${product.designation_matiere}</td>
                        <td>${product.compte_matiere}</td>
                        <td>${product.nature}</td>
                        <td>${product.format}</td>
                        <td>${product.etat}</td>
                        <td>${product.quantite}</td>
                        <td>${product.prix_unitaire} FCFA</td>
                        <td>${productTotal} FCFA</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-primary edit-product" data-id="${product.id}">
                                <i class="bx bx-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger delete-product" data-id="${product.id}">
                                <i class="bx bx-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                
                tbody.append(row);
            });
            
            $('#products-count').text(window.addedProducts.length);
            $('#total-amount').text(totalAmount + ' FCFA');
            
            // Ajouter le bouton "Continuer" si c'est le premier produit
            if (window.addedProducts.length === 1 && !$('#continue-adding').length) {
                const continueBtn = $('<button>', {
                    id: 'continue-adding',
                    type: 'button',
                    class: 'btn btn-outline-primary ms-2',
                    html: '<i class="bx bx-plus"></i> Ajouter un autre produit',
                    click: function() {
                        document.getElementById('product-tab').click();
                    }
                });
                $('#summary-actions').append(continueBtn);
            }
        } else {
            // Même si aucun produit n'est ajouté, on garde le conteneur visible mais on affiche un message
            tbody.html('<tr><td colspan="11" class="text-center">Aucun produit ajouté</td></tr>');
            $('#validate-all').addClass('d-none');
            $('#final-submit').addClass('d-none');
            $('#continue-adding').remove();
        }
    }
    
    // Réinitialiser le formulaire de produit
    function resetProductForm() {
        designationSelect.val('').trigger('change');
        natureSelect.val('').trigger('change');
        formatSelect.val('').trigger('change');
        etatSelect.val('').trigger('change');
        compteInput.val('');
        $('#compte_matiere_display').val('');
        quantiteInput.val('');
        prixInput.val('');
        
        // Supprimer les états d'erreur
        $('.is-invalid').removeClass('is-invalid');
    }
    
    // Supprimer un produit
    $(document).on('click', '.delete-product', function() {
        const productId = $(this).data('id');
        window.addedProducts = window.addedProducts.filter(product => product.id !== productId);
        refreshProductsTable();
        showAlert('Produit supprimé avec succès!', 'warning');
    });
    
    // Éditer un produit
    $(document).on('click', '.edit-product', function() {
        const productId = $(this).data('id');
        const product = window.addedProducts.find(p => p.id === productId);
        
        if (product) {
            // Remplir le formulaire avec les données du produit
            designationSelect.val(product.designation_matiere).trigger('change');
            natureSelect.val(product.nature).trigger('change');
            formatSelect.val(product.format).trigger('change');
            etatSelect.val(product.etat).trigger('change');
            quantiteInput.val(product.quantite);
            prixInput.val(product.prix_unitaire);
            
            // Supprimer le produit de la liste
            window.addedProducts = window.addedProducts.filter(p => p.id !== productId);
            refreshProductsTable();
            
            // Passer à l'onglet produit
            document.getElementById('product-tab').click();
            
            showAlert('Vous pouvez modifier le produit et l\'ajouter à nouveau.', 'info');
        }
    });
    
    // Valider tous les produits
    validateAllBtn.on('click', function() {
        if (!window.addedProducts || window.addedProducts.length === 0) {
            showAlert('Aucun produit n\'a été ajouté. Veuillez ajouter au moins un produit.', 'warning');
            return;
        }
        
        $('#final-submit').removeClass('d-none');
        $(this).addClass('d-none');
        
        showAlert('Veuillez vérifier le récapitulatif et confirmer l\'enregistrement.', 'info');
    });
    
    // Soumettre le formulaire final
    finalSubmitBtn.on('click', function() {
        if (!window.addedProducts || window.addedProducts.length === 0) {
            showAlert('Aucun produit n\'a été ajouté. Veuillez ajouter au moins un produit.', 'warning');
            return;
        }
        
        const entryForm = document.getElementById('entry-form');
        if (!entryForm) {
            showAlert('Erreur: Formulaire non trouvé!', 'danger');
            return;
        }
        
        // Créer des champs cachés pour chaque produit
        window.addedProducts.forEach((product, index) => {
            createHiddenInput(`products[${index}][designation_matiere]`, product.designation_matiere);
            createHiddenInput(`products[${index}][compte_matiere]`, product.compte_matiere);
            createHiddenInput(`products[${index}][nature]`, product.nature);
            createHiddenInput(`products[${index}][format]`, product.format);
            createHiddenInput(`products[${index}][etat]`, product.etat);
            createHiddenInput(`products[${index}][quantite]`, product.quantite);
            createHiddenInput(`products[${index}][prix_unitaire]`, product.prix_unitaire);
            createHiddenInput(`products[${index}][date_oem]`, product.date_oem);
        });
        
        // Indiquer que c'est un enregistrement groupé
        const groupedField = document.createElement('input');
        groupedField.type = 'hidden';
        groupedField.name = 'is_grouped';
        groupedField.value = '1';
        entryForm.appendChild(groupedField);
        
        // Soumettre le formulaire
        entryForm.submit();
        
        showAlert('Enregistrement en cours...', 'info');
    });
    
    // Fonction pour créer un champ caché
    function createHiddenInput(name, value) {
        const entryForm = document.getElementById('entry-form');
        if (!entryForm) {
            console.error("Le formulaire #entry-form n'existe pas dans le DOM");
            return;
        }
        const hiddenField = document.createElement('input');
        hiddenField.type = 'hidden';
        hiddenField.name = name;
        hiddenField.value = value;
        entryForm.appendChild(hiddenField);
    }
    
    // Fonction pour afficher une alerte
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        const alertContainer = document.getElementById('alert-container');
        if (alertContainer) {
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.classList.remove('show');
                setTimeout(() => {
                    alertDiv.remove();
                }, 150);
            }, 5000);
        } else {
            console.error("Container d'alerte non trouvé");
        }
    }
    
    // Mise à jour du récapitulatif
    $('#summary-tab').on('shown.bs.tab', function() {
        updateSummary();
    });
    
    function updateSummary() {
        const reference = $('#reference').val() || '-';
        const observations = $('#observations').val() || '-';
        const pieceFile = $('#piece_justificative').val() ? $('#piece_justificative').val().split('\\').pop() : '-';
        const dateOem = $('#date_oem').val() || '-';
        
        $('#summary-reference').text(reference);
        $('#summary-observations').text(observations);
        $('#summary-piece').text(pieceFile);
        $('#summary-date-oem').text(dateOem);
        $('#summary-total-products').text(window.addedProducts ? window.addedProducts.length : 0);
        
        refreshProductsTable();
    }
    
    // Initialiser le tableau
    refreshProductsTable();
});

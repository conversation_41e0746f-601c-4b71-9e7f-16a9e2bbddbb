<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Inventaire;
use App\Models\InventaireDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InventaireDetailController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @param  int  $inventaireId
     * @return \Illuminate\Http\Response
     */
    public function index($inventaireId)
    {
        $inventaire = Inventaire::with(['details', 'details.article', 'details.article.unite'])
            ->findOrFail($inventaireId);
        
        return view('comptabilite-matiere.inventaire-details.index', compact('inventaire'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param  int  $inventaireId
     * @return \Illuminate\Http\Response
     */
    public function create($inventaireId)
    {
        $inventaire = Inventaire::findOrFail($inventaireId);
        
        // Vérifier si l'inventaire est encore en cours
        if ($inventaire->statut !== 'en_cours') {
            return redirect()->route('inventaires.show', $inventaireId)
                ->with('error', 'Cet inventaire est déjà clôturé et ne peut plus être modifié.');
        }
        
        // Récupérer les articles qui ne sont pas encore dans l'inventaire
        $articlesExistants = $inventaire->details()->pluck('article_id')->toArray();
        $articles = Article::whereNotIn('id', $articlesExistants)
            ->orderBy('designation', 'asc')
            ->pluck('designation', 'id');
        
        return view('comptabilite-matiere.inventaire-details.create', compact('inventaire', 'articles'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $inventaireId
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $inventaireId)
    {
        $inventaire = Inventaire::findOrFail($inventaireId);
        
        // Vérifier si l'inventaire est encore en cours
        if ($inventaire->statut !== 'en_cours') {
            return redirect()->route('inventaires.show', $inventaireId)
                ->with('error', 'Cet inventaire est déjà clôturé et ne peut plus être modifié.');
        }
        
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'stock_reel' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->route('inventaire-details.create', $inventaireId)
                ->withErrors($validator)
                ->withInput();
        }
        
        // Vérifier si l'article existe déjà dans l'inventaire
        $existant = $inventaire->details()->where('article_id', $request->article_id)->first();
        if ($existant) {
            return redirect()->route('inventaire-details.create', $inventaireId)
                ->with('error', 'Cet article est déjà présent dans l\'inventaire.')
                ->withInput();
        }
        
        // Récupérer le stock théorique de l'article
        $article = Article::findOrFail($request->article_id);
        $stockTheorique = $article->stockActuel();
        
        // Créer le détail d'inventaire
        InventaireDetail::create([
            'inventaire_id' => $inventaireId,
            'article_id' => $request->article_id,
            'stock_theorique' => $stockTheorique,
            'stock_reel' => $request->stock_reel,
        ]);

        return redirect()->route('inventaire-details.index', $inventaireId)
            ->with('success', 'Article ajouté à l\'inventaire avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $inventaireId
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($inventaireId, $id)
    {
        $inventaire = Inventaire::findOrFail($inventaireId);
        $detail = InventaireDetail::with(['article', 'article.unite'])
            ->where('inventaire_id', $inventaireId)
            ->findOrFail($id);
        
        $ecart = $detail->calculerEcart();
        
        return view('comptabilite-matiere.inventaire-details.show', compact('inventaire', 'detail', 'ecart'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $inventaireId
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($inventaireId, $id)
    {
        $inventaire = Inventaire::findOrFail($inventaireId);
        
        // Vérifier si l'inventaire est encore en cours
        if ($inventaire->statut !== 'en_cours') {
            return redirect()->route('inventaires.show', $inventaireId)
                ->with('error', 'Cet inventaire est déjà clôturé et ne peut plus être modifié.');
        }
        
        $detail = InventaireDetail::with('article')
            ->where('inventaire_id', $inventaireId)
            ->findOrFail($id);
        
        return view('comptabilite-matiere.inventaire-details.edit', compact('inventaire', 'detail'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $inventaireId
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $inventaireId, $id)
    {
        $inventaire = Inventaire::findOrFail($inventaireId);
        
        // Vérifier si l'inventaire est encore en cours
        if ($inventaire->statut !== 'en_cours') {
            return redirect()->route('inventaires.show', $inventaireId)
                ->with('error', 'Cet inventaire est déjà clôturé et ne peut plus être modifié.');
        }
        
        $detail = InventaireDetail::where('inventaire_id', $inventaireId)->findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'stock_reel' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->route('inventaire-details.edit', [$inventaireId, $id])
                ->withErrors($validator)
                ->withInput();
        }

        $detail->update([
            'stock_reel' => $request->stock_reel,
        ]);

        return redirect()->route('inventaire-details.index', $inventaireId)
            ->with('success', 'Détail d\'inventaire mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $inventaireId
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($inventaireId, $id)
    {
        $inventaire = Inventaire::findOrFail($inventaireId);
        
        // Vérifier si l'inventaire est encore en cours
        if ($inventaire->statut !== 'en_cours') {
            return redirect()->route('inventaires.show', $inventaireId)
                ->with('error', 'Cet inventaire est déjà clôturé et ne peut plus être modifié.');
        }
        
        $detail = InventaireDetail::where('inventaire_id', $inventaireId)->findOrFail($id);
        $detail->delete();

        return redirect()->route('inventaire-details.index', $inventaireId)
            ->with('success', 'Détail d\'inventaire supprimé avec succès.');
    }
}

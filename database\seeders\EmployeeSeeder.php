<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $employees = [
            [
                'em_id' => 1,
                'em_code' => 'EMP001',
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON><PERSON>',
                'position_held' => 'Directeur Général',
                'dep_id' => 1,
                'em_phone' => '+221 77 123 45 67',
                'em_email' => '<EMAIL>',
                'em_gender' => 'M',
                'statut' => 'actif',
            ],
            [
                'em_id' => 2,
                'em_code' => 'EMP002',
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'position_held' => 'Chef de Service',
                'dep_id' => 2,
                'em_phone' => '+221 77 234 56 78',
                'em_email' => '<EMAIL>',
                'em_gender' => 'F',
                'statut' => 'actif',
            ],
            [
                'em_id' => 3,
                'em_code' => 'EMP003',
                'first_name' => '<PERSON>',
                'last_name' => 'Di<PERSON>',
                'position_held' => 'Chauffeur',
                'dep_id' => 3,
                'em_phone' => '+221 77 345 67 89',
                'em_email' => '<EMAIL>',
                'em_gender' => 'M',
                'statut' => 'actif',
            ],
            [
                'em_id' => 4,
                'em_code' => 'EMP004',
                'first_name' => 'Fatou',
                'last_name' => 'Sow',
                'position_held' => 'Secrétaire',
                'dep_id' => 1,
                'em_phone' => '+221 77 456 78 90',
                'em_email' => '<EMAIL>',
                'em_gender' => 'F',
                'statut' => 'actif',
            ],
            [
                'em_id' => 5,
                'em_code' => 'EMP005',
                'first_name' => 'Moussa',
                'last_name' => 'Ba',
                'position_held' => 'Technicien',
                'dep_id' => 2,
                'em_phone' => '+221 77 567 89 01',
                'em_email' => '<EMAIL>',
                'em_gender' => 'M',
                'statut' => 'actif',
            ],
        ];

        foreach ($employees as $employee) {
            DB::table('employee')->insertOrIgnore($employee);
        }

        // Mettre à jour quelques véhicules pour les affecter à des employés
        $vehicleAssignments = [
            ['immatriculation' => 'AB-123-AA', 'utilisateur' => 1],
            ['immatriculation' => 'AB-456-BB', 'utilisateur' => 2],
            ['immatriculation' => 'AB-789-CC', 'utilisateur' => 3],
        ];

        foreach ($vehicleAssignments as $assignment) {
            DB::table('vehicules')
                ->where('immatriculation', 'like', $assignment['immatriculation'] . '%')
                ->orWhere('immatriculation', 'like', 'AB-%')
                ->limit(1)
                ->update(['utilisateur' => $assignment['utilisateur']]);
        }

        $this->command->info('Employés créés et véhicules affectés avec succès!');
    }
}

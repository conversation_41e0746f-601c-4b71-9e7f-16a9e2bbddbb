<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Nomate;
use Illuminate\Support\Facades\Validator;

class NomateController extends Controller
{
    /**
     * Afficher la liste des éléments de la nomenclature comptable
     */
    public function index(Request $request)
    {
        // Déterminer le nombre d'éléments par page
        $perPage = $request->input('per_page', 20); // Par défaut 20 éléments
        
        // Récupérer tous les éléments actifs
        $nomates = Nomate::where('statut', 'actif')
            ->orderBy('code')
            ->paginate($perPage);
        
        return view('nomate.index', compact('nomates'));
    }
    
    /**
     * Afficher les détails d'un élément spécifique
     */
    public function show($id)
    {
        $nomate = Nomate::findOrFail($id);
        return view('nomate.show', compact('nomate'));
    }
    
    /**
     * Afficher le formulaire de modification d'un élément
     */
    public function edit($id)
    {
        $nomate = Nomate::findOrFail($id);
        return view('nomate.edit', compact('nomate'));
    }
    
    /**
     * Mettre à jour un élément dans la base de données
     */
    public function update(Request $request, $id)
    {
        // Valider les données du formulaire
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|max:50',
            'intitule' => 'required|string|max:255',
            'is_header' => 'boolean',
            'statut' => 'required|in:actif,inactif',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        // Récupérer l'élément à mettre à jour
        $nomate = Nomate::findOrFail($id);
        
        // Vérifier si le code existe déjà pour un autre élément
        $existingNomate = Nomate::where('code', $request->code)
            ->where('id', '!=', $id)
            ->first();
            
        if ($existingNomate) {
            return redirect()->back()
                ->withErrors(['code' => 'Ce code est déjà utilisé par un autre élément.'])
                ->withInput();
        }
        
        // Mettre à jour les données
        $nomate->code = $request->code;
        $nomate->intitule = $request->intitule;
        $nomate->is_header = $request->has('is_header');
        $nomate->statut = $request->statut;
        $nomate->save();
        
        return redirect()->route('nomate.show', $nomate->id)
            ->with('success', 'Le compte NOMATE a été mis à jour avec succès.');
    }
    
    /**
     * Rechercher des éléments par code ou intitulé
     */
    public function search(Request $request)
    {
        $search = $request->input('search');
        $perPage = $request->input('per_page', 20); // Par défaut 20 éléments
        
        $nomates = Nomate::where('statut', 'actif')
            ->where(function($query) use ($search) {
                $query->where('code', 'like', "%{$search}%")
                      ->orWhere('intitule', 'like', "%{$search}%");
            })
            ->orderBy('code')
            ->paginate($perPage);
        
        // Conserver les paramètres lors de la pagination
        // withQueryString() est utilisé dans la vue, donc cette ligne est redondante
        // mais on la garde par sécurité
        $nomates->appends(['search' => $search, 'per_page' => $perPage]);
        
        return view('nomate.index', compact('nomates', 'search'));
    }
}

<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Ajustement;
use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AjustementController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $ajustements = Ajustement::with(['article', 'user'])
            ->orderBy('date_ajustement', 'desc')
            ->get();
        
        return view('comptabilite-matiere.ajustements.index', compact('ajustements'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $articles = Article::orderBy('designation', 'asc')->pluck('designation', 'id');
        return view('comptabilite-matiere.ajustements.create', compact('articles'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'quantite_apres' => 'required|numeric|min:0',
            'date_ajustement' => 'required|date',
            'motif' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->route('ajustements.create')
                ->withErrors($validator)
                ->withInput();
        }
        
        // Récupérer le stock actuel
        $article = Article::findOrFail($request->article_id);
        $quantite_avant = $article->stockActuel();

        Ajustement::create([
            'article_id' => $request->article_id,
            'quantite_avant' => $quantite_avant,
            'quantite_apres' => $request->quantite_apres,
            'date_ajustement' => $request->date_ajustement,
            'motif' => $request->motif,
            'user_id' => Auth::id(),
        ]);

        return redirect()->route('ajustements.index')
            ->with('success', 'Ajustement de stock enregistré avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $ajustement = Ajustement::with(['article', 'article.unite', 'user'])->findOrFail($id);
        $ecart = $ajustement->ecart();
        
        return view('comptabilite-matiere.ajustements.show', compact('ajustement', 'ecart'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // Les ajustements ne devraient pas être modifiables pour garantir la traçabilité
        return redirect()->route('ajustements.index')
            ->with('error', 'Les ajustements de stock ne peuvent pas être modifiés pour garantir la traçabilité.');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Les ajustements ne devraient pas être modifiables pour garantir la traçabilité
        return redirect()->route('ajustements.index')
            ->with('error', 'Les ajustements de stock ne peuvent pas être modifiés pour garantir la traçabilité.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Les ajustements ne devraient pas être supprimables pour garantir la traçabilité
        return redirect()->route('ajustements.index')
            ->with('error', 'Les ajustements de stock ne peuvent pas être supprimés pour garantir la traçabilité.');
    }
}

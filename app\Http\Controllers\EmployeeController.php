<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Departement;
use Illuminate\Http\Request;

class EmployeeController extends Controller
{
    /**
     * Afficher la liste des employés
     */
    public function index(Request $request)
    {
        // Récupérer le terme de recherche s'il existe
        $search = $request->input('search');

        // Initialiser la requête avec la relation departement
        // N'afficher que les employés qui ne sont pas désactivés (statut != 'inactive')
        // ET qui appartiennent uniquement au département DAF (dep_id = 20)
        $query = Employee::with('departement')
            ->where('dep_id', 20) // Filtrer uniquement les employés du département DAF
            ->where(function($q) {
                $q->where('statut', '!=', 'inactive')
                  ->orWhereNull('statut');
            });
        
        // Journaliser la requête pour déboguer
        \Log::info('Requête SQL pour la liste des employés actifs: ' . $query->toSql());
        
        // Appliquer le filtre de recherche si un terme est fourni
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('em_code', 'LIKE', "%{$search}%")
                  ->orWhere('first_name', 'LIKE', "%{$search}%")
                  ->orWhere('last_name', 'LIKE', "%{$search}%")
                  ->orWhere('position_held', 'LIKE', "%{$search}%");
                  
                // Vérification des colonnes qui pourraient exister
                // Nous retirons email et telephone qui ne semblent pas exister dans la table
            });
        }
        
        // Exécuter la requête avec pagination
        $employees = $query->paginate(10);
        
        // Conserver le terme de recherche dans la pagination
        $employees->appends(['search' => $search]);
        
        // Récupérer toutes les colonnes de la table employee pour les détails
        $employeeColumns = \DB::getSchemaBuilder()->getColumnListing('employee');
        
        return view('employees.index', compact('employees', 'search', 'employeeColumns'));
    }
    
    /**
     * Afficher les détails d'un employé via une route directe (sans AJAX)
     */
    public function details($id)
    {
        // Recherche par em_code plutôt que par id
        $employee = Employee::with('departement')->where('em_code', $id)->firstOrFail();
        $employeeColumns = \DB::getSchemaBuilder()->getColumnListing('employee');
        return view('employees.details', compact('employee', 'employeeColumns'));
    }
    
    /**
     * Afficher le formulaire d'ajout d'un employé
     */
    public function create()
    {
        $departements = Departement::all();
        return view('employees.create', compact('departements'));
    }
    
    /**
     * Enregistrer un nouvel employé
     */
    public function store(Request $request)
    {
        // Log des données reçues
        \Log::info('Données du formulaire reçues :', $request->all());
        
        try {
            $validated = $request->validate([
                'em_code' => 'required|string|max:20|unique:employee,em_code',
                'last_name' => 'required|string|max:255',
                'first_name' => 'required|string|max:255',
                'position_held' => 'required|string|max:255',
                'dep_id' => 'required|exists:departements,id',
                'corps_id' => 'nullable|exists:corps,id',
                'em_joining_date' => 'nullable|date',
                // Champs optionnels qui pourraient ne pas exister dans la table
                'telephone' => 'nullable|string|max:20',
                'adresse' => 'nullable|string',
            ]);
            
            \Log::info('Validation réussie');
            
            // Créer un tableau avec seulement les champs que nous savons exister dans la table
            $employeeData = [
                'em_code' => $request->em_code,
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'position_held' => $request->position_held,
                'dep_id' => $request->dep_id,
            ];
            
            // Ajouter les champs optionnels s'ils sont présents dans la requête
            if ($request->filled('corps_id')) {
                $employeeData['corps_id'] = $request->corps_id;
            }
            
            if ($request->filled('em_joining_date')) {
                $employeeData['em_joining_date'] = $request->em_joining_date;
            }
            
            // Ne pas inclure email car il n'existe pas dans la table
            // Inclure telephone et adresse seulement s'ils existent dans la table
            if ($request->filled('telephone')) {
                $employeeData['telephone'] = $request->telephone;
            }
            
            if ($request->filled('adresse')) {
                $employeeData['adresse'] = $request->adresse;
            }
            
            \Log::info('Tentative de création d\'employé avec les données :', $employeeData);
            
            // Débug: Vérifier si les colonnes existent dans la table
            $columns = \DB::getSchemaBuilder()->getColumnListing('employee');
            \Log::info('Colonnes disponibles dans la table employee :', $columns);
            
            // Filtrer les données pour ne garder que les colonnes existantes
            $filteredData = array_intersect_key($employeeData, array_flip($columns));
            \Log::info('Données filtrées selon les colonnes existantes :', $filteredData);
            
            $employee = Employee::create($filteredData);
            \Log::info('Employé créé avec succès, ID: ' . $employee->id);
            
            return redirect()->route('employees.index')
                ->with('success', 'Employé ajouté avec succès');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Erreur de validation: ' . json_encode($e->errors()));
            return redirect()->route('employees.create')
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Erreur de validation. Veuillez vérifier les champs du formulaire.');
                
        } catch (\Illuminate\Database\QueryException $e) {
            \Log::error('Erreur SQL: ' . $e->getMessage());
            \Log::error('SQL: ' . $e->getSql());
            \Log::error('Bindings: ' . json_encode($e->getBindings()));
            
            return redirect()->route('employees.create')
                ->withInput()
                ->with('error', 'Erreur de base de données: ' . $e->getMessage());
                
        } catch (\Exception $e) {
            \Log::error('Exception générale: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->route('employees.create')
                ->withInput()
                ->with('error', 'Erreur lors de l\'enregistrement : ' . $e->getMessage());
        }
    }
    
    /**
     * Afficher le formulaire d'édition d'un employé
     */
    public function edit($id)
    {
        try {
            // Récupérer l'employé avec ses relations
            $employee = Employee::with(['departement', 'corps'])->where('em_code', $id)->firstOrFail();
            
            // Récupérer tous les départements et corps pour les listes déroulantes
            $departements = Departement::all();
            $corps = \App\Models\Corps::all();
            
            \Log::info('Chargement du formulaire d\'édition pour l\'employé: ' . $employee->em_code);
            
            return view('employees.edit', compact('employee', 'departements', 'corps'));
            
        } catch (\Exception $e) {
            \Log::error('Erreur lors du chargement du formulaire d\'édition: ' . $e->getMessage());
            
            return redirect()->route('employees.index')
                ->with('error', 'Impossible de charger le formulaire d\'édition: ' . $e->getMessage());
        }
    }
    
    /**
     * Mettre à jour un employé
     */
    public function update(Request $request, $id)
    {
        // Log des données reçues
        \Log::info('Données du formulaire de mise à jour reçues :', $request->all());
        
        try {
            // Trouver l'employé à mettre à jour
            $employee = Employee::where('em_code', $id)->firstOrFail();
            
            $validated = $request->validate([
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'position_held' => 'required|string|max:255',
                'dep_id' => 'required|exists:departements,id',
                'corps_id' => 'nullable|exists:corps,id',
                'em_joining_date' => 'nullable|date',
                // Champs optionnels
                'em_phone' => 'nullable|string|max:20',
                'em_address' => 'nullable|string',
                'em_email' => 'nullable|email',
                'em_gender' => 'nullable|string|max:10',
                'em_birthday' => 'nullable|date',
            ]);
            
            \Log::info('Validation réussie');
            
            // Créer un tableau avec seulement les champs que nous savons exister dans la table
            $employeeData = [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'position_held' => $request->position_held,
                'dep_id' => $request->dep_id,
            ];
            
            // Ajouter les champs optionnels s'ils sont présents dans la requête
            if ($request->filled('corps_id')) {
                $employeeData['corps_id'] = $request->corps_id;
            }
            
            if ($request->filled('em_joining_date')) {
                $employeeData['em_joining_date'] = $request->em_joining_date;
            }
            
            if ($request->filled('em_phone')) {
                $employeeData['em_phone'] = $request->em_phone;
            }
            
            if ($request->filled('em_address')) {
                $employeeData['em_address'] = $request->em_address;
            }
            
            if ($request->filled('em_email')) {
                $employeeData['em_email'] = $request->em_email;
            }
            
            if ($request->filled('em_gender')) {
                $employeeData['em_gender'] = $request->em_gender;
            }
            
            if ($request->filled('em_birthday')) {
                $employeeData['em_birthday'] = $request->em_birthday;
            }
            
            \Log::info('Tentative de mise à jour de l\'employé avec les données :', $employeeData);
            
            // Mettre à jour l'employé
            $employee->update($employeeData);
            \Log::info('Employé mis à jour avec succès, ID: ' . $employee->em_id);
            
            return redirect()->route('employees.index')
                ->with('success', 'Employé mis à jour avec succès');
                
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('Employé non trouvé: ' . $id);
            return redirect()->route('employees.index')
                ->with('error', 'Employé non trouvé');
                
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Erreur de validation: ' . json_encode($e->errors()));
            return redirect()->route('employees.edit', $id)
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Erreur de validation. Veuillez vérifier les champs du formulaire.');
                
        } catch (\Illuminate\Database\QueryException $e) {
            \Log::error('Erreur SQL: ' . $e->getMessage());
            \Log::error('SQL: ' . $e->getSql());
            \Log::error('Bindings: ' . json_encode($e->getBindings()));
            
            return redirect()->route('employees.edit', $id)
                ->withInput()
                ->with('error', 'Erreur de base de données: ' . $e->getMessage());
                
        } catch (\Exception $e) {
            \Log::error('Exception générale: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->route('employees.edit', $id)
                ->withInput()
                ->with('error', 'Erreur lors de la mise à jour : ' . $e->getMessage());
        }
    }
    
    /**
     * Afficher les détails d'un employé
     */
    /**
     * Méthode dédiée pour les détails des employés (API)
     */
    public function getEmployeeDetails($id)
    {
        try {
            // Récupérer l'employé avec sa relation département
            $employee = Employee::with('departement')->find($id);
            
            if (!$employee) {
                return response()->json([
                    'success' => false,
                    'message' => 'Employé non trouvé'
                ], 404);
            }
            
            // Nom du département pour l'affichage
            $departementName = $employee->departement ? $employee->departement->nom_departement : 'Non assigné';
            
            // Renvoyer les données de l'employé
            return response()->json([
                'success' => true,
                'employee' => $employee,
                'departement' => $departementName
            ]);
            
        } catch (\Exception $e) {
            // Journaliser l'erreur et renvoyer une réponse d'erreur
            \Log::error('Erreur API - Détails employé: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la récupération des détails',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Désactiver un employé (suppression logique)
     */
    public function destroy($id)
    {
        try {
            // Récupérer l'employé
            $employee = Employee::where('em_code', $id)->firstOrFail();
            
            // Journaliser l'action
            \Log::info('Tentative de désactivation de l\'employé: ' . $employee->em_code);
            
            // Mettre à jour le statut de l'employé à 'inactive'
            $employee->statut = 'inactive';
            $employee->save();
            
            \Log::info('Employé désactivé avec succès: ' . $employee->em_code);
            
            return response()->json([
                'success' => true,
                'message' => 'L\'employé a été désactivé avec succès.'
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la désactivation de l\'employé: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la désactivation de l\'employé: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Afficher la liste des employés désactivés
     */
    public function deleted()
    {
        try {
            // Récupérer tous les employés désactivés
            $employees = Employee::where('statut', 'inactive')
                ->with('departement')
                ->orderBy('last_name')
                ->get();
            
            return view('employees.deleted', compact('employees'));
            
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des employés désactivés: ' . $e->getMessage());
            
            return redirect()->route('employees.index')
                ->with('error', 'Erreur lors de la récupération des employés désactivés: ' . $e->getMessage());
        }
    }
    
    /**
     * Réactiver un employé désactivé
     */
    public function restore($id)
    {
        try {
            // Récupérer l'employé
            $employee = Employee::where('em_code', $id)->firstOrFail();
            
            // Journaliser l'action
            \Log::info('Tentative de réactivation de l\'employé: ' . $employee->em_code);
            
            // Mettre à jour le statut de l'employé à 'active'
            $employee->statut = 'active';
            $employee->save();
            
            \Log::info('Employé réactivé avec succès: ' . $employee->em_code);
            
            return response()->json([
                'success' => true,
                'message' => 'L\'employé a été réactivé avec succès.'
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la réactivation de l\'employé: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la réactivation de l\'employé: ' . $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Departement extends Model
{
    use HasFactory;
    protected $guarded = [];
    
    // Attributs fillable
    protected $fillable = [
        'nom_departement',
        'statut',
        'created_at',
        'updated_at'
    ];

    // Relation avec les véhicules
    public function vehicules()
    {
        return $this->hasMany(Vehicule::class);
    }

    // Relation inverse si nécessaire
    public function receptions()
    {
        return $this->hasMany(Reception::class, 'direction_structure');
    }
}

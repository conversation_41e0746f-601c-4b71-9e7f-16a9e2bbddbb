/* Styles pour la page de détails des réceptions */
:root {
    --primary: #4e73df;
    --primary-light: #eef2ff;
    --primary-dark: #224abe;
    --secondary: #6c757d;
    --success: #1cc88a;
    --danger: #e74a3b;
    --warning: #f6c23e;
    --info: #36b9cc;
    --light: #f8f9fc;
    --dark: #5a5c69;
    --white: #fff;
    --transition: all 0.3s ease;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.details-header {
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    color: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.details-header h4 {
    margin: 0;
    font-weight: 600;
}

.details-header .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    margin-top: 0.5rem;
}

.details-header .breadcrumb-item a {
    color: var(--white);
    opacity: 0.8;
    transition: var(--transition);
}

.details-header .breadcrumb-item a:hover {
    opacity: 1;
    text-decoration: none;
}

.details-header .breadcrumb-item.active {
    color: var(--white);
    opacity: 1;
}

.details-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.details-card .card-header {
    background: var(--white);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.5rem;
}

.details-card .card-title {
    margin: 0;
    font-weight: 600;
    color: var(--dark);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.details-card .card-body {
    padding: 1.5rem;
}

.details-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.details-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.details-section-title {
    color: var(--primary);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--primary-light);
    display: flex;
    align-items: center;
}

.details-section-title i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.details-item {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--light);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.details-item:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.details-item:last-child {
    margin-bottom: 0;
}

.details-label {
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.details-value {
    color: var(--dark);
    font-size: 1.1rem;
}

.details-value.empty {
    color: var(--secondary);
    font-style: italic;
}

.details-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-details {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    border: none;
    color: var(--white);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(34, 74, 190, 0.3);
}

.btn-secondary {
    background-color: var(--secondary);
    border: none;
    color: var(--white);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

.btn-success {
    background-color: var(--success);
    border: none;
    color: var(--white);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(28, 200, 138, 0.3);
}

.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.badge-success {
    background-color: rgba(28, 200, 138, 0.1);
    color: var(--success);
}

.badge-warning {
    background-color: rgba(246, 194, 62, 0.1);
    color: var(--warning);
}

.details-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    background-color: var(--primary-light);
    border-radius: var(--border-radius);
    color: var(--primary-dark);
    font-size: 0.9rem;
}

.details-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.details-meta-item i {
    font-size: 1.1rem;
}

/* Animation d'entrée */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.animate-delay-1 {
    animation-delay: 0.1s;
}

.animate-delay-2 {
    animation-delay: 0.2s;
}

.animate-delay-3 {
    animation-delay: 0.3s;
}

/* Styles pour l'affichage des documents */
.document-preview {
    text-align: center;
    padding: 1rem;
    background-color: var(--light);
    border-radius: var(--border-radius);
    transition: var(--transition);
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
}

.document-preview:hover {
    background-color: var(--primary-light);
}

.document-embed {
    margin: 0 auto;
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

/* Style pour les PDF */
.pdf-embed {
    height: 400px;
    width: 100%;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.pdf-viewer {
    width: 100%;
    height: 100%;
    border: none;
}

/* Style pour les images */
.image-embed {
    max-width: 100%;
    text-align: center;
    background-color: #f8f9fc;
    padding: 1rem;
}

.document-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: var(--border-radius);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Style pour les autres types de fichiers */
.other-file-embed {
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fc;
}

.file-icon {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.file-extension {
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    font-weight: bold;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
}

/* Informations sur le fichier */
.document-info {
    padding: 0.75rem;
    background-color: rgba(78, 115, 223, 0.05);
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.file-name {
    font-weight: 600;
    color: var(--primary-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
}

.file-type {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Actions sur le document */
.document-actions {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Animation pour le chargement du document */
@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

.document-loading {
    animation: pulse 1.5s infinite;
    text-align: center;
    padding: 2rem;
}

.document-loading i {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: 1rem;
    display: block;
}

/* Styles pour les informations complémentaires */
.info-box {
    padding: 1rem;
    border-radius: var(--border-radius);
    background-color: rgba(54, 185, 204, 0.1);
    border-left: 4px solid var(--info);
    margin-bottom: 1.5rem;
}

.info-box-title {
    color: var(--info);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-box-content {
    color: var(--dark);
    font-size: 0.95rem;
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sortie extends Model
{
    use HasFactory;
    use \Illuminate\Database\Eloquent\SoftDeletes;
    
    protected $fillable = [
        'article_id',
        'quantite',
        'date_sortie',
        'beneficiaire_id',
        'type_sortie',
        'motif',
        'user_id'
    ];
    
    protected $casts = [
        'date_sortie' => 'date',
    ];
    
    public function article()
    {
        return $this->belongsTo(Article::class);
    }
    
    public function beneficiaire()
    {
        return $this->belongsTo(User::class, 'beneficiaire_id');
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}

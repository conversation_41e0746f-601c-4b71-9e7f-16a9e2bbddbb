<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Departement;
use Exception;

class DepartementController extends Controller
{
    public function index(){
        // Récupérer tous les départements actifs ou sans statut défini
        $departements = Departement::where(function($q) {
                $q->where('statut', '!=', 'inactive')
                  ->orWhereNull('statut');
            })
            ->latest()
            ->get();
        return view('departements.index', compact('departements'));
    }
    
    /**
     * Afficher la liste des départements désactivés
     */
    public function deleted(){
        // Récupérer tous les départements désactivés
        $departements = Departement::where('statut', 'inactive')
            ->latest()
            ->get();
        return view('departements.deleted', compact('departements'));
    }


    public function create(){
        //dd("bonjour");
        return view('departements.create');
    }

    public function edit($id){
        $departements = Departement::find($id);
        return view('departements.edit', compact('departements'));
    }


    //Interaction avec la BD
    public function store(Request $request){
        //Enregistrer une nouvelle Direction
        try{
            $departement = new Departement();
            $departement->nom_departement = $request->name;
            $departement->save();
            $notification = array(
                'message' => 'Département enregistré avec succès',
                'alert-type' => 'success'
            );
            return redirect()->route('departements.index')->with($notification);
        }catch(Exception $e){
            dd($e);
        }
    }

    public function update(Request $request, $id){
        //Modifier une Direction
        try{
            $departement = Departement::find($id);
            $departement->nom_departement = $request->name;
            $departement->update();
            $notification = array(
                'message' => 'Département modifié avec succès',
                'alert-type' => 'success'
            );
            return redirect()->route('departements.index')->with($notification);
        }catch(Exception $e){
            dd($e);
        }
    }

    public function delete($id){
        // Désactiver un département (suppression logique)
        try{
            $departement = Departement::find($id);
            
            // Vérifier si le département existe
            if (!$departement) {
                return response()->json([
                    'success' => false,
                    'message' => 'Département non trouvé'
                ], 404);
            }
            
            // Mettre à jour le statut du département à 'inactive'
            $departement->statut = 'inactive';
            $departement->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Le département a été désactivé avec succès.'
            ]);
            
        } catch(Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la désactivation du département: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Réactiver un département désactivé
     */
    public function restore($id) {
        try {
            // Récupérer le département
            $departement = Departement::find($id);
            
            // Vérifier si le département existe
            if (!$departement) {
                return response()->json([
                    'success' => false,
                    'message' => 'Département non trouvé'
                ], 404);
            }
            
            // Mettre à jour le statut du département à 'active'
            $departement->statut = 'active';
            $departement->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Le département a été réactivé avec succès.'
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la réactivation du département: ' . $e->getMessage()
            ], 500);
        }
    }
}

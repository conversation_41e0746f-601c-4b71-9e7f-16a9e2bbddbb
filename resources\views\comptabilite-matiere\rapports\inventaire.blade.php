@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Rapport d'Inventaire</h1>
        <div>
            <a href="{{ route('inventaires.show', $inventaire->id) }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">
                <i class="fas fa-eye fa-sm text-white-50"></i> Voir l'inventaire
            </a>
            <a href="{{ route('rapports.inventaire', ['id' => $inventaire->id, 'export_pdf' => 1]) }}" class="d-none d-sm-inline-block btn btn-sm btn-danger shadow-sm">
                <i class="fas fa-file-pdf fa-sm text-white-50"></i> Exporter en PDF
            </a>
        </div>
    </div>

    <!-- Informations de l'inventaire -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Informations générales</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Référence :</th>
                            <td>{{ $inventaire->reference }}</td>
                        </tr>
                        <tr>
                            <th>Date de début :</th>
                            <td>{{ \Carbon\Carbon::parse($inventaire->date_debut)->format('d/m/Y') }}</td>
                        </tr>
                        <tr>
                            <th>Date de clôture :</th>
                            <td>{{ \Carbon\Carbon::parse($inventaire->date_cloture)->format('d/m/Y') }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Statut :</th>
                            <td>
                                <span class="badge badge-success">Clôturé</span>
                            </td>
                        </tr>
                        <tr>
                            <th>Responsable :</th>
                            <td>{{ $inventaire->user->name }}</td>
                        </tr>
                        <tr>
                            <th>Observations :</th>
                            <td>{{ $inventaire->observations ?? 'Aucune observation' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques de l'inventaire -->
    <div class="row">
        <!-- Total Articles -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Articles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalArticles }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Articles avec écart -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Articles avec écart</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $articlesAvecEcart }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Écart positif -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Écart positif (excédent)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $ecartPositif }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plus-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Écart négatif -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Écart négatif (manquant)</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $ecartNegatif }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-minus-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des articles avec écart -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Articles avec écart</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="tableEcarts" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Désignation</th>
                            <th>Unité</th>
                            <th>Stock théorique</th>
                            <th>Stock réel</th>
                            <th>Écart</th>
                            <th>Observations</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($inventaire->details as $detail)
                            @if($detail->stock_theorique != $detail->stock_reel)
                            <tr>
                                <td>{{ $detail->article->code_article }}</td>
                                <td>{{ $detail->article->designation }}</td>
                                <td>{{ $detail->article->unite->libelle }}</td>
                                <td class="text-right">{{ $detail->stock_theorique }}</td>
                                <td class="text-right">{{ $detail->stock_reel }}</td>
                                <td class="text-right">
                                    @php
                                        $ecart = $detail->stock_reel - $detail->stock_theorique;
                                    @endphp
                                    @if($ecart > 0)
                                        <span class="text-success">+{{ $ecart }}</span>
                                    @else
                                        <span class="text-danger">{{ $ecart }}</span>
                                    @endif
                                </td>
                                <td>{{ $detail->observations }}</td>
                            </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Tableau complet de l'inventaire -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Inventaire complet</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="tableInventaire" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Désignation</th>
                            <th>Unité</th>
                            <th>Stock théorique</th>
                            <th>Stock réel</th>
                            <th>Écart</th>
                            <th>Observations</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($inventaire->details as $detail)
                        <tr>
                            <td>{{ $detail->article->code_article }}</td>
                            <td>{{ $detail->article->designation }}</td>
                            <td>{{ $detail->article->unite->libelle }}</td>
                            <td class="text-right">{{ $detail->stock_theorique }}</td>
                            <td class="text-right">{{ $detail->stock_reel }}</td>
                            <td class="text-right">
                                @php
                                    $ecart = $detail->stock_reel - $detail->stock_theorique;
                                @endphp
                                @if($ecart > 0)
                                    <span class="text-success">+{{ $ecart }}</span>
                                @elseif($ecart < 0)
                                    <span class="text-danger">{{ $ecart }}</span>
                                @else
                                    <span class="text-muted">0</span>
                                @endif
                            </td>
                            <td>{{ $detail->observations }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Conclusion -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Conclusion</h6>
        </div>
        <div class="card-body">
            <p>
                Cet inventaire a été clôturé le {{ \Carbon\Carbon::parse($inventaire->date_cloture)->format('d/m/Y') }} 
                par {{ $inventaire->user->name }}. 
                @if($articlesAvecEcart > 0)
                    Des écarts ont été constatés sur {{ $articlesAvecEcart }} articles sur un total de {{ $totalArticles }} articles inventoriés.
                    @if($ecartPositif > 0)
                        Un excédent total de {{ $ecartPositif }} unités a été constaté.
                    @endif
                    @if($ecartNegatif > 0)
                        Un manquant total de {{ $ecartNegatif }} unités a été constaté.
                    @endif
                    @if($inventaire->ajustements_generes)
                        Des ajustements de stock ont été générés automatiquement pour corriger ces écarts.
                    @else
                        Aucun ajustement automatique n'a été généré pour ces écarts.
                    @endif
                @else
                    Aucun écart n'a été constaté sur les {{ $totalArticles }} articles inventoriés.
                @endif
            </p>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        $('#tableEcarts').DataTable({
            "order": [[ 5, "desc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
        
        $('#tableInventaire').DataTable({
            "order": [[ 1, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
    });
</script>
@endsection

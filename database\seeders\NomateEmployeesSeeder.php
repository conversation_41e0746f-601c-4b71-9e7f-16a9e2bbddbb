<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\IOFactory;

class NomateEmployeesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Renommer le seeder pour refléter son vrai rôle
        $this->command->info('Exécution du seeder pour la nomenclature des comptes matière (NOMATE)');
        
        // Chemin vers le fichier Excel NOMATE.xlsx
        $filePath = public_path('backend/assets/NOMATE.xlsx');
        
        // Vérifier si le fichier existe
        if (!file_exists($filePath)) {
            $this->command->error('Le fichier NOMATE.xlsx n\'existe pas à l\'emplacement spécifié.');
            return;
        }
        
        try {
            // Charger le fichier Excel
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();
            
            // Supprimer l'en-tête (première ligne)
            $headers = array_shift($rows);
            
            $this->command->info('Importation des données de comptabilité matière en cours...');
            
            $count = 0;
            $errors = 0;
            
            // Parcourir les lignes du fichier Excel
            foreach ($rows as $row) {
                // Vérifier si la ligne contient des données
                if (empty($row[0])) continue;
                
                // Extraire les données de la ligne
                // Adapter ces indices en fonction de la structure réelle de votre fichier Excel
                $code = $row[0] ?? null;
                $sous_code = $row[1] ?? null;
                $intitule = $row[2] ?? null;
                $description = $row[3] ?? null;
                $montant = $row[4] ?? 0;
                $type_compte = $row[5] ?? null;
                
                // Vérifier que nous avons au moins le code et l'intitulé
                if (empty($code) || empty($intitule)) {
                    $this->command->warn('Ligne ignorée: code ou intitulé manquant.');
                    continue;
                }
                
                // Insérer dans la table nomate
                try {
                    DB::table('nomate')->insertOrIgnore([
                        'code' => $code,
                        'sous_code' => $sous_code,
                        'intitule' => $intitule,
                        'description' => $description,
                        'montant' => is_numeric($montant) ? $montant : 0,
                        'type_compte' => $type_compte,
                        'statut' => 'actif',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    $count++;
                } catch (\Exception $e) {
                    $this->command->error('Erreur lors de l\'insertion du compte: ' . $code . ' - ' . $intitule . ' - ' . $e->getMessage());
                    $errors++;
                }
            }
            
            $this->command->info('Importation terminée. ' . $count . ' comptes importés avec succès. ' . $errors . ' erreurs.');
            
        } catch (\Exception $e) {
            $this->command->error('Une erreur est survenue lors de la lecture du fichier Excel: ' . $e->getMessage());
        }
    }
}

<?php

require __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

// Chemin vers le fichier Excel
$filePath = __DIR__ . '/public/backend/assets/NOMATE.xlsx';

// Vérifier si le fichier existe
if (!file_exists($filePath)) {
    echo "Le fichier NOMATE.xlsx n'existe pas à l'emplacement spécifié.\n";
    exit;
}

try {
    // Charger le fichier Excel
    $spreadsheet = IOFactory::load($filePath);
    $worksheet = $spreadsheet->getActiveSheet();
    $rows = $worksheet->toArray();
    
    // Afficher les en-têtes (première ligne)
    if (count($rows) > 0) {
        echo "En-têtes du fichier Excel:\n";
        echo "-------------------------\n";
        foreach ($rows[0] as $index => $header) {
            echo "Colonne " . $index . ": " . $header . "\n";
        }
        echo "\n";
    }
    
    // Afficher les 5 premières lignes de données
    echo "Échantillon de données (5 premières lignes):\n";
    echo "-------------------------------------------\n";
    for ($i = 1; $i <= min(5, count($rows) - 1); $i++) {
        echo "Ligne " . $i . ":\n";
        foreach ($rows[$i] as $index => $value) {
            echo "  Colonne " . $index . ": " . $value . "\n";
        }
        echo "\n";
    }
    
} catch (\Exception $e) {
    echo "Une erreur est survenue lors de la lecture du fichier Excel: " . $e->getMessage() . "\n";
}

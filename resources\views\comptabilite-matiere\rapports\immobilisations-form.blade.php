<!doctype html>
<html lang="fr">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!--favicon-->
    <link rel="icon" href="{{ asset('backend/assets/images/favicon-32x32.png') }}" type="image/png"/>
    <!--plugins-->
    <link href="{{ asset('backend/assets/plugins/vectormap/jquery-jvectormap-2.0.2.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/simplebar/css/simplebar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/metismenu/css/metisMenu.min.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <!-- loader-->
    <link href="{{ asset('backend/assets/css/pace.min.css') }}" rel="stylesheet"/>
    <script src="{{ asset('backend/assets/js/pace.min.js') }}"></script>
    <!-- Bootstrap CSS -->
    <link href="{{ asset('backend/assets/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/bootstrap-extended.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/icons.css') }}" rel="stylesheet">
    <!-- Theme Style CSS -->
    <link rel="stylesheet" href="{{ asset('backend/assets/css/dark-theme.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/semi-dark.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/header-colors.css') }}"/>
    <!-- Toster CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" >
    <!-- Animate CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <!-- Custom Sidebar CSS -->
    <link rel="stylesheet" href="{{ asset('css/custom-sidebar.css') }}">
    <!-- End Custom Sidebar CSS -->
    <title>Rapport des Immobilisations</title>
    <style>
        .form-card {
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            border: none;
            transition: all 0.3s ease;
            margin-bottom: 25px;
        }
        .form-card:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transform: translateY(-5px);
        }
        .form-card .card-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px 25px;
            border-bottom: none;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .info-card .card-header {
            background: linear-gradient(135deg, #36b9cc 0%, #1a8a9c 100%);
        }
        .form-card .card-body {
            padding: 30px;
            border-radius: 0 0 15px 15px;
        }
        .btn-back {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 50px;
            transition: all 0.3s;
            background-color: #f8f9fc;
            color: #5a5c69;
            border: 1px solid #e2e8f0;
        }
        .btn-back:hover {
            background-color: #eaecf4;
            color: #3a3b45;
            transform: translateY(-2px);
        }
        .btn-action {
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
            border: none;
        }
        .form-label {
            font-weight: 500;
            color: #4e73df;
            margin-bottom: 10px;
        }
        .form-control, .form-select {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;
        }
        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
            border-color: #4e73df;
        }
        .select2-container {
            width: 100% !important;
        }
        .select2-container--default .select2-selection--single {
            border-radius: 10px;
            height: 48px;
            padding: 10px 15px;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 48px;
        }
        .select2-dropdown {
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #4e73df;
        }
        .feature-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background-color: rgba(78, 115, 223, 0.1);
            color: #4e73df;
            font-size: 24px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        .feature-card:hover .feature-icon {
            background-color: #4e73df;
            color: white;
            transform: scale(1.1);
        }
        .feature-card {
            padding: 20px;
            border-radius: 15px;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s;
            height: 100%;
        }
        .feature-card:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transform: translateY(-5px);
        }
        .info-list li {
            margin-bottom: 12px;
            position: relative;
            padding-left: 30px;
        }
        .info-list li:before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: #4e73df;
            font-size: 18px;
        }
        .alert-custom {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: none;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .alert-info {
            background-color: rgba(54, 185, 204, 0.1);
            border-left: 4px solid #36b9cc;
            color: #1a4a52;
        }
        .info-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(54, 185, 204, 0.2);
            color: #36b9cc;
            font-size: 20px;
        }
        .form-group {
            margin-bottom: 25px;
        }
    </style>
</head>

<body>
    <!--wrapper-->
    <div class="wrapper">
        <!--sidebar wrapper -->
        @include('admin.body.sidebar')
        <!--end sidebar wrapper -->
        <!--start header -->
        @include('admin.body.header')
        <!--end header -->
        <!--start page wrapper -->
        <div class="page-wrapper">
            <div class="page-content">
                <!--breadcrumb-->
                <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
                    <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
                    <div class="ps-3">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0 p-0">
                                <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a></li>
                                <li class="breadcrumb-item"><a href="{{ route('rapports.index') }}">Rapports</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Rapport des Immobilisations</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ route('rapports.index') }}" class="btn btn-back">
                            <i class="bx bx-arrow-back"></i> Retour aux rapports
                        </a>
                    </div>
                </div>
                <!--end breadcrumb-->

                <!-- Formulaire de génération du rapport des immobilisations -->
                <div class="row">
                    <div class="col-12">
                        <div class="card form-card animate__animated animate__fadeIn">
                            <div class="card-header">
                                <i class="bx bx-filter-alt me-2"></i>
                                <h5 class="mb-0 text-white">Filtres du rapport</h5>
                            </div>
                            <div class="card-body">
                                <form action="{{ route('rapports.immobilisations') }}" method="POST" class="row g-4">
                                    @csrf
                                    
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="service_id" class="form-label"><i class="bx bx-building me-1"></i> Service</label>
                                            <select class="form-select" id="service_id" name="service_id">
                                                <option value="">Tous les services</option>
                                                @foreach($services as $id => $nom)
                                                    <option value="{{ $id }}" {{ old('service_id') == $id ? 'selected' : '' }}>
                                                        {{ $nom }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <small class="text-muted">Filtrer par service ou direction</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="etat_physique" class="form-label"><i class="bx bx-health me-1"></i> État physique</label>
                                            <select class="form-select" id="etat_physique" name="etat_physique">
                                                <option value="">Tous les états</option>
                                                @foreach($etatsPhysiques as $value => $label)
                                                    <option value="{{ $value }}" {{ old('etat_physique') == $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <small class="text-muted">Condition actuelle des biens</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="statut" class="form-label"><i class="bx bx-check-circle me-1"></i> Statut</label>
                                            <select class="form-select" id="statut" name="statut">
                                                <option value="">Tous les statuts</option>
                                                @foreach($statuts as $value => $label)
                                                    <option value="{{ $value }}" {{ old('statut') == $value ? 'selected' : '' }}>
                                                        {{ $label }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <small class="text-muted">Statut administratif des immobilisations</small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12 mt-4">
                                        <div class="d-flex justify-content-center gap-3">
                                            <button type="submit" class="btn btn-primary btn-action">
                                                <i class="bx bx-search"></i> Générer le rapport
                                            </button>
                                            <button type="submit" name="export_pdf" value="1" class="btn btn-danger btn-action">
                                                <i class="bx bx-file-pdf"></i> Exporter en PDF
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section d'informations sur le rapport -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-custom alert-info animate__animated animate__fadeIn">
                            <div class="info-icon">
                                <i class="bx bx-info-circle"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Comment utiliser ce rapport ?</h5>
                                <p class="mb-0">Utilisez les filtres ci-dessus pour affiner les résultats selon vos besoins spécifiques.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="card form-card info-card animate__animated animate__fadeIn">
                            <div class="card-header">
                                <i class="bx bx-info-circle me-2"></i>
                                <h5 class="mb-0 text-white">Informations sur le Rapport des Immobilisations</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="fs-5">Le <strong>Rapport des Immobilisations</strong> permet de visualiser l'état du patrimoine immobilisé de l'organisation, filtré selon différents critères.</p>
                                        
                                        <p class="mt-3">Ce rapport est essentiel pour :</p>
                                        <ul class="info-list">
                                            <li>Avoir une vue d'ensemble des immobilisations par service</li>
                                            <li>Suivre l'état physique des biens</li>
                                            <li>Identifier les biens réformés ou hors service</li>
                                            <li>Faciliter les inventaires physiques</li>
                                            <li>Préparer les opérations de maintenance ou de renouvellement</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="feature-card">
                                                    <div class="feature-icon">
                                                        <i class="bx bx-building"></i>
                                                    </div>
                                                    <h5>Filtrage par service</h5>
                                                    <p>Visualisez les immobilisations par service ou direction pour une meilleure gestion décentralisée.</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="feature-card">
                                                    <div class="feature-icon">
                                                        <i class="bx bx-health"></i>
                                                    </div>
                                                    <h5>État physique</h5>
                                                    <p>Identifiez rapidement les biens en bon état, moyens, mauvais ou hors service.</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="feature-card">
                                                    <div class="feature-icon">
                                                        <i class="bx bx-check-circle"></i>
                                                    </div>
                                                    <h5>Statut administratif</h5>
                                                    <p>Suivez le statut administratif des biens : actifs, réformés, en transfert, etc.</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="feature-card">
                                                    <div class="feature-icon">
                                                        <i class="bx bx-file-pdf"></i>
                                                    </div>
                                                    <h5>Export PDF</h5>
                                                    <p>Générez des rapports PDF pour vos archives ou présentations aux responsables.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end page wrapper -->
        <!--start footer -->
        @include('admin.body.footer')
        <!--end footer -->
    </div>
    <!--end wrapper-->

    <!-- Bootstrap JS -->
    <script src="{{ asset('backend/assets/js/bootstrap.bundle.min.js') }}"></script>
    <!--plugins-->
    <script src="{{ asset('backend/assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/simplebar/js/simplebar.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/metismenu/js/metisMenu.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!--app JS-->
    <script src="{{ asset('backend/assets/js/app.js') }}"></script>
    
    <script>
        $(document).ready(function() {
            // Initialiser les select2 pour une meilleure expérience utilisateur
            $('#service_id').select2({
                placeholder: "Tous les services",
                allowClear: true,
                theme: "bootstrap-5"
            });
            
            $('#etat_physique').select2({
                placeholder: "Tous les états",
                allowClear: true,
                theme: "bootstrap-5"
            });
            
            $('#statut').select2({
                placeholder: "Tous les statuts",
                allowClear: true,
                theme: "bootstrap-5"
            });
            
            // Animation des cartes au scroll
            const animateOnScroll = () => {
                const cards = document.querySelectorAll('.feature-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('animate__animated', 'animate__fadeInUp');
                    }, index * 100);
                });
            };
            
            // Déclencher l'animation après le chargement de la page
            setTimeout(animateOnScroll, 500);
            
            // Afficher un message de succès si présent
            @if(session('success'))
                toastr.success("{{ session('success') }}");
            @endif
            
            // Afficher un message d'erreur si présent
            @if(session('error'))
                toastr.error("{{ session('error') }}");
            @endif
        });
    </script>
</body>
</html>



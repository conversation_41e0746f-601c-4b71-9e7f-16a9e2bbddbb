/* Dashboard Modern CSS - Styles personnalisés pour le dashboard */

/* Styles généraux */
.dashboard-container {
    padding: 1.5rem;
}

.welcome-banner {
    background: linear-gradient(135deg, #2b4d7e 0%, #1e88e5 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background-image: url('../backend/assets/images/bg-shapes.svg');
    background-repeat: no-repeat;
    background-position: right;
    opacity: 0.2;
}

.welcome-banner h2 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
}

.welcome-banner p {
    opacity: 0.9;
    max-width: 80%;
}

/* Cartes statistiques */
.stat-card {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    border: none;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.stat-card .card-body {
    padding: 1.5rem;
    z-index: 1;
}

.stat-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.stat-card .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-desc {
    font-size: 0.8rem;
    color: #6c757d;
}

.stat-card .trend-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.trend-up {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.trend-down {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stat-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.bg-vehicles {
    background: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
}

.bg-receptions {
    background: linear-gradient(135deg, #f83600 0%, #fe8c00 100%);
}

.bg-departments {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.bg-personnel {
    background: linear-gradient(135deg, #834d9b 0%, #d04ed6 100%);
}

/* Cartes graphiques */
.chart-card {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    border: none;
}

.chart-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

.chart-card .card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.chart-card .card-body {
    padding: 1.5rem;
}

.chart-container {
    position: relative;
    height: 300px;
}

.chart-legend {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
    margin-right: 0.5rem;
}

/* Tableau des activités récentes */
.activity-card {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: none;
}

.activity-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

.activity-card .card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.activity-card .card-body {
    padding: 0;
}

.activity-table th {
    font-weight: 600;
    color: #495057;
    border-top: none;
    background-color: #f8f9fa;
}

.activity-table td {
    vertical-align: middle;
    padding: 1rem 1.5rem;
}

.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.status-completed {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.status-canceled {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.vehicle-img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    background-color: #f8f9fa;
    border: none;
    transition: all 0.2s ease;
    margin-right: 0.5rem;
}

.action-btn:hover {
    background-color: #e9ecef;
    color: #212529;
}

.action-btn.view:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.action-btn.edit:hover {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.action-btn.delete:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* Widgets latéraux */
.side-widget {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    border: none;
    overflow: hidden;
}

.side-widget .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

.side-widget .card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.side-widget .card-body {
    padding: 1.5rem;
}

.calendar-widget {
    background-color: #fff;
}

.calendar-date {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.calendar-day {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.calendar-day .day {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1;
}

.calendar-day .month {
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
}

.calendar-event {
    flex: 1;
}

.calendar-event h6 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.calendar-event p {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}

.task-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.task-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.task-checkbox {
    margin-right: 1rem;
}

.task-content {
    flex: 1;
}

.task-content h6 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.task-content p {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}

.task-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.priority-medium {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.priority-low {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

/* Alertes */
.alert-widget {
    border-left: 4px solid;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.alert-warning {
    border-left-color: #ffc107;
}

.alert-danger {
    border-left-color: #dc3545;
}

.alert-info {
    border-left-color: #0dcaf0;
}

.alert-widget h6 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    color: #212529;
}

.alert-widget p {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 {
    animation-delay: 0.1s;
}

.delay-2 {
    animation-delay: 0.2s;
}

.delay-3 {
    animation-delay: 0.3s;
}

.delay-4 {
    animation-delay: 0.4s;
}

/* Responsive */
@media (max-width: 992px) {
    .welcome-banner h2 {
        font-size: 1.5rem;
    }
    
    .welcome-banner p {
        max-width: 100%;
    }
    
    .chart-container {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .welcome-banner {
        padding: 1.5rem;
    }
    
    .stat-card .stat-value {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .activity-table td, .activity-table th {
        padding: 0.75rem;
    }
}

/* Filtres et exportation */
.filter-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.filter-select {
    min-width: 150px;
}

.export-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.export-btn i {
    font-size: 1rem;
}

.tooltip-custom {
    position: relative;
    display: inline-block;
}

.tooltip-custom .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: #212529;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.75rem;
}

.tooltip-custom:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #dc3545;
    color: white;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Styles spécifiques pour le graphique des employés */
.header-title-section h5 {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.header-title-section h5 i {
    color: #3498db;
    font-size: 1.3rem;
}

.header-title-section p {
    margin: 0;
    font-size: 0.875rem;
}

/* Animation pour le graphique des employés */
@keyframes employeeChartFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

#employeeChart {
    animation: employeeChartFadeIn 0.8s ease-out;
}

/* Amélioration responsive pour le nouveau graphique */
@media (max-width: 768px) {
    .chart-legend {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .header-title-section {
        margin-bottom: 15px;
    }

    .chart-card .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}

<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Departement;
use App\Models\Immobilisation;
use App\Models\Mutation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MutationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $mutations = Mutation::with(['immobilisation', 'immobilisation.article', 'serviceOrigine', 'serviceDestination', 'user'])
            ->orderBy('date_mutation', 'desc')
            ->get();
        
        return view('comptabilite-matiere.mutations.index', compact('mutations'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Récupérer uniquement les immobilisations actives (non réformées)
        $immobilisations = Immobilisation::where('statut', '!=', 'reforme')
            ->with(['article', 'serviceAffecte'])
            ->get()
            ->map(function ($immobilisation) {
                return [
                    'id' => $immobilisation->id,
                    'display' => $immobilisation->numero_inventaire . ' - ' . $immobilisation->article->designation . ' (Service actuel: ' . $immobilisation->serviceAffecte->nom . ')'
                ];
            })
            ->pluck('display', 'id');
            
        $services = Departement::orderBy('nom', 'asc')->pluck('nom', 'id');
        
        return view('comptabilite-matiere.mutations.create', compact('immobilisations', 'services'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'immobilisation_id' => 'required|exists:immobilisations,id',
            'service_destination_id' => 'required|exists:departements,id',
            'date_mutation' => 'required|date',
            'motif' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->route('mutations.create')
                ->withErrors($validator)
                ->withInput();
        }
        
        // Récupérer l'immobilisation
        $immobilisation = Immobilisation::findOrFail($request->immobilisation_id);
        
        // Vérifier que l'immobilisation n'est pas réformée
        if ($immobilisation->statut === 'reforme') {
            return redirect()->route('mutations.create')
                ->with('error', 'Impossible de muter une immobilisation réformée.')
                ->withInput();
        }
        
        // Vérifier que le service de destination est différent du service d'origine
        if ($immobilisation->service_affecte_id == $request->service_destination_id) {
            return redirect()->route('mutations.create')
                ->with('error', 'Le service de destination doit être différent du service d\'origine.')
                ->withInput();
        }

        // Créer la mutation
        Mutation::create([
            'immobilisation_id' => $request->immobilisation_id,
            'service_origine_id' => $immobilisation->service_affecte_id,
            'service_destination_id' => $request->service_destination_id,
            'date_mutation' => $request->date_mutation,
            'motif' => $request->motif,
            'user_id' => Auth::id(),
        ]);
        
        // Mettre à jour le service d'affectation de l'immobilisation
        $immobilisation->update([
            'service_affecte_id' => $request->service_destination_id
        ]);

        return redirect()->route('mutations.index')
            ->with('success', 'Mutation enregistrée avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $mutation = Mutation::with(['immobilisation', 'immobilisation.article', 'serviceOrigine', 'serviceDestination', 'user'])
            ->findOrFail($id);
        
        return view('comptabilite-matiere.mutations.show', compact('mutation'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $mutation = Mutation::findOrFail($id);
        
        return view('comptabilite-matiere.mutations.edit', compact('mutation'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $mutation = Mutation::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'date_mutation' => 'required|date',
            'motif' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->route('mutations.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }

        $mutation->update([
            'date_mutation' => $request->date_mutation,
            'motif' => $request->motif,
        ]);

        return redirect()->route('mutations.index')
            ->with('success', 'Mutation mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $mutation = Mutation::findOrFail($id);
        
        // On ne permet pas la suppression des mutations pour garantir la traçabilité
        // Mais si on le fait, il faudrait remettre l'immobilisation dans son service d'origine
        return redirect()->route('mutations.index')
            ->with('error', 'La suppression des mutations n\'est pas autorisée pour garantir la traçabilité.');
    }
}

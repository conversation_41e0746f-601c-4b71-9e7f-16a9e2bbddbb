<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Corps extends Model
{
    use HasFactory;
    
    // Spécifier le nom de la table existante
    protected $table = 'corps';
    
    protected $fillable = [
        'corps_name',
        // Ajoutez d'autres champs si nécessaire
    ];
    
    // Relation avec les employés
    public function employees()
    {
        return $this->hasMany(Employee::class, 'corps_id');
    }
}

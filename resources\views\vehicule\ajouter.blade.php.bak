@extends('admin.admin_dashboard')
@section('admin')

<style>
    :root {
        --primary: #4361ee;
        --primary-light: #eaefff;
        --primary-dark: #3a56d4;
        --secondary: #6c757d;
        --success: #2ecc71;
        --info: #3498db;
        --warning: #f39c12;
        --danger: #e74c3c;
        --light: #f8f9fa;
        --dark: #343a40;
        --gray-100: #f8f9fa;
        --gray-200: #e9ecef;
        --gray-300: #dee2e6;
        --gray-400: #ced4da;
        --gray-500: #adb5bd;
        --gray-600: #6c757d;
        --gray-700: #495057;
        --gray-800: #343a40;
        --gray-900: #212529;
        --transition: all 0.3s ease;
        --border-radius: 0.5rem;
        --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* Styles généraux */
    .page-content {
        background-color: #f5f7fb;
        min-height: 100vh;
        padding: 1.5rem;
    }

    .page-header {
        background-color: var(--primary);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--box-shadow-lg);
    }

    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.85);
        margin-bottom: 0;
    }

    .breadcrumb {
        margin-bottom: 0;
        padding: 0.5rem 0;
    }

    .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
    }

    .breadcrumb-item.active {
        color: rgba(255, 255, 255, 0.7);
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.5);
    }

    /* Styles du formulaire */
    .form-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .form-card-header {
        background-color: var(--primary-light);
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .form-card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary);
        margin-bottom: 0;
    }

    .form-card-body {
        padding: 1.5rem;
    }
    
    /* Style spécifique pour le champ Direction/Structure */
    .full-width-select {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        display: block !important;
    }
    
    /* Correction pour les champs select col-md-4 */
    .col-md-4 .input-group {
        width: 100% !important;
        display: flex !important;
    }
    
    .col-md-4 .form-select {
        width: 100% !important;
        flex: 1 1 auto !important;
        color: #000000 !important;
    }
    
    .full-width-select .input-group {
        width: 100% !important;
        max-width: 100% !important;
        display: flex !important;
    }
    
    .full-width-select .form-select {
        width: 100% !important;
        flex: 1 1 auto !important;
        color: #000000 !important;
        font-weight: 500 !important;
    }
    
    /* Correction pour Choices.js */
    .full-width-select .choices {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    .full-width-select .choices__inner {
        width: 100% !important;
        min-width: 100% !important;
        color: #000000 !important;
    }
    
    .choices__list--single {
        color: #000000 !important;
        font-weight: 500 !important;
    }
    
    .choices__list--dropdown .choices__item {
        color: #000000 !important;
    }
    
    /* Correction pour les listes déroulantes */
    .form-select {
        position: relative;
        z-index: 5 !important; /* Z-index plus élevé pour s'assurer que la liste s'affiche au-dessus des autres éléments */
        height: auto !important;
    }
    
    .form-select option {
        color: #000000 !important;
        background-color: #ffffff !important;
        padding: 8px !important;
        font-size: 14px !important;
    }
    
    /* Assurer que les listes déroulantes s'affichent correctement */
    select.form-select {
        overflow: visible !important;
        appearance: auto !important; /* Rétablir l'apparence native du select */
        -webkit-appearance: auto !important;
        -moz-appearance: auto !important;
    }
    
    /* Correction pour les listes déroulantes qui s'affichent en dessous */
    .input-group .form-select {
        overflow: visible !important;
        position: relative !important;
    }
    
    /* Styles pour le dropdown */
    .dropdown-menu {
        width: 100% !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        z-index: 9999 !important;
    }
    
    /* Assurer que les options sont visibles */
    select.form-select[size], select.form-select[multiple] {
        height: auto !important;
    }
    
    /* Correction pour Bootstrap */
    .form-select:not([multiple]):not([size]) {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
        background-position: right 0.75rem center !important;
        background-repeat: no-repeat !important;
        background-size: 16px 12px !important;
    }

    .form-label {
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        transition: var(--transition);
        color: #000000;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    }

    .input-group-text {
        background-color: var(--primary-light);
        border-color: var(--gray-300);
        color: var(--primary);
    }

    .btn-primary {
        background-color: var(--primary);
        border-color: var(--primary);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    /* Image preview */
    .image-preview-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        transition: var(--transition);
        cursor: pointer;
    }

    .image-preview-container:hover {
        border-color: var(--primary);
    }

    .image-preview-placeholder {
        color: var(--gray-500);
        margin-bottom: 1rem;
    }

    .image-preview-placeholder i {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    .image-preview {
        max-width: 100%;
        max-height: 250px;
        border-radius: 0.375rem;
        margin-top: 1rem;
        box-shadow: var(--box-shadow);
    }

    .image-details {
        background-color: var(--gray-100);
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-top: 1rem;
        font-size: 0.875rem;
    }

    .image-details-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .image-details-item:last-child {
        margin-bottom: 0;
    }

    .image-details-label {
        font-weight: 500;
        color: var(--gray-700);
    }

    .image-details-value {
        color: var(--gray-600);
    }

    /* Styles pour Choices.js */
    .choices__inner {
        background-color: #fff;
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        min-height: 38px;
        padding: 0.375rem 0.75rem;
    }

    .choices__list--dropdown {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: var(--box-shadow);
    }

    .choices__list--dropdown .choices__item--selectable.is-highlighted {
        background-color: var(--primary);
        color: white;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-content {
            padding: 1rem;
        }

        .page-header {
            padding: 1rem;
        }

        .form-card-body {
            padding: 1rem;
        }
    }
</style>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Choices CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css" />

<!-- Choices JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/scripts/choices.min.js"></script>

<!-- Ajout des liens vers Bootstrap-select -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>

<div class="page-content">
    <!-- En-tête de page -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">

    <!-- Ajout des liens vers Bootstrap-select -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>

    <div class="page-content">
        <!-- En-tête de page -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="page-title">
                        <i class='bx bxs-car-garage me-2'></i>Ajout d'un Véhicule
                    </h4>
                    <p class="page-subtitle">Enregistrez un nouveau véhicule dans le parc automobile</p>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i> Accueil</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('pageListeVehicule') }}">Parc Automobile</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Nouveau Véhicule</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('pageListeVehicule') }}" class="btn btn-outline-light">
                        <i class="bx bx-arrow-back me-1"></i> Retour à la liste
                    </a>
                </div>
            <div>
                <h4 class="page-title">
                    <i class='bx bxs-car-garage me-2'></i>Ajout d'un Véhicule
                </h4>
                <p class="page-subtitle">Enregistrez un nouveau véhicule dans le parc automobile</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i> Accueil</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('pageListeVehicule') }}">Parc Automobile</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Nouveau Véhicule</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="{{ route('pageListeVehicule') }}" class="btn btn-outline-light">
                    <i class="bx bx-arrow-back me-1"></i> Retour à la liste
                </a>
            </div>
        </div>
    </div>

    <!-- Formulaire -->
    <div class="row">
        <div class="col-lg-12">
            <form id="vehicleForm" action="{{ route('enregistrer.engin') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('POST')

                <!-- Informations générales -->
                <div class="form-card mb-4">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-info-circle me-2"></i>Informations Générales</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row g-3">
                            <!-- Direction/Structure -->
                            <div class="col-12 full-width-select" style="width: 100% !important; display: block !important;">
                                <label for="departement_id" class="form-label">Direction / Structure</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-buildings"></i></span>
                                    <select id="departement_id" name="direction_structure" class="form-select" style="width: 100% !important; flex-grow: 1 !important;" required>
                                        <option value="">Choisir la Direction / Structure...</option>
                                        @foreach($departements as $departement)
                                            <option value="{{ $departement->id }}">{{ $departement->nom_departement }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Immatriculation -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="immatriculation" class="form-label">Immatriculation</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-id-card"></i></span>
                                    <input type="text" class="form-control" id="immatriculation" name="immatriculation" value="{{ old('immatriculation') }}" placeholder="Ex: AB-1234-CD" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                </div>
                            </div>

                            <!-- Genre -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="genre" class="form-label">Genre</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-category"></i></span>
                                    <select name="genre" id="genre" class="form-select" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                        <option value="">Choisir le Genre...</option>
                                        <option value="Voiture">Voiture</option>
                                        <option value="Moto">Moto</option>
                                        <option value="Tête Porte Chars">Tête Porte Chars</option>
                                        <option value="Porte Chars">Porte Chars</option>
                                        <option value="Tricycle">Tricycle</option>
                                        <option value="Camion">Camion</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Marque -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="marque" class="form-label">Marque</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bxs-car"></i></span>
                                    <select id="marque" name="marque" class="form-select" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                        <option value="">Choisir la Marque...</option>
                                        <!-- Voitures -->
                                        <optgroup label="Voitures">
                                            <option value="Abarth">Abarth</option>
                                            <option value="Acura">Acura</option>
                                            <option value="Alfa Romeo">Alfa Romeo</option>
                                            <!-- Autres marques de voitures -->
                                        </optgroup>
                                        <!-- Motos -->
                                        <optgroup label="Motos">
                                            <option value="Aprilia">Aprilia</option>
                                            <option value="BMW Motorrad">BMW Motorrad</option>
                                            <!-- Autres marques de motos -->
                                        </optgroup>
                                        <!-- Camions -->
                                        <optgroup label="Camions">
                                            <option value="DAF">DAF</option>
                                            <option value="Iveco">Iveco</option>
                                            <!-- Autres marques de camions -->
                                        </optgroup>
                                    </select>
                                </div>
                            </div>

                            <!-- Type -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="type" class="form-label">Type</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-car"></i></span>
                                    <select id="type" name="type" class="form-select" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                        <option value="">Choisir le Type...</option>
                                        <!-- Options de type -->
                                    </select>
                                </div>
                            </div>

                            <!-- Valeur d'acquisition -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="valeur_acquisition" class="form-label">Valeur d'Acquisition</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-money"></i></span>
                                    <input type="number" class="form-control" id="valeur_acquisition" name="valeur_acquisition" value="{{ old('valeur_acquisition') }}" placeholder="Montant en FCFA" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                    <span class="input-group-text">FCFA</span>
                                </div>
                            </div>

                            <!-- Usage -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="usage" class="form-label">Usage</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-info-circle"></i></span>
                                    <select name="usage" id="usage" class="form-select" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                        <option value="">Choisir l'Usage...</option>
                                        <option value="Privé">Privé</option>
                                        <option value="Taxi">Taxi</option>
                                        <!-- Autres options d'usage -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Caractéristiques techniques -->
                <div class="form-card mb-4">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-cog me-2"></i>Caractéristiques Techniques</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row g-3">
                            <!-- État -->
                            <div class="col-md-3 full-width-select" style="width: 25% !important; flex: 0 0 25% !important;">
                                <label for="etat" class="form-label">État</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-health"></i></span>
                                    <select name="etat" id="etat" class="form-select" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                        <option value="">Choisir l'État...</option>
                                        <option value="Bon">Bon</option>
                                        <option value="Passable">Passable</option>
                                        <option value="En Panne">En Panne</option>
                                        <option value="Hors Service">Hors Service</option>
                                    </select>
                                </div>
                            </div>

                            <!-- PTC -->
                            <div class="col-md-3 full-width-select" style="width: 25% !important; flex: 0 0 25% !important;">
                                <label for="ptc" class="form-label">Poids Total en Charge</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-weight"></i></span>
                                    <input type="text" class="form-control" id="ptc" name="ptc" value="{{ old('ptc') }}" placeholder="Ex: 3500 kg" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;">
                                </div>
                            </div>

                            <!-- Puissance -->
                            <div class="col-md-3 full-width-select" style="width: 25% !important; flex: 0 0 25% !important;">
                                <label for="puissance" class="form-label">Puissance</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-power-off"></i></span>
                                    <input type="text" class="form-control" id="puissance" name="puissance" value="{{ old('puissance') }}" placeholder="Ex: 110 CV" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;">
                                </div>
                            </div>

                            <!-- Énergie -->
                            <div class="col-md-3">
                                <label for="energie" class="form-label">Énergie</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bx bx-gas-pump"></i></span>
                                    <select name="energie" id="energie" class="form-select" required>
                                        <option value="">Choisir l'Énergie...</option>
                                        <option value="Essence">Essence</option>
                                        <option value="Gasoil">Gasoil</option>
                                        <option value="Électrique">Électrique</option>
                                        <option value="Hybride">Hybride</option>
                                        <option value="GPL">GPL</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Utilisation -->
                <div class="form-card mb-4">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-user me-2"></i>Utilisation</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row g-3">
                            <!-- Utilisateur -->
                            <div class="col-md-6 full-width-select" style="width: 50% !important; flex: 0 0 50% !important;">
                                <label for="utilisateur" class="form-label">Utilisateur</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-user"></i></span>
                                    <input type="text" class="form-control" id="utilisateur" name="utilisateur" value="{{ old('utilisateur') }}" placeholder="Nom de l'utilisateur" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                </div>
                            </div>

                            <!-- Service -->
                            <div class="col-md-6 full-width-select" style="width: 50% !important; flex: 0 0 50% !important;">
                                <label for="service" class="form-label">Service</label>
                                <div class="input-group" style="width: 100% !important; display: flex !important;">
                                    <span class="input-group-text"><i class="bx bx-building"></i></span>
                                    <input type="text" class="form-control" id="service" name="service" value="{{ old('service') }}" placeholder="Service ou bureau d'utilisation" style="width: 100% !important; flex-grow: 1 !important; color: #000000 !important;" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Photo du véhicule -->
                <div class="form-card mb-4">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-image me-2"></i>Photo du Véhicule</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <div class="image-preview-container" id="dropzone" onclick="document.getElementById('image').click()">
                                    <input type="file" class="d-none" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                                    <div id="preview-placeholder" class="image-preview-placeholder">
                                        <i class="bx bx-image-add"></i>
                                        <p>Cliquez ou déposez une image ici</p>
                                        <small class="text-muted">Formats acceptés: JPG, PNG, GIF (Max: 2MB)</small>
                                    </div>
                                    <img id="showImage" class="image-preview d-none" src="{{ url('upload/no_image.jpg') }}" alt="Photo du véhicule">
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div id="imageDetails" class="image-details d-none">
                                    <h6 class="mb-3">Détails de l'image</h6>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Nom du fichier:</span>
                                        <span id="fileName" class="image-details-value"></span>
                                    </div>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Taille:</span>
                                        <span id="fileSize" class="image-details-value"></span>
                                    </div>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Type:</span>
                                        <span id="fileType" class="image-details-value"></span>
                                    </div>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Dimensions:</span>
                                        <span id="dimensions" class="image-details-value"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-flex justify-content-end gap-2 mb-4">
                    <a href="{{ route('pageListeVehicule') }}" class="btn btn-light">
                        <i class="bx bx-x me-1"></i>Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bx bx-save me-1"></i>Enregistrer le véhicule
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Prévisualisation de l'image
function previewImage(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        var file = input.files[0];
        
        reader.onload = function(e) {
            // Afficher l'image
            $('#showImage').attr('src', e.target.result).removeClass('d-none');
            $('#preview-placeholder').addClass('d-none');
            
            // Afficher les détails du fichier
            $('#fileName').text(file.name);
            $('#fileSize').text((file.size / 1024).toFixed(2) + ' KB');
            $('#fileType').text(file.type);
            
            // Obtenir les dimensions de l'image
            var img = new Image();
            img.onload = function() {
                $('#dimensions').text(this.width + ' x ' + this.height + ' px');
            };
            img.src = e.target.result;
            
            // Afficher la section de détails
            $('#imageDetails').removeClass('d-none');
        }
        
        reader.readAsDataURL(file);
    }
}

// Initialisation de Choices.js pour les selects
document.addEventListener('DOMContentLoaded', function() {
    // Configuration de base pour tous les selects
    const configChoices = {
        removeItemButton: true,
        searchEnabled: true,
        searchPlaceholderValue: 'Rechercher...',
        noResultsText: 'Aucun résultat trouvé',
        noChoicesText: 'Aucun choix disponible',
        itemSelectText: 'Cliquer pour sélectionner',
        shouldSort: false,
        position: 'bottom'
    };
    
    // Initialiser les selects principaux
    const departementSelect = new Choices('#departement_id', configChoices);
    const genreSelect = new Choices('#genre', configChoices);
    const marqueSelect = new Choices('#marque', configChoices);
    const typeSelect = new Choices('#type', configChoices);
    const etatSelect = new Choices('#etat', configChoices);
    const energieSelect = new Choices('#energie', configChoices);
    const usageSelect = new Choices('#usage', configChoices);
    
    // Filtrer les types en fonction du genre sélectionné
    genreSelect.passedElement.element.addEventListener('change', function() {
        const genre = this.value;
        typeSelect.clearChoices();
        
        if (genre === 'Voiture') {
            typeSelect.setChoices([
                {value: '', label: 'Choisir le Type...', disabled: true},
                {value: 'Berline', label: 'Berline'},
                {value: 'Break', label: 'Break'},
                {value: 'Citadine', label: 'Citadine'},
                {value: 'Coupé', label: 'Coupé'},
                {value: 'SUV', label: 'SUV'},
                {value: 'Monospace', label: 'Monospace'}
            ]);
        } else if (genre === 'Camion') {
            typeSelect.setChoices([
                {value: '', label: 'Choisir le Type...', disabled: true},
                {value: 'Benne', label: 'Benne'},
                {value: 'Citerne', label: 'Citerne'},
                {value: 'Plateau', label: 'Plateau'},
                {value: 'Frigorifique', label: 'Frigorifique'}
            ]);
        } else if (genre === 'Moto') {
            typeSelect.setChoices([
                {value: '', label: 'Choisir le Type...', disabled: true},
                {value: 'Sport', label: 'Sport'},
                {value: 'Routière', label: 'Routière'},
                {value: 'Custom', label: 'Custom'},
                {value: 'Trail', label: 'Trail'}
            ]);
        }
    });
    
    // Support du drag and drop pour l'image
    const dropzone = document.getElementById('dropzone');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropzone.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropzone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropzone.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropzone.classList.add('border-primary');
    }
    
    function unhighlight() {
        dropzone.classList.remove('border-primary');
    }
    
    dropzone.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length) {
            document.getElementById('image').files = files;
            previewImage(document.getElementById('image'));
        }
    }
});
    // Amélioration des listes déroulantes
    document.addEventListener('DOMContentLoaded', function() {
        // Appliquer des styles aux options des listes déroulantes
        const selects = document.querySelectorAll('select.form-select');
        
        selects.forEach(function(select) {
            // S'assurer que la liste déroulante s'affiche correctement
            select.style.color = '#000000';
            
            // Ajouter des événements pour gérer l'affichage
            select.addEventListener('focus', function() {
                this.style.zIndex = '9999';
                // Ouvrir temporairement le select pour qu'il affiche toutes les options
                if (!this.multiple) {
                    this.size = 4;
                }
            });
            
            select.addEventListener('blur', function() {
                this.style.zIndex = '5';
                // Remettre à la taille normale
                if (!this.multiple) {
                    this.size = 1;
                }
            });
            
            select.addEventListener('change', function() {
                // Remettre à la taille normale après sélection
                if (!this.multiple) {
                    this.size = 1;
                    this.blur();
                }
            });
            
            // Assurer que les options sont visibles
            const options = select.querySelectorAll('option');
            options.forEach(function(option) {
                option.style.padding = '8px';
                option.style.color = '#000000';
                option.style.backgroundColor = '#ffffff';
                option.style.display = 'block';
            });
        });
        
        // Correction pour les listes déroulantes dans les colonnes
        const fullWidthSelects = document.querySelectorAll('.full-width-select select');
        fullWidthSelects.forEach(function(select) {
            const parent = select.closest('.full-width-select');
            const width = parent.offsetWidth;
            
            // S'assurer que le select a la bonne largeur
            select.style.width = '100%';
            select.style.maxWidth = width + 'px';
            
            // Assurer que les options ont la même largeur
            select.addEventListener('focus', function() {
                const options = this.querySelectorAll('option');
                options.forEach(function(option) {
                    option.style.width = width + 'px';
                    option.style.maxWidth = width + 'px';
                });
            });
        });
    });
</script>

@endsection

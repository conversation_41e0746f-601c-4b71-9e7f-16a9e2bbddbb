<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Article extends Model
{
    use HasFactory;
    use \Illuminate\Database\Eloquent\SoftDeletes;
    
    protected $fillable = [
        'code_article',
        'designation',
        'categorie',
        'unite_id',
        'prix_unitaire_estime',
        'stock_minimum',
        'actif'
    ];
    
    public function unite()
    {
        return $this->belongsTo(Unite::class);
    }
    
    public function entrees()
    {
        return $this->hasMany(Entree::class);
    }
    
    public function sorties()
    {
        return $this->hasMany(Sortie::class);
    }
    
    public function immobilisations()
    {
        return $this->hasMany(Immobilisation::class);
    }
    
    public function stockActuel()
    {
        $entrees = $this->entrees()->sum('quantite');
        $sorties = $this->sorties()->sum('quantite');
        return $entrees - $sorties;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Employee extends Model
{
    use HasFactory;
    
    // Spécifier le nom de la table existante
    protected $table = 'employee';
    
    // Désactiver les timestamps (created_at et updated_at)
    public $timestamps = false;
    
    // Spécifier la clé primaire
    protected $primaryKey = 'em_id';
    
    protected $fillable = [
        'em_code',
        'first_name',
        'last_name',
        'position_held',
        'dep_id',
        'corps_id',
        'em_joining_date',
        'em_phone',
        'em_address',
        'em_email',
        'em_gender',
        'em_birthday',
        'em_certificate',
        'em_levelofstudy',
        'marital_status',
        'em_ending_date',
        'statut',
        'category',
        'classe',
        'echelon',
        'grade',
        'em_type',
        'em_region',
        'em_image'
    ];
    
    public function departement()
    {
        return $this->belongsTo(Departement::class, 'dep_id');
    }
    
    public function corps()
    {
        return $this->belongsTo(Corps::class, 'corps_id');
    }
}

@extends('admin.admin_dashboard')
@section('admin')
<!-- Ajout des styles personnalisés -->
<link rel="stylesheet" href="{{ asset('css/reception-liste.css') }}">
<!-- Ajout de DataTables -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
<!-- Ajout de SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.19/dist/sweetalert2.min.css">

<div class="page-content">
    <!-- En-tête avec titre et actions -->
    <div class="reception-header animate-fade-in">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h4><i class='bx bx-list-check me-2'></i>Gestion des Réceptions de Marchés</h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 p-0 mt-2">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i> Accueil</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Liste des Réceptions</li>
                    </ol>
                </nav>
            </div>
            <div class="col-lg-6">
                <div class="action-buttons text-lg-end mt-3 mt-lg-0">
                    <button id="theme-toggle" class="btn btn-sm btn-light me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Changer de thème">
                        <i class='bx bx-moon'></i>
                    </button>
                    <a href="{{ route('ajouter_reception') }}" class="btn btn-reception">
                        <i class='bx bx-plus-circle'></i> Nouvelle Réception
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Filtres et recherche -->
    <div class="filters-container animate-fade-in">
        <div class="search-container">
            <i class='bx bx-search'></i>
            <input type="text" id="global-search" class="form-control" placeholder="Rechercher une réception...">
        </div>
        <div class="filter-dropdown">
            <select id="quick-filter-status" class="form-select">
                <option value="all">Tous les statuts</option>
                <option value="with-pv">Avec PV</option>
                <option value="without-pv">Sans PV</option>
            </select>
        </div>
        <div class="filter-dropdown">
            <select id="date-filter" class="form-select">
                <option value="all">Toutes les dates</option>
                <option value="today">Aujourd'hui</option>
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois</option>
                <option value="year">Cette année</option>
            </select>
        </div>
    </div>
    
    <!-- Actions en lot (initialement cachées) -->
    <div class="bulk-actions d-none animate-fade-in mb-3">
        <div class="alert alert-info d-flex align-items-center justify-content-between">
            <div>
                <i class='bx bx-check-square me-2'></i>
                <span class="selected-count">0</span> élément(s) sélectionné(s)
            </div>
            <div>
                <button id="bulk-delete" class="btn btn-sm btn-danger">
                    <i class='bx bx-trash me-1'></i> Supprimer la sélection
                </button>
            </div>
        </div>
    </div>
    
    <!-- Carte principale -->
    <div class="reception-card animate-fade-in">
        <div class="card-header">
            <h5 class="card-title">
                <i class='bx bx-list-ul me-2'></i> Liste des réceptions
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="example2" class="reception-table table">
                    <thead>
                        <tr>
                            <th width="30px">
                                <label class="custom-checkbox">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmark"></span>
                                </label>
                            </th>
                            <th>Date</th>
                            <th>Structure</th>
                            <th>Réf. Courier</th>
                            <th>Réf. Marché</th>
                            <th>Objet</th>
                            <th>Période</th>
                            <th>Exécutant</th>
                            <th>PV</th>
                            <th width="120px">Actions</th>
                        </tr>
                        <!-- Ligne de filtres par colonne -->
                        <tr class="filters-row">
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($receptions as $key => $item)
                        <tr class="scroll-animation">
                            <td>
                                <label class="custom-checkbox">
                                    <input type="checkbox" class="row-checkbox" data-id="{{ $item->id }}">
                                    <span class="checkmark"></span>
                                </label>
                            </td>
                            <td>
                                {{ \Illuminate\Support\Carbon::parse($item->date_enregistrement)->format('d-m-Y') }}
                            </td>
                            <td>
                                <span class="text-primary">{{ $item->departement->nom_departement ?? 'Non défini' }}</span>
                            </td>
                            <td>
                                <strong>{{ $item->reference_courier }}</strong>
                            </td>
                            <td>
                                {{ $item->reference_marche }}
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 200px;" data-bs-toggle="tooltip" title="{{ $item->objet }}">
                                    {{ $item->objet }}
                                </div>
                            </td>
                            <td>
                                {{ $item->periode_execution }}
                            </td>
                            <td>
                                {{ $item->executant }}
                            </td>
                            <td>
                                @if ($item->pv_reception)
                                    <span class="badge badge-success pv-status checked">
                                        <i class='bx bx-check'></i> Disponible
                                    </span>
                                @else
                                    <span class="badge badge-warning pv-status">
                                        <i class='bx bx-x'></i> Non disponible
                                    </span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex justify-content-around">
                                    <a href="{{ route('reception.details', $item->id) }}" class="btn btn-sm btn-info me-1" data-bs-toggle="tooltip" title="Voir détails">
                                        <i class='bx bx-detail'></i>
                                    </a>
                                    
                                    @if ($item->pv_reception)
                                        @php
                                            $filename = basename($item->pv_reception);
                                        @endphp
                                        <a href="{{ route('documents.show_pv', $filename) }}" class="btn btn-sm btn-success me-1" data-bs-toggle="tooltip" title="Voir PV">
                                            <i class='bx bx-file'></i>
                                        </a>
                                    @endif
                                    
                                    <a href="{{ route('editer_reception', $item->id) }}" class="btn btn-sm btn-primary me-1" data-bs-toggle="tooltip" title="Modifier">
                                        <i class='bx bx-edit'></i>
                                    </a>
                                    
                                    <a href="{{ route('supprimer_reception', $item->id) }}" class="btn btn-sm btn-danger delete-reception-btn" data-bs-toggle="tooltip" title="Supprimer">
                                        <i class='bx bx-trash'></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal de détails -->
<div class="modal fade modal-reception" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">Détails de la réception</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Le contenu sera chargé dynamiquement -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de prévisualisation PV -->
<div class="modal fade modal-reception" id="pvPreviewModal" tabindex="-1" aria-labelledby="pvPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pvPreviewModalLabel">Prévisualisation du PV</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Le contenu sera chargé dynamiquement -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Bouton flottant d'ajout -->
<a href="{{ route('ajouter_reception') }}" class="floating-btn" data-bs-toggle="tooltip" data-bs-placement="left" title="Ajouter une nouvelle réception">
    <i class='bx bx-plus'></i>
</a>

<!-- DataTables -->
<script src="{{ asset('plugins/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-responsive/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-responsive/js/responsive.bootstrap4.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-buttons/js/dataTables.buttons.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-buttons/js/buttons.bootstrap4.min.js') }}"></script>
<script src="{{ asset('plugins/jszip/jszip.min.js') }}"></script>
<script src="{{ asset('plugins/pdfmake/pdfmake.min.js') }}"></script>
<script src="{{ asset('plugins/pdfmake/vfs_fonts.js') }}"></script>
<script src="{{ asset('plugins/datatables-buttons/js/buttons.html5.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-buttons/js/buttons.print.min.js') }}"></script>
<script src="{{ asset('plugins/datatables-buttons/js/buttons.colVis.min.js') }}"></script>

<!-- Choices.js -->
<script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>

<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>

<!-- Custom JS -->
<script src="{{ asset('js/reception-liste.js') }}"></script>
<script src="{{ asset('js/reception-delete.js') }}"></script>
@endsection
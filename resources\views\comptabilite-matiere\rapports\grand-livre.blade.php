@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Grand Livre</h1>
        <div>
            <a href="{{ route('rapports.grand-livre-form') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">
                <i class="fas fa-sync fa-sm text-white-50"></i> Modifier les paramètres
            </a>
            <a href="{{ route('rapports.grand-livre', ['date_debut' => $dateDebut->format('Y-m-d'), 'date_fin' => $dateFin->format('Y-m-d'), 'export_pdf' => 1]) }}" class="d-none d-sm-inline-block btn btn-sm btn-danger shadow-sm">
                <i class="fas fa-file-pdf fa-sm text-white-50"></i> Exporter en PDF
            </a>
        </div>
    </div>

    <!-- Informations de la période -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">Informations générales</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Période :</th>
                            <td>Du {{ $dateDebut->format('d/m/Y') }} au {{ $dateFin->format('d/m/Y') }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Nombre d'articles :</th>
                            <td>{{ count($grandLivre) }} articles</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau du Grand Livre -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">Grand Livre des Stocks</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Désignation</th>
                            <th>Unité</th>
                            <th>Stock Initial</th>
                            <th>Entrées</th>
                            <th>Sorties</th>
                            <th>Stock Final</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($grandLivre as $ligne)
                        <tr>
                            <td>{{ $ligne['article']->code_article }}</td>
                            <td>{{ $ligne['article']->designation }}</td>
                            <td>{{ $ligne['article']->unite->libelle }}</td>
                            <td class="text-right">{{ $ligne['stock_initial'] }}</td>
                            <td class="text-right">{{ $ligne['entrees'] }}</td>
                            <td class="text-right">{{ $ligne['sorties'] }}</td>
                            <td class="text-right font-weight-bold">
                                @if($ligne['stock_final'] <= 0 && $ligne['article']->seuil_alerte > 0)
                                    <span class="text-danger">{{ $ligne['stock_final'] }}</span>
                                @elseif($ligne['stock_final'] <= $ligne['article']->seuil_alerte && $ligne['article']->seuil_alerte > 0)
                                    <span class="text-warning">{{ $ligne['stock_final'] }}</span>
                                @else
                                    <span class="text-success">{{ $ligne['stock_final'] }}</span>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row">
        <!-- Articles en rupture de stock -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Articles en rupture</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ count(array_filter($grandLivre, function($item) { return $item['stock_final'] <= 0; })) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Articles sous le seuil d'alerte -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Articles sous seuil d'alerte</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ count(array_filter($grandLivre, function($item) { 
                                    return $item['stock_final'] > 0 && 
                                           $item['stock_final'] <= $item['article']->seuil_alerte && 
                                           $item['article']->seuil_alerte > 0; 
                                })) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Articles avec mouvements -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Articles avec mouvements</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ count(array_filter($grandLivre, function($item) { 
                                    return $item['entrees'] > 0 || $item['sorties'] > 0; 
                                })) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Articles sans mouvement -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                Articles sans mouvement</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ count(array_filter($grandLivre, function($item) { 
                                    return $item['entrees'] == 0 && $item['sorties'] == 0; 
                                })) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pause-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "order": [[ 1, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
    });
</script>
@endsection

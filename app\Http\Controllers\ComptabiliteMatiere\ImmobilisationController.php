<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Departement;
use App\Models\Immobilisation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ImmobilisationController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $immobilisations = Immobilisation::with(['article', 'serviceAffecte'])
            ->orderBy('date_acquisition', 'desc')
            ->get();
        
        return view('comptabilite-matiere.immobilisations.index', compact('immobilisations'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Récupérer uniquement les articles de type immobilisable
        $articles = Article::where('type', 'immobilisable')
            ->orderBy('designation', 'asc')
            ->pluck('designation', 'id');
            
        $services = Departement::orderBy('nom', 'asc')->pluck('nom', 'id');
        
        $etatsPhysiques = [
            'neuf' => 'Neuf',
            'bon' => 'Bon état',
            'moyen' => 'État moyen',
            'mauvais' => 'Mauvais état'
        ];
        
        $statuts = [
            'actif' => 'Actif',
            'en_maintenance' => 'En maintenance',
            'reserve' => 'En réserve'
        ];
        
        return view('comptabilite-matiere.immobilisations.create', compact('articles', 'services', 'etatsPhysiques', 'statuts'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'numero_inventaire' => 'required|string|max:50|unique:immobilisations',
            'date_acquisition' => 'required|date',
            'valeur_acquisition' => 'required|numeric|min:0',
            'service_affecte_id' => 'required|exists:departements,id',
            'etat_physique' => 'required|string|in:neuf,bon,moyen,mauvais',
            'statut' => 'required|string|in:actif,en_maintenance,reserve',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('immobilisations.create')
                ->withErrors($validator)
                ->withInput();
        }
        
        // Vérifier que l'article est bien de type immobilisable
        $article = Article::findOrFail($request->article_id);
        if ($article->type !== 'immobilisable') {
            return redirect()->route('immobilisations.create')
                ->with('error', 'Cet article n\'est pas de type immobilisable.')
                ->withInput();
        }

        Immobilisation::create([
            'article_id' => $request->article_id,
            'numero_inventaire' => $request->numero_inventaire,
            'date_acquisition' => $request->date_acquisition,
            'valeur_acquisition' => $request->valeur_acquisition,
            'service_affecte_id' => $request->service_affecte_id,
            'etat_physique' => $request->etat_physique,
            'statut' => $request->statut,
            'description' => $request->description,
        ]);

        return redirect()->route('immobilisations.index')
            ->with('success', 'Immobilisation enregistrée avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $immobilisation = Immobilisation::with(['article', 'article.unite', 'serviceAffecte', 'mutations', 'reformes'])
            ->findOrFail($id);
        
        return view('comptabilite-matiere.immobilisations.show', compact('immobilisation'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $immobilisation = Immobilisation::findOrFail($id);
        
        // Récupérer uniquement les articles de type immobilisable
        $articles = Article::where('type', 'immobilisable')
            ->orderBy('designation', 'asc')
            ->pluck('designation', 'id');
            
        $services = Departement::orderBy('nom', 'asc')->pluck('nom', 'id');
        
        $etatsPhysiques = [
            'neuf' => 'Neuf',
            'bon' => 'Bon état',
            'moyen' => 'État moyen',
            'mauvais' => 'Mauvais état'
        ];
        
        $statuts = [
            'actif' => 'Actif',
            'en_maintenance' => 'En maintenance',
            'reserve' => 'En réserve'
        ];
        
        return view('comptabilite-matiere.immobilisations.edit', compact('immobilisation', 'articles', 'services', 'etatsPhysiques', 'statuts'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $immobilisation = Immobilisation::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'numero_inventaire' => 'required|string|max:50|unique:immobilisations,numero_inventaire,' . $id,
            'date_acquisition' => 'required|date',
            'valeur_acquisition' => 'required|numeric|min:0',
            'service_affecte_id' => 'required|exists:departements,id',
            'etat_physique' => 'required|string|in:neuf,bon,moyen,mauvais',
            'statut' => 'required|string|in:actif,en_maintenance,reserve',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('immobilisations.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }
        
        // Vérifier que l'article est bien de type immobilisable
        $article = Article::findOrFail($request->article_id);
        if ($article->type !== 'immobilisable') {
            return redirect()->route('immobilisations.edit', $id)
                ->with('error', 'Cet article n\'est pas de type immobilisable.')
                ->withInput();
        }

        $immobilisation->update([
            'article_id' => $request->article_id,
            'numero_inventaire' => $request->numero_inventaire,
            'date_acquisition' => $request->date_acquisition,
            'valeur_acquisition' => $request->valeur_acquisition,
            'service_affecte_id' => $request->service_affecte_id,
            'etat_physique' => $request->etat_physique,
            'statut' => $request->statut,
            'description' => $request->description,
        ]);

        return redirect()->route('immobilisations.index')
            ->with('success', 'Immobilisation mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $immobilisation = Immobilisation::findOrFail($id);
        
        // Vérifier si l'immobilisation a des réformes ou mutations associées
        if ($immobilisation->reformes()->count() > 0 || $immobilisation->mutations()->count() > 0) {
            return redirect()->route('immobilisations.index')
                ->with('error', 'Impossible de supprimer cette immobilisation car elle a des réformes ou mutations associées.');
        }
        
        $immobilisation->delete();

        return redirect()->route('immobilisations.index')
            ->with('success', 'Immobilisation supprimée avec succès.');
    }
}

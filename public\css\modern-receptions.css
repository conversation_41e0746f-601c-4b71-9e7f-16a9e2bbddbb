/* Styles modernes pour la section Réceptions récentes */

.modern-activity-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.modern-activity-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header-modern {
    padding: 24px 28px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.header-title-section {
    flex: 1;
}

.card-title-modern {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
}

.card-title-modern i {
    color: #3498db;
    font-size: 1.4rem;
}

.card-subtitle-modern {
    font-size: 0.875rem;
    color: #6c757d;
    margin: 4px 0 0 0;
}

.btn-modern {
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary-modern {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.btn-primary-modern:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    color: white;
}

.btn-outline-modern {
    background: transparent;
    color: #3498db;
    border: 1px solid #3498db;
}

.btn-outline-modern:hover {
    background: #3498db;
    color: white;
    transform: translateY(-1px);
}

.card-body-modern {
    padding: 0;
}

.reception-item-modern {
    display: flex;
    align-items: flex-start;
    padding: 20px 28px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
}

.reception-item-modern:last-child {
    border-bottom: none;
}

.reception-item-modern:hover {
    background: rgba(52, 152, 219, 0.02);
}

.reception-icon-modern {
    margin-right: 16px;
    flex-shrink: 0;
}

.icon-wrapper-modern {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.icon-wrapper-modern.completed {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.icon-wrapper-modern.pending {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.reception-content-modern {
    flex: 1;
    min-width: 0;
}

.reception-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.reception-reference-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.status-badge-modern {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

.status-badge-modern.completed {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.status-badge-modern.pending {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
    border: 1px solid rgba(243, 156, 18, 0.2);
}

.reception-object-modern {
    font-size: 0.9rem;
    color: #34495e;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.reception-meta-modern {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.meta-item-modern {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: flex;
    align-items: center;
}

.meta-item-modern i {
    color: #95a5a6;
}

.reception-actions-modern {
    display: flex;
    gap: 8px;
    margin-left: 16px;
    flex-shrink: 0;
}

.action-btn-modern {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.action-btn-modern.view {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.action-btn-modern.view:hover {
    background: #3498db;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.action-btn-modern.edit {
    background: rgba(230, 126, 34, 0.1);
    color: #e67e22;
    border: 1px solid rgba(230, 126, 34, 0.2);
}

.action-btn-modern.edit:hover {
    background: #e67e22;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
}

/* État vide */
.empty-state-modern {
    text-align: center;
    padding: 60px 28px;
}

.empty-icon-modern {
    font-size: 4rem;
    color: #bdc3c7;
    margin-bottom: 16px;
}

.empty-title-modern {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.empty-text-modern {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 24px;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.reception-item-modern {
    animation: slideInUp 0.5s ease forwards;
}

.reception-item-modern:nth-child(1) { animation-delay: 0.1s; }
.reception-item-modern:nth-child(2) { animation-delay: 0.2s; }
.reception-item-modern:nth-child(3) { animation-delay: 0.3s; }
.reception-item-modern:nth-child(4) { animation-delay: 0.4s; }

/* Responsive */
@media (max-width: 768px) {
    .card-header-modern {
        padding: 20px;
        flex-direction: column;
        gap: 16px;
    }
    
    .reception-item-modern {
        padding: 16px 20px;
        flex-direction: column;
        gap: 12px;
    }
    
    .reception-header-modern {
        flex-direction: column;
        gap: 8px;
    }
    
    .reception-meta-modern {
        flex-direction: column;
        gap: 8px;
    }
    
    .reception-actions-modern {
        margin-left: 0;
        justify-content: center;
    }
}

<!-- Fenêtre modale simple et épurée pour les détails du véhicule -->
<div class="modal fade" id="detailsModal{{ $item->id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du véhicule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Image du véhicule -->
                <div class="text-center mb-3">
                    <img src="{{ asset($item->image) }}" alt="{{ $item->marque }}" class="img-fluid vehicle-image" style="max-height: 200px; border-radius: 8px;">
                </div>
                
                <!-- Informations générales -->
                <div class="info-section mb-3">
                    <h6 class="info-title">Informations générales</h6>
                    <div class="row">
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Immatriculation</span>
                                <span class="info-value">{{ $item->immatriculation }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Marque</span>
                                <span class="info-value">{{ $item->marque }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Type</span>
                                <span class="info-value">{{ $item->type }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Genre</span>
                                <span class="info-value">{{ $item->genre }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">État</span>
                                <span class="info-value">{{ $item->etat }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Informations techniques -->
                <div class="info-section mb-3">
                    <h6 class="info-title">Informations techniques</h6>
                    <div class="row">
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">PTC</span>
                                <span class="info-value">{{ $item->ptc }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Puissance</span>
                                <span class="info-value">{{ $item->puissance }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Informations financières -->
                <div class="info-section mb-3">
                    <h6 class="info-title">Informations financières</h6>
                    <div class="row">
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Valeur d'acquisition</span>
                                <span class="info-value">{{ number_format($item->valeur_acquisition, 0, ',', ' ') }} F</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Date d'acquisition</span>
                                <span class="info-value">{{ $item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Utilisation -->
                <div class="info-section mb-3">
                    <h6 class="info-title">Utilisation</h6>
                    <div class="row">
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Département</span>
                                <span class="info-value">{{ $item->departement->nom_departement ?? 'Non assigné' }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Utilisateur</span>
                                <span class="info-value">{{ $item->utilisateur ?? 'Non assigné' }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Service</span>
                                <span class="info-value">{{ $item->service_utilisation ?? 'Non spécifié' }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Usage</span>
                                <span class="info-value">{{ $item->usage }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <span class="info-label">Date d'affectation</span>
                                <span class="info-value">{{ $item->date_affectation ? date('d/m/Y', strtotime($item->date_affectation)) : 'N/A' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Observations (si présentes) -->
                @if($item->observation)
                <div class="info-section">
                    <h6 class="info-title">Observations</h6>
                    <p class="observation-text">{{ $item->observation }}</p>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <a href="{{ route('editer.engin', $item->id) }}" class="btn btn-primary">Modifier</a>
            </div>
        </div>
    </div>
</div>

<style>
/* Styles simples pour la fenêtre modale */
.modal-title {
    font-weight: 600;
    color: #333;
}

.vehicle-image {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.info-section {
    margin-bottom: 15px;
}

.info-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.info-item {
    margin-bottom: 8px;
}

.info-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.info-value {
    display: block;
    font-weight: 500;
    color: #333;
}

.observation-text {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    color: #333;
    margin: 0;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}
</style>

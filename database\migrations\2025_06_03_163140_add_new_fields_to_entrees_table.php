<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('entrees', function (Blueprint $table) {
            // Supprimer la contrainte de clé étrangère article_id
            $table->dropForeign(['article_id']);
            $table->dropColumn('article_id');
            
            // Supprimer la colonne source qui est remplacée par piece_justificative
            $table->dropColumn('source');
            
            // Ajouter les nouvelles colonnes
            $table->string('compte_matiere')->after('id');
            $table->string('designation_matiere')->after('compte_matiere');
            $table->string('nature')->after('designation_matiere');
            $table->string('format')->after('nature');
            $table->string('etat')->after('format');
            $table->string('piece_justificative')->nullable()->after('reference_doc');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('entrees', function (Blueprint $table) {
            // Supprimer les nouvelles colonnes
            $table->dropColumn('compte_matiere');
            $table->dropColumn('designation_matiere');
            $table->dropColumn('nature');
            $table->dropColumn('format');
            $table->dropColumn('etat');
            $table->dropColumn('piece_justificative');
            
            // Rétablir les colonnes supprimées
            $table->foreignId('article_id')->constrained('articles');
            $table->string('source');
        });
    }
};

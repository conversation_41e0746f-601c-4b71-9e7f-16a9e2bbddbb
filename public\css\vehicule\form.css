/* Variables */
:root {
    --primary: #4361ee;
    --primary-light: #eaefff;
    --primary-dark: #3a56d4;
    --secondary: #6c757d;
    --success: #2ecc71;
    --info: #3498db;
    --warning: #f39c12;
    --danger: #e74c3c;
    --light: #f8f9fa;
    --dark: #343a40;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --transition: all 0.3s ease;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --card-border-radius: 1rem;
    --input-border-radius: 0.5rem;
    --button-border-radius: 0.5rem;
}

/* General Styles */
.page-content {
    background-color: #f5f7fb;
    background-image: linear-gradient(135deg, #f5f7fb 0%, #edf1fd 100%);
    min-height: 100vh;
    padding: 1.5rem;
}

.page-header {
    background-color: var(--primary);
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: var(--card-border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow-lg);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
    z-index: 0;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 0;
}

.breadcrumb {
    margin-bottom: 0;
    padding: 0.5rem 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.5);
}

/* Form Styles */
.form-card {
    background-color: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.form-card.card-hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-light);
}

.form-card-header {
    background-color: var(--primary-light);
    background-image: linear-gradient(to right, var(--primary-light), #f0f4ff);
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    position: relative;
}

.form-card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--primary), transparent);
}

.form-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.form-card-title i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
    background: var(--primary);
    color: white;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.form-card-body {
    padding: 1.5rem;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    display: block;
}

.form-control, 
.form-select {
    border: 1px solid var(--gray-300);
    border-radius: var(--input-border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    width: 100%;
    font-size: 0.95rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.form-control::placeholder {
    color: var(--gray-500);
    opacity: 0.7;
}

.form-control:focus, 
.form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    transform: translateY(-2px);
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--primary);
    text-align: center;
    white-space: nowrap;
    background-color: var(--primary-light);
    border: 1px solid var(--gray-300);
    border-radius: var(--input-border-radius) 0 0 var(--input-border-radius);
    transition: var(--transition);
}

.input-group:focus-within .input-group-text {
    border-color: var(--primary);
    color: var(--primary-dark);
}

.input-group > .form-control,
.input-group > .form-select {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

/* Full width select */
.full-width-select {
    width: 100% !important;
    margin-bottom: 1rem;
}

.full-width-select .form-select {
    width: 100% !important;
}

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 500;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    border-radius: var(--button-border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    z-index: -2;
}

.btn::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    z-index: -1;
}

.btn:hover::before {
    width: 100%;
}

.btn-with-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.pulse-on-hover:hover {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.btn-primary {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.btn-glass {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Image Preview */
.image-preview-container {
    margin-top: 1rem;
    text-align: center;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 2rem;
    transition: var(--transition);
    cursor: pointer;
}

.image-preview-container:hover {
    border-color: var(--primary);
    background-color: var(--primary-light);
    opacity: 0.9;
}

.shake-error {
    animation: shake 0.5s linear;
}

@keyframes shake {
    0%, 100% {transform: translateX(0);}
    10%, 30%, 50%, 70%, 90% {transform: translateX(-5px);}
    20%, 40%, 60%, 80% {transform: translateX(5px);}
}

/* Validation styles */
.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
    border-color: var(--danger);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid,
.was-validated .form-select:valid {
    border-color: var(--success);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.image-preview {
    max-width: 100%;
    max-height: 200px;
    border-radius: var(--border-radius);
    border: 1px dashed var(--gray-400);
    padding: 1rem;
    margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .page-content {
        padding: 1rem;
    }
    
    .form-card-body {
        padding: 1rem;
    }
    
    .col-md-4, 
    .col-md-6, 
    .col-12 {
        width: 100% !important;
        flex: 0 0 100% !important;
    }
}

/* Fix for Choices.js dropdowns */
.choices__list--dropdown {
    z-index: 1060 !important;
}

.choices__list--dropdown .choices__list {
    max-height: 300px;
    overflow-y: auto;
}

/* Required field indicator */
.required:after {
    content: " *";
    color: var(--danger);
}

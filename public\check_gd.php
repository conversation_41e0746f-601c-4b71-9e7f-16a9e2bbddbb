<?php
// Afficher toutes les extensions PHP chargées
echo "<h1>Extensions PHP chargées</h1>";
echo "<pre>";
print_r(get_loaded_extensions());
echo "</pre>";

// Vérifier spécifiquement l'extension GD
echo "<h2>Vérification de l'extension GD</h2>";
if (extension_loaded('gd')) {
    echo "L'extension GD est bien chargée.";
    
    // Afficher la version de GD
    $gd_info = gd_info();
    echo "<h3>Informations sur GD</h3>";
    echo "<pre>";
    print_r($gd_info);
    echo "</pre>";
} else {
    echo "L'extension GD n'est PAS chargée !";
}

// Vérifier la configuration PHP
echo "<h2>Configuration PHP</h2>";
echo "Version PHP: " . phpversion();
echo "<br>php.ini utilisé: " . php_ini_loaded_file();
?>

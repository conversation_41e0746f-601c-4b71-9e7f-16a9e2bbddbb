<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class VehiculesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tableau de marques de véhicules
        $marques = ['Toyota', 'Renault', 'Peugeot', 'Citroën', 'Ford', 'Volkswagen', 'Mercedes', 'BMW', 'Audi', 'Nissan'];
        
        // Tableau de genres de véhicules
        $genres = ['Berline', 'SUV', 'Utilitaire', 'Camionnette', 'Camion', 'Minibus'];
        
        // Tableau d'états de véhicules
        $etats = ['Neuf', 'Bon état', 'État moyen', 'À réparer', 'Hors service'];
        
        // Tableau d'usages de véhicules
        $usages = ['Service', 'Personnel', 'Transport de personnel', 'Transport de marchandises', 'Missions'];
        
        // Générer 20 véhicules
        for ($i = 1; $i <= 20; $i++) {
            // Générer une immatriculation unique
            $immatriculation = 'AB-' . rand(100, 999) . '-' . chr(rand(65, 90)) . chr(rand(65, 90));
            
            // Date d'acquisition (entre 2018 et 2024)
            $annee_acquisition = rand(2018, 2024);
            $mois_acquisition = rand(1, 12);
            $jour_acquisition = rand(1, 28);
            $date_acquisition = $annee_acquisition . '-' . str_pad($mois_acquisition, 2, '0', STR_PAD_LEFT) . '-' . str_pad($jour_acquisition, 2, '0', STR_PAD_LEFT);
            
            // Date d'affectation (après la date d'acquisition)
            $annee_affectation = $annee_acquisition;
            $mois_affectation = ($mois_acquisition == 12) ? 12 : rand($mois_acquisition, 12);
            $jour_affectation = rand(1, 28);
            $date_affectation = $annee_affectation . '-' . str_pad($mois_affectation, 2, '0', STR_PAD_LEFT) . '-' . str_pad($jour_affectation, 2, '0', STR_PAD_LEFT);
            
            // Insérer le véhicule dans la base de données
            DB::table('vehicules')->insert([
                'immatriculation' => $immatriculation,
                'departement_id' => rand(1, 5), // ID de département aléatoire entre 1 et 5
                'genre' => $genres[array_rand($genres)],
                'marque' => $marques[array_rand($marques)],
                'type' => 'Type ' . rand(1, 5),
                'date_acquisition' => $date_acquisition,
                'date_affectation' => $date_affectation,
                'valeur_acquisition' => rand(1500000, 25000000), // Valeur en FCFA ou autre monnaie locale
                'usage' => $usages[array_rand($usages)],
                'etat' => $etats[array_rand($etats)],
                'ptc' => rand(1000, 5000) . ' kg',
                'puissance' => rand(70, 250) . ' CV',
                'utilisateur' => 'Utilisateur ' . $i,
                'observation' => $i % 3 == 0 ? 'Nécessite une révision prochainement' : null,
                'image' => 'vehicule' . $i . '.jpg',
                'service_utilisation' => 'Service ' . chr(rand(65, 70)),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }
}

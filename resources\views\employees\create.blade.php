@extends('admin.admin_dashboard')
@section('admin')

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

<style>
    :root {
        --primary: #4361ee;
        --primary-light: #4895ef;
        --secondary: #3f37c9;
        --accent: #4cc9f0;
        --success: #4ade80;
        --danger: #f87171;
        --warning: #fbbf24;
        --info: #60a5fa;
        --dark: #374151;
        --light: #f9fafb;
    }

    /* Cards et conteneurs */
    .card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        padding: 1.2rem;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header .header-icon {
        font-size: 1.4rem;
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 1.8rem;
    }

    /* Fil d'ariane */
    .page-breadcrumb {
        background: linear-gradient(to right, #f9fafb, #f3f4f6);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        border-left: 4px solid var(--primary);
    }

    .breadcrumb-item a {
        color: var(--primary);
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .breadcrumb-item a:hover {
        color: var(--secondary);
        text-decoration: none;
    }

    .breadcrumb-item a i {
        margin-right: 0.5rem;
    }

    .breadcrumb-item.active {
        color: var(--dark);
        font-weight: 600;
    }

    /* Formulaire */
    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: var(--dark);
    }

    .form-control {
        border-radius: 10px;
        border: 1px solid #e5e7eb;
        padding: 0.6rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        border-color: var(--primary-light);
    }

    .form-select {
        border-radius: 10px;
        border: 1px solid #e5e7eb;
        padding: 0.6rem 1rem;
        transition: all 0.3s ease;
    }

    .form-select:focus {
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
        border-color: var(--primary-light);
    }

    .select2-container--default .select2-selection--single {
        border-radius: 10px;
        border: 1px solid #e5e7eb;
        height: 42px;
        padding: 0.3rem 0.5rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 42px;
    }

    .select2-container--default .select2-selection--single:focus {
        outline: none;
    }

    .input-group-text {
        border-radius: 10px 0 0 10px;
        background-color: #f3f4f6;
        border: 1px solid #e5e7eb;
        border-right: none;
    }

    /* Boutons */
    .btn {
        border-radius: 10px;
        padding: 0.6rem 1.2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn i {
        font-size: 1.1rem;
    }

    .btn-primary {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(145deg, var(--secondary), var(--primary));
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .btn-success {
        background-color: var(--success);
        border: none;
    }

    /* Animation d'entrée */
    .animate__fadeIn {
        animation-duration: 0.6s;
    }

    .animate__fadeInUp {
        animation-duration: 0.8s;
    }
</style>

<div class="page-content animate__animated animate__fadeIn">
    <!-- Fil d'ariane amélioré -->
    <div class="page-breadcrumb d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3 d-flex align-items-center">
            <i class='bx bx-group me-2 text-primary fs-5'></i>
            <div>
                <h4 class="mb-0">Personnel</h4>
                <p class="mb-0 text-secondary small">Gestion du personnel</p>
            </div>
        </div>
        <div class="ms-auto">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class='bx bx-home-alt'></i> Tableau de bord</a></li>
                <li class="breadcrumb-item"><a href="{{ route('employees.index') }}"><i class='bx bx-group'></i> Personnel</a></li>
                <li class="breadcrumb-item active" aria-current="page">Ajouter un Personnel</li>
            </ol>
        </div>
    </div>

    <!-- Carte principale avec formulaire -->
    <div class="card animate__animated animate__fadeInUp">
        <div class="card-header">
            <div>
                <i class='bx bx-user-plus header-icon'></i>
                Ajouter un Personnel
            </div>
        </div>
        <div class="card-body">
            <!-- Section d'affichage des erreurs et messages -->
            @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <i class='bx bx-error-circle me-2 fs-4'></i>
                    <strong>Erreur!</strong> {{ session('error') }}
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <i class='bx bx-check-circle me-2 fs-4'></i>
                    <strong>Succès!</strong> {{ session('success') }}
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <i class='bx bx-error-circle me-2 fs-4'></i>
                    <div>
                        <strong>Erreurs de validation:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <!-- Début du formulaire -->
            <form action="{{ route('employees.store') }}" method="POST" id="employeeForm">
                @csrf
                
                <!-- Section Informations d'identification -->
                <div class="mb-4">
                    <h5 class="border-start border-primary ps-2 mb-4" style="border-left-width: 4px !important;">Informations d'identification</h5>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="em_code" class="form-label">Matricule <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-id-card'></i></span>
                                <input type="text" class="form-control @error('em_code') is-invalid @enderror" id="em_code" name="em_code" value="{{ old('em_code') }}" placeholder="ex: 123456-A" required>
                            </div>
                            @error('em_code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Format recommandé: 123456-X</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="first_name" class="form-label">Prénom <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('first_name') is-invalid @enderror" id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                            @error('first_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="last_name" class="form-label">Nom <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('last_name') is-invalid @enderror" id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                            @error('last_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <!-- Section Informations professionnelles -->
                <div class="mb-4">
                    <h5 class="border-start border-success ps-2 mb-4" style="border-left-width: 4px !important;">Informations professionnelles</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="position_held" class="form-label">Poste occupé <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-briefcase'></i></span>
                                <input type="text" class="form-control @error('position_held') is-invalid @enderror" id="position_held" name="position_held" value="{{ old('position_held') }}" required>
                            </div>
                            @error('position_held')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="dep_id" class="form-label">Direction/Département <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-building'></i></span>
                                <select class="form-select select2 @error('dep_id') is-invalid @enderror" id="dep_id" name="dep_id" required>
                                    <option value="">Sélectionner une direction</option>
                                    @foreach($departements as $departement)
                                        <option value="{{ $departement->id }}" {{ old('dep_id') == $departement->id ? 'selected' : '' }}>{{ $departement->nom_departement }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @error('dep_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="corps_id" class="form-label">Corps</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-group'></i></span>
                                <select class="form-select select2 @error('corps_id') is-invalid @enderror" id="corps_id" name="corps_id">
                                    <option value="">Sélectionner un corps</option>
                                    @foreach(\App\Models\Corps::all() as $corps)
                                        <option value="{{ $corps->id }}" {{ old('corps_id') == $corps->id ? 'selected' : '' }}>{{ $corps->corps_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @error('corps_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="em_joining_date" class="form-label">Date d'embauche</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                                <input type="date" class="form-control @error('em_joining_date') is-invalid @enderror" id="em_joining_date" name="em_joining_date" value="{{ old('em_joining_date') }}">
                            </div>
                            @error('em_joining_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <!-- Section Coordonnées -->
                <div class="mb-4">
                    <h5 class="border-start border-info ps-2 mb-4" style="border-left-width: 4px !important;">Coordonnées</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-envelope'></i></span>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" placeholder="<EMAIL>">
                            </div>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">Téléphone</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-phone'></i></span>
                                <input type="text" class="form-control @error('telephone') is-invalid @enderror" id="telephone" name="telephone" value="{{ old('telephone') }}" placeholder="ex: +237 6XX XX XX XX">
                            </div>
                            @error('telephone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="adresse" class="form-label">Adresse</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-map'></i></span>
                                <textarea class="form-control @error('adresse') is-invalid @enderror" id="adresse" name="adresse" rows="3" placeholder="Adresse complète">{{ old('adresse') }}</textarea>
                            </div>
                            @error('adresse')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <!-- Boutons d'action -->
                <div class="d-flex justify-content-between mt-4 pt-3 border-top">
                    <a href="{{ route('employees.index') }}" class="btn btn-light">
                        <i class='bx bx-arrow-back'></i> Retour à la liste
                    </a>
                    <div>
                        <button type="reset" class="btn btn-outline-secondary me-2">
                            <i class='bx bx-reset'></i> Réinitialiser
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class='bx bx-save'></i> Enregistrer
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialiser Select2 sur tous les éléments select avec la classe select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: 'Sélectionner une option',
            allowClear: true
        });
        
        // Effet de focus amélioré sur les champs
        $('.form-control, .form-select').on('focus', function() {
            $(this).closest('.mb-3').addClass('highlight');
        }).on('blur', function() {
            $(this).closest('.mb-3').removeClass('highlight');
        });
        
        // Animation des icônes au survol
        $('.input-group-text i').parent().hover(
            function() { $(this).find('i').addClass('animate__animated animate__heartBeat'); },
            function() { $(this).find('i').removeClass('animate__animated animate__heartBeat'); }
        );
        
        // Débogage du formulaire
        $('#employeeForm').on('submit', function(e) {
            // Afficher les données du formulaire dans la console
            console.log('Soumission du formulaire avec les données suivantes:');
            var formData = {};
            $(this).serializeArray().forEach(function(field) {
                formData[field.name] = field.value;
            });
            console.log(formData);
            
            // Vérifier si tous les champs requis sont remplis
            var isValid = true;
            $(this).find('[required]').each(function() {
                if (!$(this).val()) {
                    console.error('Champ requis non rempli:', $(this).attr('name'));
                    isValid = false;
                }
            });
            
            if (!isValid) {
                console.error('Formulaire invalide: certains champs requis ne sont pas remplis');
            }
        });
    });
</script>

<!-- Script de débogage supplémentaire -->
<script>
    // Afficher les erreurs PHP dans la console
    @if(session('error'))
        console.error('Erreur PHP:', "{{ session('error') }}");
    @endif
    
    // Afficher les erreurs de validation dans la console
    @if($errors->any())
        console.error('Erreurs de validation:');
        @foreach($errors->all() as $error)
            console.error("- {{ $error }}");
        @endforeach
    @endif
    
    // Fonction pour tester la connexion à la base de données
    function testDatabaseConnection() {
        fetch('/api/test-db-connection')
            .then(response => response.json())
            .then(data => {
                console.log('Test de connexion à la base de données:', data);
            })
            .catch(error => {
                console.error('Erreur lors du test de connexion à la base de données:', error);
            });
    }
    
    // Fonction pour inspecter la structure de la table employee
    function inspectEmployeeTable() {
        fetch('/api/inspect-employee-table')
            .then(response => response.json())
            .then(data => {
                console.log('Structure de la table employee:', data);
            })
            .catch(error => {
                console.error('Erreur lors de l\'inspection de la table employee:', error);
            });
    }
</script>

@endsection

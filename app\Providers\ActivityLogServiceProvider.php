<?php

namespace App\Providers;

use App\Services\ActivityLogService;
use Illuminate\Support\ServiceProvider;

class ActivityLogServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ActivityLogService::class, function ($app) {
            return new ActivityLogService('comptabilite-matiere');
        });

        // Alias pour faciliter l'accès au service
        $this->app->alias(ActivityLogService::class, 'activity.log');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}

@extends('admin.admin_dashboard')
@section('admin')

@section('styles')
    <link rel="stylesheet" href="{{ asset('css/vehicule/form.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
    /* Style spécifique pour la marge inférieure des sections */
    #informations-generales .form-card-body,
    #caracteristiques-techniques .form-card-body,
    .form-card-body {
        padding-bottom: 150px !important;
    }
    
    /* Style spécifique pour la section Utilisation */
    .form-card:nth-child(3) .form-card-body {
        padding-bottom: 200px !important;
        margin-bottom: 50px;
    }
    
    /* Correction pour Bootstrap */
    .form-select:not([multiple]):not([size]) {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
        background-position: right 0.75rem center !important;
        background-repeat: no-repeat !important;
        background-size: 16px 12px !important;
    }

    .form-label {
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
    }

    /* Style uniforme pour tous les champs de formulaire */
    .form-control, 
    .form-select,
    .choices__inner,
    .bootstrap-select > .dropdown-toggle,
    .selectpicker {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        color: #000000;
        background-color: #fff;
        height: 46px; /* Hauteur fixe pour tous les champs */
        font-size: 0.95rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        display: flex;
        align-items: center;
    }
    
    /* Styles pour Select2 */
    .select2-container--default .select2-selection--single {
        height: 46px;
        padding: 0.5rem 1rem;
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 28px;
        color: #000000;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 44px;
    }
    
    .select2-dropdown {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid var(--primary);
        border-radius: 0.25rem;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        box-shadow: 0 0 5px rgba(67, 97, 238, 0.2);
    }
    
    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: var(--primary);
        color: white;
    }
    
    .select2-container--default .select2-results__option {
        padding: 8px 12px;
    }
    
    .select2-container--open .select2-dropdown--below {
        margin-top: 5px;
    }
    
    /* Correction pour l'input-group avec Select2 */
    .input-group .select2-container {
        flex: 1 1 auto;
        width: 1% !important;
    }
    
    .input-group .select2-container .select2-selection--single {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Style spécifique pour le conteneur Select2 dans la section Utilisation */
    .form-card:nth-child(3) .select2-container--open .select2-dropdown--below {
        margin-top: 5px;
        z-index: 9999;
    }
    
    /* Styles pour les boutons Bootstrap-select */
    .bootstrap-select .btn {
        border: 1px solid var(--gray-300);
        background-color: #fff;
        color: #000000;
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        font-weight: 400;
        line-height: 1.5;
        text-align: left;
        height: 46px;
    }
    
    .bootstrap-select .dropdown-toggle::after {
        margin-left: auto;
    }
    
    .bootstrap-select .dropdown-menu {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 0.5rem 0;
        margin: 0.125rem 0 0;
    }
    
    .bootstrap-select > .dropdown-toggle:focus,
    .bootstrap-select .dropdown-toggle:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        outline: 0;
    }

    /* Style pour les input-group */
    .input-group-text {
        background-color: var(--primary-light);
        border-color: var(--gray-300);
        color: var(--primary);
        padding: 0 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem 0 0 0.375rem;
        min-width: 45px;
        font-size: 1.1rem;
        height: 46px;
    }
    
    /* Titles */
    .form-card-title {
        margin: 0;
        color: var(--gray-700);
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .form-card-body {
        padding: 1.5rem;
        position: relative;
    }
    
    /* Style pour les éléments de choix (dropdown) */
    .choices__list--dropdown .choices__item {
        padding: 0.5rem;
        margin-top: 0.25rem;
    }
    
    .bootstrap-select .dropdown-item,
    .choices__item--choice {
        padding: 0.5rem 1rem;
        font-size: 0.95rem;
        color: var(--gray-700);
        transition: var(--transition);
    }
    
    .bootstrap-select .dropdown-item:hover,
    .bootstrap-select .dropdown-item:focus,
    .choices__item--choice.is-highlighted {
        background-color: var(--primary-light);
        color: var(--primary-dark);
    }
    
    /* Style pour les champs de formulaire focus */
    .form-control:focus, 
    .form-select:focus,
    .choices__inner:focus,
    .bootstrap-select > .dropdown-toggle.bs-placeholder:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        outline: 0;
    }
    
    /* Bouton transparent style "glass" */
    .btn-glass {
        background-color: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: var(--primary);
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        transition: var(--transition);
        font-weight: 500;
    }

    .btn-glass:hover {
        background-color: rgba(255, 255, 255, 0.9);
        border-color: var(--primary-light);
        color: var(--primary-dark);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    /* Style pour la section actuelle */
    .nav-link {
        color: var(--gray-600);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        transition: var(--transition);
        margin-bottom: 0.5rem;
    }

    .nav-link i {
        margin-right: 0.5rem;
        font-size: 1.2rem;
    }

    .nav-link.active {
        background-color: var(--primary-light);
        color: var(--primary);
        box-shadow: 0 2px 5px rgba(67, 97, 238, 0.15);
    }

    .nav-link:hover:not(.active) {
        background-color: var(--gray-100);
        color: var(--gray-800);
    }

    /* Animation pour les cards */
    .form-card {
        animation: fadeInUp 0.6s both;
        margin-bottom: 2rem;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--gray-200);
    }

    /* Styles d'en-tête */
    .page-header {
        padding: 1.5rem 2rem;
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
        animation: fadeInDown 0.5s both;
        border: 1px solid var(--gray-200);
    }

    .page-title {
        color: var(--primary-dark);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .page-subtitle {
        color: var(--gray-600);
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .breadcrumb {
        margin-bottom: 0;
        padding: 0;
        background: transparent;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-content {
            padding: 1rem;
        }

        .page-header {
            padding: 1rem;
        }

        .form-card-body {
            padding: 1rem;
        }
    }
    </style>
@endsection

<div class="page-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-header animate__animated animate__fadeInDown">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="page-title" style="color: white;"><i class='bx bxs-car me-2'></i> Modifier un Véhicule</h1>
                            <p class="page-subtitle" style="color: white;">Modifiez les informations du véhicule dans le formulaire ci-dessous</p>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class='bx bx-home-alt me-1'></i>Accueil</a></li>
                                    <li class="breadcrumb-item"><a href="{{ route('pageListeVehicule') }}">Véhicules</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">Modifier</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="card form-card animate__animated animate__fadeInUp">
                            <form action="{{ route('modifier.engin') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="id" value="{{ $vehicules->id }}">

                                {{-- Bloc pour afficher les erreurs de validation --}}
                                @if ($errors->any())
                                    <div class="alert alert-danger animate__animated animate__shakeX">
                                        <h6 class="alert-heading">Erreur de validation</h6>
                                        <ul class="mb-0">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <!-- Informations générales -->
                                    <div id="informations-generales" class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                                        <div class="form-card-header">
                                            <h5 class="form-card-title"><i class="bx bx-info-circle me-2"></i>Informations Générales</h5>
                                        </div>
                                        <div class="form-card-body">
                                            <div class="row g-3">
                                                <!-- Direction/Structure -->
                                                <div class="col-12">
                                                    <label for="departement_id" class="form-label">Direction / Structure</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-buildings"></i></span>
                                                        <select id="departement_id" name="departement_id" class="form-select" required>
                                                            <option value="">Choisir la Direction / Structure...</option>
                                                            @foreach($departements as $departement)
                                                                <option value="{{ $departement->id }}" {{ $vehicules->departement_id == $departement->id ? 'selected' : '' }}>{{ $departement->nom_departement }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Immatriculation -->
                                                <div class="col-md-4">
                                                    <label for="immatriculation" class="form-label">Immatriculation</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-id-card"></i></span>
                                                        <input type="text" class="form-control" id="immatriculation" name="immatriculation" value="{{ $vehicules->immatriculation }}" placeholder="Ex: AB-1234-CD" required>
                                                    </div>
                                                </div>

                                                <!-- Genre -->
                                                <div class="col-md-4">
                                                    <label for="genre" class="form-label">Genre</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-category"></i></span>
                                                        <select id="genre" name="genre" class="form-select" required>
                                                            <option value="">Choisir le Genre...</option>
                                                            <option value="Voiture" {{ $vehicules->genre == 'Voiture' ? 'selected' : '' }}>Voiture</option>
                                                            <option value="Camion" {{ $vehicules->genre == 'Camion' ? 'selected' : '' }}>Camion</option>
                                                            <option value="Moto" {{ $vehicules->genre == 'Moto' ? 'selected' : '' }}>Moto</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Marque -->
                                                <div class="col-md-4">
                                                    <label for="marque" class="form-label">Marque</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bxs-car"></i></span>
                                                        <select id="marque" name="marque" class="form-select" required>
                                                            <option value="">Choisir la Marque...</option>
                                                            <option value="Toyota" {{ $vehicules->marque == 'Toyota' ? 'selected' : '' }}>Toyota</option>
                                                            <option value="Ford" {{ $vehicules->marque == 'Ford' ? 'selected' : '' }}>Ford</option>
                                                            <option value="Renault" {{ $vehicules->marque == 'Renault' ? 'selected' : '' }}>Renault</option>
                                                            <option value="Peugeot" {{ $vehicules->marque == 'Peugeot' ? 'selected' : '' }}>Peugeot</option>
                                                            <option value="Mercedes" {{ $vehicules->marque == 'Mercedes' ? 'selected' : '' }}>Mercedes</option>
                                                            <option value="BMW" {{ $vehicules->marque == 'BMW' ? 'selected' : '' }}>BMW</option>
                                                            <option value="Audi" {{ $vehicules->marque == 'Audi' ? 'selected' : '' }}>Audi</option>
                                                            <option value="Man" {{ $vehicules->marque == 'Man' ? 'selected' : '' }}>Man</option>
                                                            <option value="Scania" {{ $vehicules->marque == 'Scania' ? 'selected' : '' }}>Scania</option>
                                                            <option value="Yamaha" {{ $vehicules->marque == 'Yamaha' ? 'selected' : '' }}>Yamaha</option>
                                                            <option value="Honda" {{ $vehicules->marque == 'Honda' ? 'selected' : '' }}>Honda</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Type -->
                                                <div class="col-md-4">
                                                    <label for="type" class="form-label">Type</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-car"></i></span>
                                                        <select id="type" name="type" class="form-select" required>
                                                            <option value="">Choisir le Type...</option>
                                                            <optgroup label="Voitures">
                                                                <option value="Berline" {{ $vehicules->type == 'Berline' ? 'selected' : '' }}>Berline</option>
                                                                <option value="SUV" {{ $vehicules->type == 'SUV' ? 'selected' : '' }}>SUV</option>
                                                                <option value="4x4" {{ $vehicules->type == '4x4' ? 'selected' : '' }}>4x4</option>
                                                                <option value="Break" {{ $vehicules->type == 'Break' ? 'selected' : '' }}>Break</option>
                                                                <option value="Coupé" {{ $vehicules->type == 'Coupé' ? 'selected' : '' }}>Coupé</option>
                                                                <option value="Cabriolet" {{ $vehicules->type == 'Cabriolet' ? 'selected' : '' }}>Cabriolet</option>
                                                                <option value="Monospace" {{ $vehicules->type == 'Monospace' ? 'selected' : '' }}>Monospace</option>
                                                                <option value="Citadine" {{ $vehicules->type == 'Citadine' ? 'selected' : '' }}>Citadine</option>
                                                                <option value="Pickup" {{ $vehicules->type == 'Pickup' ? 'selected' : '' }}>Pickup</option>
                                                            </optgroup>
                                                            <optgroup label="Camions">
                                                                <option value="Benne" {{ $vehicules->type == 'Benne' ? 'selected' : '' }}>Benne</option>
                                                                <option value="Plateau" {{ $vehicules->type == 'Plateau' ? 'selected' : '' }}>Plateau</option>
                                                                <option value="Citerne" {{ $vehicules->type == 'Citerne' ? 'selected' : '' }}>Citerne</option>
                                                                <option value="Frigorifique" {{ $vehicules->type == 'Frigorifique' ? 'selected' : '' }}>Frigorifique</option>
                                                                <option value="Porte-conteneur" {{ $vehicules->type == 'Porte-conteneur' ? 'selected' : '' }}>Porte-conteneur</option>
                                                            </optgroup>
                                                            <optgroup label="Motos">
                                                                <option value="Sportive" {{ $vehicules->type == 'Sportive' ? 'selected' : '' }}>Sportive</option>
                                                                <option value="Roadster" {{ $vehicules->type == 'Roadster' ? 'selected' : '' }}>Roadster</option>
                                                                <option value="Custom" {{ $vehicules->type == 'Custom' ? 'selected' : '' }}>Custom</option>
                                                                <option value="Trail" {{ $vehicules->type == 'Trail' ? 'selected' : '' }}>Trail</option>
                                                            </optgroup>
                                                            <optgroup label="Autres">
                                                                <option value="Tracteur" {{ $vehicules->type == 'Tracteur' ? 'selected' : '' }}>Tracteur</option>
                                                                <option value="Remorque" {{ $vehicules->type == 'Remorque' ? 'selected' : '' }}>Remorque</option>
                                                                <option value="Engin de chantier" {{ $vehicules->type == 'Engin de chantier' ? 'selected' : '' }}>Engin de chantier</option>
                                                            </optgroup>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Valeur d'acquisition -->
                                                <div class="col-md-4">
                                                    <label for="valeur_acquisition" class="form-label">Valeur d'Acquisition</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-money"></i></span>
                                                        <input type="number" class="form-control" id="valeur_acquisition" name="valeur_acquisition" value="{{ $vehicules->valeur_acquisition }}" placeholder="Montant en FCFA"  required>
                                                        <span class="input-group-text">FCFA</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Caractéristiques techniques -->
                                    <div id="caracteristiques-techniques" class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                                        <div class="form-card-header">
                                            <h5 class="form-card-title"><i class="bx bx-cog me-2"></i>Caractéristiques Techniques</h5>
                                        </div>
                                        <div class="form-card-body">
                                            <div class="row g-3">
                                                <!-- État -->
                                                <div class="col-md-4">
                                                    <label for="etat" class="form-label">État</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-health"></i></span>
                                                        <select name="etat" id="etat" class="form-select" required>
                                                            <option value="">Choisir l'état...</option>
                                                            <option value="Bon" {{ $vehicules->etat == 'Bon' ? 'selected' : '' }}>Bon</option>
                                                            <option value="Passable" {{ $vehicules->etat == 'Passable' ? 'selected' : '' }}>Passable</option>
                                                            <option value="En Panne" {{ $vehicules->etat == 'En Panne' ? 'selected' : '' }}>En Panne</option>
                                                            <option value="Hors Service" {{ $vehicules->etat == 'Hors Service' ? 'selected' : '' }}>Hors Service</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- PTC -->
                                                <div class="col-md-4">
                                                    <label for="ptc" class="form-label">Poids Total en Charge</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-weight"></i></span>
                                                        <input type="text" class="form-control" id="ptc" name="ptc" value="{{ $vehicules->ptc }}" placeholder="Ex: 3500 kg" >
                                                    </div>
                                                </div>

                                                <!-- Puissance -->
                                                <div class="col-md-4">
                                                    <label for="puissance" class="form-label">Puissance</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-power-off"></i></span>
                                                        <input type="text" class="form-control" id="puissance" name="puissance" value="{{ $vehicules->puissance }}" placeholder="Ex: 110 CV" >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Utilisation -->
                                    <div class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                                        <div class="form-card-header">
                                            <h5 class="form-card-title"><i class="bx bx-user me-2"></i>Utilisation</h5>
                                        </div>
                                        <div class="form-card-body">
                                            <div class="row g-3">
                                                 <!-- Usage -->
                                                <div class="col-md-4">
                                                    <label for="usage" class="form-label">Usage</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-info-circle"></i></span>
                                                        <select name="usage" id="usage" class="form-select" required>
                                                            <option value="">Choisir l'Usage...</option>
                                                            <option value="Privé" {{ $vehicules->usage == 'Privé' ? 'selected' : '' }}>Privé</option>
                                                            <option value="Taxi" {{ $vehicules->usage == 'Taxi' ? 'selected' : '' }}>Taxi</option>
                                                            <option value="Transport de personnel" {{ $vehicules->usage == 'Transport de personnel' ? 'selected' : '' }}>Transport de personnel</option>
                                                            <option value="Transport de marchandises" {{ $vehicules->usage == 'Transport de marchandises' ? 'selected' : '' }}>Transport de marchandises</option>
                                                            <option value="Transport de matériaux" {{ $vehicules->usage == 'Transport de matériaux' ? 'selected' : '' }}>Transport de matériaux</option>
                                                            <option value="Transport spécialisé" {{ $vehicules->usage == 'Transport spécialisé' ? 'selected' : '' }}>Transport spécialisé</option>
                                                            <option value="Véhicule de fonction" {{ $vehicules->usage == 'Véhicule de fonction' ? 'selected' : '' }}>Véhicule de fonction</option>
                                                            <option value="Véhicule de service" {{ $vehicules->usage == 'Véhicule de service' ? 'selected' : '' }}>Véhicule de service</option>
                                                            <option value="Véhicule d'intervention" {{ $vehicules->usage == 'Véhicule d\'intervention' ? 'selected' : '' }}>Véhicule d'intervention</option>
                                                            <option value="Autre" {{ $vehicules->usage == 'Autre' ? 'selected' : '' }}>Autre</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <!-- Utilisateur -->
                                                <div class="col-md-4">
                                                    <label for="utilisateur" class="form-label">Utilisateur</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-user"></i></span>
                                                        <select class="form-select" id="utilisateur" name="utilisateur" required>
                                                            <option value="">Sélectionner un utilisateur</option>
                                                            @foreach($employees as $employee)
                                                                <option value="{{ $employee->em_id }}" {{ $vehicules->utilisateur == $employee->em_id ? 'selected' : '' }}>{{ $employee->em_code }} - {{ $employee->first_name }} {{ $employee->last_name }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                                <!-- Service -->
                                                <div class="col-md-4">
                                                    <label for="service_utilisation" class="form-label">Service</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="bx bx-building"></i></span>
                                                        <input type="text" class="form-control" id="service_utilisation" name="service_utilisation" value="{{ $vehicules->service_utilisation }}" placeholder="Service ou bureau d'utilisation">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Photo du véhicule -->
                                    <div class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                                        <div class="form-card-header">
                                            <h5 class="form-card-title"><i class="bx bx-image me-2"></i>Photo du Véhicule</h5>
                                        </div>
                                        <div class="form-card-body">
                                            <div class="row">
                                                <div class="col-md-7">
                                                    <div class="image-preview-container" id="dropzone" onclick="document.getElementById('image').click()">
                                                        <input type="file" class="d-none" id="image" name="photo" accept="image/*" onchange="previewImage(this)">
                                                        <div id="preview-placeholder" class="image-preview-placeholder {{ $vehicules->image ? 'd-none' : '' }}">
                                                            <i class="bx bx-image-add"></i>
                                                            <p>Cliquez ou déposez une image ici</p>
                                                            <small class="text-muted">Formats acceptés: JPG, PNG, GIF (Max: 2MB)</small>
                                                        </div>
                                                        <img id="showImage" class="image-preview {{ $vehicules->image ? '' : 'd-none' }}" src="{{ $vehicules->image ? asset($vehicules->image) : url('upload/no_image.jpg') }}" alt="Photo du véhicule">
                                                    </div>
                                                </div>
                                                <div class="col-md-5">
                                                    <div id="imageDetails" class="image-details d-none">
                                                        <h6 class="mb-3">Détails de l'image</h6>
                                                        <div class="image-details-item">
                                                            <span class="image-details-label">Nom du fichier:</span>
                                                            <span id="fileName" class="image-details-value"></span>
                                                        </div>
                                                        <div class="image-details-item">
                                                            <span class="image-details-label">Taille:</span>
                                                            <span id="fileSize" class="image-details-value"></span>
                                                        </div>
                                                        <div class="image-details-item">
                                                            <span class="image-details-label">Type:</span>
                                                            <span id="fileType" class="image-details-value"></span>
                                                        </div>
                                                        <div class="image-details-item">
                                                            <span class="image-details-label">Dimensions:</span>
                                                            <span id="dimensions" class="image-details-value"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Observation -->
                                    <div class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                                        <div class="form-card-header">
                                            <h5 class="form-card-title"><i class="bx bx-edit-alt me-2"></i>Observation</h5>
                                        </div>
                                        <div class="form-card-body">
                                            <textarea class="form-control" id="observation" name="observation" rows="4" placeholder="Ajoutez une observation sur le véhicule...">{{ $vehicules->observation }}</textarea>
                                        </div>
                                    </div>

                                    <div class="col-12 mt-4 text-center">
                                        <button type="submit" class="btn btn-primary px-5 py-2">
                                            <i class='bx bx-save me-2'></i>Enregistrer les Modifications
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration de base pour Choices.js
    const configChoices = {
        removeItemButton: true,
        searchEnabled: true,
        itemSelectText: 'Cliquer pour sélectionner',
        shouldSort: false
    };

    // Initialiser les selects avec Choices.js
    const genreSelectEl = document.getElementById('genre');
    const typeSelectEl = document.getElementById('type');
    const genreChoice = new Choices(genreSelectEl, configChoices);
    const typeChoice = new Choices(typeSelectEl, configChoices);

    new Choices(document.getElementById('departement_id'), configChoices);
    new Choices(document.getElementById('marque'), configChoices);
    new Choices(document.getElementById('etat'), configChoices);
    new Choices(document.getElementById('energie'), configChoices);
    new Choices(document.getElementById('usage'), configChoices);

    // Logique de dépendance entre Genre et Type
    const typeOptions = {
        'Voiture': ['Berline', 'Break', 'Citadine', 'Coupé', 'SUV', 'Monospace'],
        'Camion': ['Benne', 'Citerne', 'Plateau', 'Frigorifique'],
        'Moto': ['Sport', 'Routière', 'Custom', 'Trail']
    };

    function updateTypeOptions() {
        const selectedGenre = genreChoice.getValue(true);
        const currentType = "{{ $vehicules->type ?? '' }}";
        
        typeChoice.clearStore();

        let newOptions = [{ value: '', label: 'Choisir le Type...', selected: false, disabled: true }];

        if (selectedGenre && typeOptions[selectedGenre]) {
            typeOptions[selectedGenre].forEach(type => {
                newOptions.push({
                    value: type,
                    label: type,
                    selected: type === currentType
                });
            });
        }

        typeChoice.setChoices(newOptions, 'value', 'label', true);
    }

    // Écouter les changements sur le champ Genre
    genreSelectEl.addEventListener('change', updateTypeOptions);

    // Mettre à jour les options au chargement de la page
    updateTypeOptions();

    // Fonction de prévisualisation d'image
    window.previewImage = function(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            var file = input.files[0];
            
            reader.onload = function(e) {
                $('#showImage').attr('src', e.target.result).removeClass('d-none');
                $('#preview-placeholder').addClass('d-none');
                $('#fileName').text(file.name);
                $('#fileSize').text((file.size / 1024).toFixed(2) + ' KB');
                $('#fileType').text(file.type);
                
                var img = new Image();
                img.onload = function() {
                    $('#dimensions').text(this.width + ' x ' + this.height + ' px');
                };
                img.src = e.target.result;
                $('#imageDetails').removeClass('d-none');
            }
            
            reader.readAsDataURL(file);
        } 
    }

    // Gestion du Drag & Drop
    const imageInput = document.getElementById('image');
    const dropzone = document.getElementById('dropzone');

    if (dropzone) {
        imageInput.addEventListener('change', () => previewImage(imageInput));

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, (e) => { e.preventDefault(); e.stopPropagation(); }, false);
        });
        ['dragenter', 'dragover'].forEach(eventName => {
            dropzone.addEventListener(eventName, () => dropzone.classList.add('border-primary', 'bg-light'), false);
        });
        ['dragleave', 'drop'].forEach(eventName => {
            dropzone.addEventListener(eventName, () => dropzone.classList.remove('border-primary', 'bg-light'), false);
        });
        dropzone.addEventListener('drop', (e) => {
            imageInput.files = e.dataTransfer.files;
            previewImage(imageInput);
        }, false);
    }
});
</script>
@endsection

/* Styles pour la page liste des réceptions */
:root {
    --primary: #4e73df;
    --primary-light: #eef2ff;
    --primary-dark: #224abe;
    --secondary: #6c757d;
    --success: #1cc88a;
    --danger: #e74a3b;
    --warning: #f6c23e;
    --info: #36b9cc;
    --light: #f8f9fc;
    --dark: #5a5c69;
    --white: #fff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --transition: all 0.3s ease;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Header et navigation */
.reception-header {
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    color: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.reception-header h4 {
    margin: 0;
    font-weight: 600;
}

.reception-header .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.reception-header .breadcrumb-item a {
    color: var(--white);
    opacity: 0.8;
    transition: var(--transition);
}

.reception-header .breadcrumb-item a:hover {
    opacity: 1;
    text-decoration: none;
}

.reception-header .breadcrumb-item.active {
    color: var(--white);
    opacity: 1;
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.btn-reception {
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    border: none;
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-reception:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(34, 74, 190, 0.3);
}

.btn-reception i {
    font-size: 1.2rem;
}

/* Carte principale */
.reception-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.reception-card .card-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
}

.reception-card .card-title {
    margin: 0;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reception-card .card-body {
    padding: 1.5rem;
}

/* Filtres et recherche */
.filters-container {
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.search-container {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-container input {
    padding-left: 2.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    width: 100%;
    transition: var(--transition);
}

.search-container input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    outline: none;
}

.search-container i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-600);
}

.filter-dropdown {
    position: relative;
    min-width: 200px;
}

.filter-dropdown select {
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    transition: var(--transition);
}

.filter-dropdown select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    outline: none;
}

/* Tableau des réceptions */
.reception-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.reception-table th,
.reception-table td {
    padding: 1rem;
    vertical-align: middle;
}

.reception-table thead th {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.05em;
    border: none;
    position: relative;
}

.reception-table thead th:first-child {
    border-top-left-radius: var(--border-radius);
}

.reception-table thead th:last-child {
    border-top-right-radius: var(--border-radius);
}

.reception-table tbody tr {
    transition: var(--transition);
    background-color: var(--white);
}

.reception-table tbody tr:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.reception-table tbody tr td {
    border-top: none;
    border-bottom: 1px solid var(--gray-200);
}

.reception-table tbody tr:last-child td {
    border-bottom: none;
}

.reception-table tbody tr td:first-child {
    border-left: 1px solid var(--gray-200);
}

.reception-table tbody tr td:last-child {
    border-right: 1px solid var(--gray-200);
}

/* Badges et statuts */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.badge-success {
    background-color: rgba(28, 200, 138, 0.1);
    color: var(--success);
}

.badge-warning {
    background-color: rgba(246, 194, 62, 0.1);
    color: var(--warning);
}

.badge-danger {
    background-color: rgba(231, 74, 59, 0.1);
    color: var(--danger);
}

.badge-info {
    background-color: rgba(54, 185, 204, 0.1);
    color: var(--info);
}

/* Boutons d'action dans le tableau */
.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    margin-right: 0.25rem;
    color: var(--white);
    border: none;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.action-btn-view {
    background-color: var(--info);
}

.action-btn-edit {
    background-color: var(--primary);
}

.action-btn-delete {
    background-color: var(--danger);
}

.action-btn-file {
    background-color: var(--success);
}

/* Checkbox personnalisée */
.custom-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
}

.custom-checkbox input {
    opacity: 0;
    width: 0;
    height: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background-color: var(--white);
    border: 2px solid var(--gray-400);
    border-radius: 3px;
    transition: var(--transition);
}

.custom-checkbox input:checked ~ .checkmark {
    background-color: var(--success);
    border-color: var(--success);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.custom-checkbox .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Pagination */
.reception-pagination {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
}

.reception-pagination .page-item .page-link {
    border: none;
    margin: 0 0.25rem;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--gray-700);
    transition: var(--transition);
}

.reception-pagination .page-item.active .page-link {
    background-color: var(--primary);
    color: var(--white);
}

.reception-pagination .page-item .page-link:hover {
    background-color: var(--primary-light);
    color: var(--primary);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Responsive */
@media (max-width: 992px) {
    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-container,
    .filter-dropdown {
        width: 100%;
    }
    
    .reception-table {
        display: block;
        overflow-x: auto;
    }
}

/* Tooltip personnalisé */
.tooltip-reception {
    position: relative;
    display: inline-block;
}

.tooltip-reception .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: var(--dark);
    color: var(--white);
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip-reception .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--dark) transparent transparent transparent;
}

.tooltip-reception:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Modal personnalisé */
.modal-reception .modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.modal-reception .modal-header {
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    color: var(--white);
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-reception .modal-footer {
    border-top: 1px solid var(--gray-200);
}

.modal-reception .btn-close {
    color: var(--white);
    opacity: 0.8;
}

.modal-reception .btn-close:hover {
    opacity: 1;
}

/* Carte de détails */
.detail-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.detail-card:hover {
    border-color: var(--primary);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.detail-card .detail-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.detail-card .detail-value {
    color: var(--gray-900);
}

/* Loader */
.loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loader {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* DataTables personnalisé */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    color: var(--gray-700);
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
    transition: var(--transition);
}

.dataTables_wrapper .dataTables_length select:focus,
.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    outline: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: none !important;
    background: transparent !important;
    margin: 0 0.25rem;
    width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50% !important;
    color: var(--gray-700) !important;
    transition: var(--transition);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--primary) !important;
    color: var(--white) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--primary-light) !important;
    color: var(--primary) !important;
}

/* Statut de disponibilité */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-available {
    background-color: var(--success);
}

.status-unavailable {
    background-color: var(--danger);
}

.status-maintenance {
    background-color: var(--warning);
}

/* Bouton flottant d'ajout */
.floating-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 5px 15px rgba(34, 74, 190, 0.3);
    transition: var(--transition);
    z-index: 999;
}

.floating-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(34, 74, 190, 0.4);
}

/* Effet de survol pour les lignes du tableau */
.reception-table tbody tr {
    position: relative;
}

.reception-table tbody tr::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    transition: width 0.3s ease;
}

.reception-table tbody tr:hover::after {
    width: 100%;
}

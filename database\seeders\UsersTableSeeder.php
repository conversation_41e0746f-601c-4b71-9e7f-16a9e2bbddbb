<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;
use Illuminate\Support\Facades\Hash;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('users')->insert([
            //Admin
            [
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('maedr@moussa'),
                'role' => 'admin',
                'status' => 'active'
            ],
            
            //User
            [
                'name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('maedr@moussa'),
                'role' => 'user',
                'status' => 'active'
            ]

        ]);
    }
}

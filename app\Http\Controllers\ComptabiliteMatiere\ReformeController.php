<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Immobilisation;
use App\Models\Reforme;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ReformeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $reformes = Reforme::with(['immobilisation', 'immobilisation.article', 'user'])
            ->orderBy('date_reforme', 'desc')
            ->get();
        
        return view('comptabilite-matiere.reformes.index', compact('reformes'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Récupérer uniquement les immobilisations qui ne sont pas déjà réformées
        $immobilisations = Immobilisation::whereDoesntHave('reformes')
            ->with('article')
            ->get()
            ->map(function ($immobilisation) {
                return [
                    'id' => $immobilisation->id,
                    'display' => $immobilisation->numero_inventaire . ' - ' . $immobilisation->article->designation
                ];
            })
            ->pluck('display', 'id');
        
        $motifs = [
            'obsolescence' => 'Obsolescence technologique',
            'panne' => 'Panne irréparable',
            'deterioration' => 'Détérioration',
            'autre' => 'Autre'
        ];
        
        return view('comptabilite-matiere.reformes.create', compact('immobilisations', 'motifs'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'immobilisation_id' => 'required|exists:immobilisations,id',
            'date_reforme' => 'required|date',
            'motif_reforme' => 'required|string|in:obsolescence,panne,deterioration,autre',
            'observations' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('reformes.create')
                ->withErrors($validator)
                ->withInput();
        }
        
        // Vérifier que l'immobilisation n'est pas déjà réformée
        $immobilisation = Immobilisation::findOrFail($request->immobilisation_id);
        if ($immobilisation->reformes()->count() > 0) {
            return redirect()->route('reformes.create')
                ->with('error', 'Cette immobilisation est déjà réformée.')
                ->withInput();
        }

        Reforme::create([
            'immobilisation_id' => $request->immobilisation_id,
            'date_reforme' => $request->date_reforme,
            'motif_reforme' => $request->motif_reforme,
            'observations' => $request->observations,
            'user_id' => Auth::id(),
        ]);
        
        // Mettre à jour le statut de l'immobilisation
        $immobilisation->update([
            'statut' => 'reforme'
        ]);

        return redirect()->route('reformes.index')
            ->with('success', 'Réforme enregistrée avec succès.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $reforme = Reforme::with(['immobilisation', 'immobilisation.article', 'immobilisation.serviceAffecte', 'user'])
            ->findOrFail($id);
        
        return view('comptabilite-matiere.reformes.show', compact('reforme'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $reforme = Reforme::findOrFail($id);
        
        $motifs = [
            'obsolescence' => 'Obsolescence technologique',
            'panne' => 'Panne irréparable',
            'deterioration' => 'Détérioration',
            'autre' => 'Autre'
        ];
        
        return view('comptabilite-matiere.reformes.edit', compact('reforme', 'motifs'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $reforme = Reforme::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'date_reforme' => 'required|date',
            'motif_reforme' => 'required|string|in:obsolescence,panne,deterioration,autre',
            'observations' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->route('reformes.edit', $id)
                ->withErrors($validator)
                ->withInput();
        }

        $reforme->update([
            'date_reforme' => $request->date_reforme,
            'motif_reforme' => $request->motif_reforme,
            'observations' => $request->observations,
        ]);

        return redirect()->route('reformes.index')
            ->with('success', 'Réforme mise à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $reforme = Reforme::findOrFail($id);
        $immobilisation = $reforme->immobilisation;
        
        // Remettre l'immobilisation en état actif
        $immobilisation->update([
            'statut' => 'actif'
        ]);
        
        $reforme->delete();

        return redirect()->route('reformes.index')
            ->with('success', 'Réforme supprimée avec succès.');
    }
}

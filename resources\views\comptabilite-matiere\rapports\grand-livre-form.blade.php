<!doctype html>
<html lang="fr">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!--favicon-->
    <link rel="icon" href="{{ asset('backend/assets/images/favicon-32x32.png') }}" type="image/png"/>
    <!--plugins-->
    <link href="{{ asset('backend/assets/plugins/vectormap/jquery-jvectormap-2.0.2.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/simplebar/css/simplebar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/metismenu/css/metisMenu.min.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet"/>
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- loader-->
    <link href="{{ asset('backend/assets/css/pace.min.css') }}" rel="stylesheet"/>
    <script src="{{ asset('backend/assets/js/pace.min.js') }}"></script>
    <!-- Bootstrap CSS -->
    <link href="{{ asset('backend/assets/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/bootstrap-extended.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/icons.css') }}" rel="stylesheet">
    <!-- Theme Style CSS -->
    <link rel="stylesheet" href="{{ asset('backend/assets/css/dark-theme.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/semi-dark.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/header-colors.css') }}"/>
    <!-- Toster CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" >
    <!-- Animate CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <!-- Custom Sidebar CSS -->
    <link rel="stylesheet" href="{{ asset('css/custom-sidebar.css') }}">
    <!-- End Custom Sidebar CSS -->
    <title>Générer le Grand Livre</title>
    <style>
        .form-card {
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            border: none;
            transition: all 0.3s ease;
        }
        .form-card:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .form-card .card-header {
            background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px 25px;
            border-bottom: none;
        }
        .info-card .card-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        }
        .form-card .card-body {
            padding: 30px;
        }
        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #36b9cc;
            box-shadow: 0 0 0 0.2rem rgba(54, 185, 204, 0.25);
        }
        .form-label {
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
        }
        .btn-submit {
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-info {
            background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
            border: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
            border: none;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #858796 0%, #5a5c69 100%);
            border: none;
        }
        .flatpickr-input {
            background-color: white !important;
        }
        .form-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #36b9cc;
            pointer-events: none;
        }
        .form-floating {
            position: relative;
        }
        .form-section {
            background-color: #f8f9fc;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #36b9cc;
        }
        .form-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #36b9cc;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn-back {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border-radius: 50px;
            transition: all 0.3s;
            background-color: #f8f9fc;
            color: #5a5c69;
            border: 1px solid #e2e8f0;
        }
        .btn-back:hover {
            background-color: #eaecf4;
            color: #3a3b45;
            transform: translateY(-2px);
        }
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #3a3b45;
            margin-bottom: 0;
        }
        .invalid-feedback {
            font-size: 0.85rem;
            margin-top: 5px;
        }
        .info-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(78, 115, 223, 0.1);
            color: #4e73df;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        .info-list {
            padding-left: 20px;
        }
        .info-list li {
            margin-bottom: 10px;
            position: relative;
            padding-left: 25px;
        }
        .info-list li:before {
            content: '\f058';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: #36b9cc;
        }
        .info-card {
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            border: none;
            transition: all 0.3s ease;
            margin-top: 30px;
            animation: fadeInUp 0.5s ease-out 0.3s both;
        }
        .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            background-color: #f8f9fc;
            transition: all 0.3s;
        }
        .feature-item:hover {
            background-color: #eaecf4;
            transform: translateY(-3px);
        }
        .feature-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 10px;
            background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
            color: white;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        .feature-content h6 {
            margin-bottom: 5px;
            color: #4a5568;
            font-weight: 600;
        }
        .feature-content p {
            margin-bottom: 0;
            color: #858796;
            font-size: 0.9rem;
        }
    </style>
</head>

<body>
    <!--wrapper-->
    <div class="wrapper">
        <!--sidebar wrapper -->
        @include('admin.body.sidebar')
        <!--end sidebar wrapper -->
        <!--start header -->
        @include('admin.body.header')
        <!--end header -->
        <!--start page wrapper -->
        <div class="page-wrapper">
            <div class="page-content">
                <!--breadcrumb-->
                <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
                    <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
                    <div class="ps-3">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0 p-0">
                                <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a></li>
                                <li class="breadcrumb-item"><a href="{{ route('rapports.index') }}">Rapports</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Grand Livre</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ route('rapports.index') }}" class="btn btn-back">
                            <i class="bx bx-arrow-back"></i> Retour aux rapports
                        </a>
                    </div>
                </div>
                <!--end breadcrumb-->

                <div class="row">
                    <div class="col-12 col-lg-8 mx-auto">
                        <div class="card form-card animate__animated animate__fadeIn">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 text-white"><i class="bx bx-book me-2"></i>Générer le Grand Livre</h5>
                            </div>
                            <div class="card-body">
                                @if ($errors->any())
                                <div class="alert alert-danger border-0 bg-danger alert-dismissible fade show">
                                    <div class="text-white">Une erreur s'est produite lors de la soumission du formulaire.</div>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                                @endif

                                <form action="{{ route('rapports.grand-livre') }}" method="POST" class="mt-3">
                                    @csrf
                                    
                                    <div class="form-section">
                                        <div class="form-section-title">
                                            <i class="bx bx-calendar"></i> Période du rapport
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-4 position-relative">
                                                <label for="date_debut" class="form-label">Date de début</label>
                                                <input type="text" class="form-control datepicker @error('date_debut') is-invalid @enderror" id="date_debut" name="date_debut" value="{{ old('date_debut', date('Y-m-01')) }}" placeholder="Sélectionner une date" required>
                                                <div class="form-icon">
                                                    <i class="bx bx-calendar"></i>
                                                </div>
                                                @error('date_debut')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                            
                                            <div class="col-md-6 mb-4 position-relative">
                                                <label for="date_fin" class="form-label">Date de fin</label>
                                                <input type="text" class="form-control datepicker @error('date_fin') is-invalid @enderror" id="date_fin" name="date_fin" value="{{ old('date_fin', date('Y-m-d')) }}" placeholder="Sélectionner une date" required>
                                                <div class="form-icon">
                                                    <i class="bx bx-calendar"></i>
                                                </div>
                                                @error('date_fin')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between mt-4">
                                        <a href="{{ route('rapports.index') }}" class="btn btn-secondary">
                                            <i class="bx bx-arrow-back"></i> Annuler
                                        </a>
                                        <div>
                                            <button type="submit" class="btn btn-info btn-submit me-2">
                                                <i class="bx bx-search"></i> Générer le rapport
                                            </button>
                                            <button type="submit" name="export_pdf" value="1" class="btn btn-danger btn-submit">
                                                <i class="bx bx-file-pdf"></i> Exporter en PDF
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <div class="card info-card animate__animated animate__fadeInUp">
                            <div class="card-header d-flex align-items-center">
                                <h5 class="mb-0 text-white"><i class="bx bx-info-circle me-2"></i>Informations sur le Grand Livre</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-start mb-4">
                                    <div class="info-icon">
                                        <i class="bx bx-book"></i>
                                    </div>
                                    <div>
                                        <p class="mb-0">Le <strong>Grand Livre</strong> est un document comptable qui présente une synthèse des mouvements et des soldes de stock pour tous les articles sur une période donnée.</p>
                                    </div>
                                </div>
                                
                                <h6 class="text-primary mb-3">Fonctionnalités principales :</h6>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <div class="feature-icon">
                                                <i class="bx bx-bar-chart-alt-2"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h6>Vue d'ensemble</h6>
                                                <p>Synthèse complète de l'état des stocks</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <div class="feature-icon">
                                                <i class="bx bx-transfer-alt"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h6>Mouvements globaux</h6>
                                                <p>Suivi des entrées et sorties par article</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <div class="feature-icon">
                                                <i class="bx bx-check-shield"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h6>Contrôles facilités</h6>
                                                <p>Aide aux inventaires et audits</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <div class="feature-item">
                                            <div class="feature-icon">
                                                <i class="bx bx-analyse"></i>
                                            </div>
                                            <div class="feature-content">
                                                <h6>Analyse de rotation</h6>
                                                <p>Identification des articles à forte/faible rotation</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info d-flex align-items-center mt-3 mb-0">
                                    <i class="bx bx-bulb me-2 fs-5"></i>
                                    <div>
                                        Le grand livre est un outil essentiel pour la gestion des stocks et l'analyse de la consommation des articles.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end page wrapper -->
        <!--start overlay-->
        <div class="overlay toggle-icon"></div>
        <!--end overlay-->
        <!--Start Back To Top Button-->
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
        <!--End Back To Top Button-->
        @include('admin.body.footer')
    </div>
    <!--end wrapper-->

    <!-- Bootstrap JS -->
    <script src="{{ asset('backend/assets/js/bootstrap.bundle.min.js') }}"></script>
    <!--plugins-->
    <script src="{{ asset('backend/assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/simplebar/js/simplebar.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/metismenu/js/metisMenu.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js') }}"></script>
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://npmcdn.com/flatpickr/dist/l10n/fr.js"></script>
    <!--app JS-->
    <script src="{{ asset('backend/assets/js/app.js') }}"></script>
    
    <!-- Toastr JS -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
    @if(Session::has('message'))
    var type = "{{ Session::get('alert-type','info') }}"
    switch(type){
        case 'info':
        toastr.info(" {{ Session::get('message') }} ");
        break;

        case 'success':
        toastr.success(" {{ Session::get('message') }} ");
        break;

        case 'warning':
        toastr.warning(" {{ Session::get('message') }} ");
        break;

        case 'error':
        toastr.error(" {{ Session::get('message') }} ");
        break; 
    }
    @endif 
    </script>
    
    <script>
        $(document).ready(function() {
            // Initialiser Flatpickr pour les sélecteurs de date
            flatpickr(".datepicker", {
                dateFormat: "Y-m-d",
                locale: "fr",
                allowInput: true,
                altInput: true,
                altFormat: "d/m/Y",
                maxDate: "today"
            });
        });
    </script>
</body>
</html>

@extends('admin.admin_dashboard')
@section('admin')

<div class="page-content">
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Comptabilit<PERSON></div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-calculator"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Nomenclature Comptable (NOMATE)</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <h5 class="card-title">Liste des Comptes NOMATE</h5>
                    <p class="text-muted">
                        Affichage de {{ $nomates->firstItem() ?? 0 }} à {{ $nomates->lastItem() ?? 0 }} sur {{ $nomates->total() }} éléments
                        @if(isset($search) && !empty($search))
                            (filtrés par "{{ $search }}")
                        @endif
                    </p>
                </div>
                <div class="col-md-6">
                    <form action="{{ route('nomate.search') }}" method="GET" class="d-flex">
                        <input type="text" name="search" class="form-control me-2" placeholder="Rechercher par code ou intitulé" value="{{ $search ?? '' }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="bx bx-search"></i>
                        </button>
                        @if(isset($search) && !empty($search))
                            <a href="{{ route('nomate.index') }}" class="btn btn-outline-secondary ms-1">
                                <i class="bx bx-x"></i>
                            </a>
                        @endif
                    </form>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th width="10%">Code</th>
                            <th width="70%">Intitulé</th>
                            <th width="10%">Type</th>
                            <th width="10%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($nomates as $key => $item)
                        <tr class="{{ $item->is_header ? 'table-primary fw-bold' : '' }}">
                            <td>{{ $item->code }}</td>
                            <td>{{ $item->intitule }}</td>
                            <td>
                                @if($item->is_header)
                                <span class="badge bg-primary">En-tête</span>
                                @else
                                <span class="badge bg-info">Compte</span>
                                @endif
                            </td>
                            <td>
                                <a href="{{ route('nomate.show', $item->id) }}" class="btn btn-sm btn-info">
                                    <i class="bx bx-detail"></i>
                                </a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                <div class="d-flex justify-content-center">
                    {{ $nomates->withQueryString()->links('pagination::bootstrap-5') }}
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-2">
                    <div>
                        <span class="text-muted">Page {{ $nomates->currentPage() }} sur {{ $nomates->lastPage() }}</span>
                    </div>
                    <div>
                        <form action="{{ isset($search) ? route('nomate.search') : route('nomate.index') }}" method="GET" class="d-flex align-items-center">
                            @if(isset($search))
                                <input type="hidden" name="search" value="{{ $search }}">
                            @endif
                            <label for="per_page" class="me-2">Afficher :</label>
                            <select name="per_page" id="per_page" class="form-select form-select-sm" style="width: 70px" onchange="this.form.submit()">
                                <option value="10" {{ request('per_page') == 10 ? 'selected' : '' }}>10</option>
                                <option value="20" {{ (request('per_page') == 20 || request('per_page') == null) ? 'selected' : '' }}>20</option>
                                <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

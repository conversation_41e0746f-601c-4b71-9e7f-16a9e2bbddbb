<?php

namespace App\Http\Controllers\ComptabiliteMatiere;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Entree;
use App\Models\Sortie;
use App\Models\Immobilisation;
use App\Models\Ajustement;
use App\Models\Inventaire;
use App\Models\Departement;
use App\Services\ActivityLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use PDF;

class RapportController extends Controller
{
    /**
     * Service de journalisation des activités
     *
     * @var ActivityLogService
     */
    protected $activityLog;
    
    /**
     * Service de mise en cache des rapports
     *
     * @var \App\Services\RapportCacheService
     */
    protected $cacheService;

    /**
     * Create a new controller instance.
     *
     * @param ActivityLogService $activityLog
     * @param \App\Services\RapportCacheService $cacheService
     * @return void
     */
    public function __construct(ActivityLogService $activityLog, \App\Services\RapportCacheService $cacheService)
    {
        $this->middleware(['auth', 'comptabilite.matiere:rapports.view']);
        $this->activityLog = $activityLog->setModule('comptabilite-matiere.rapports');
        $this->cacheService = $cacheService;
    }

    /**
     * Affiche la page d'index des rapports disponibles
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Journaliser l'accès à la page d'index des rapports
        $this->activityLog->logAccess('Accès à la page d\'index des rapports');
        
        return view('comptabilite-matiere.rapports.index');
    }

    /**
     * Génère une fiche de stock pour un article spécifique
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function ficheStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'article_id' => 'required|exists:articles,id',
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after_or_equal:date_debut',
        ]);

        if ($validator->fails()) {
            return redirect()->route('rapports.index')
                ->withErrors($validator)
                ->withInput();
        }

        $article = Article::findOrFail($request->article_id);
        $dateDebut = Carbon::parse($request->date_debut)->startOfDay();
        $dateFin = Carbon::parse($request->date_fin)->endOfDay();

        // Récupérer le stock initial (avant la date de début)
        $entreesAvant = Entree::where('article_id', $article->id)
            ->where('date_entree', '<', $dateDebut)
            ->sum('quantite');

        $sortiesAvant = Sortie::where('article_id', $article->id)
            ->where('date_sortie', '<', $dateDebut)
            ->sum('quantite');

        $stockInitial = $entreesAvant - $sortiesAvant;

        // Récupérer les mouvements pendant la période
        $entrees = Entree::with('user')
            ->where('article_id', $article->id)
            ->whereBetween('date_entree', [$dateDebut, $dateFin])
            ->orderBy('date_entree')
            ->get();

        $sorties = Sortie::with(['beneficiaire', 'user'])
            ->where('article_id', $article->id)
            ->whereBetween('date_sortie', [$dateDebut, $dateFin])
            ->orderBy('date_sortie')
            ->get();

        $ajustements = Ajustement::with('user')
            ->where('article_id', $article->id)
            ->whereBetween('date_ajustement', [$dateDebut, $dateFin])
            ->orderBy('date_ajustement')
            ->get();

        // Fusionner et trier tous les mouvements par date
        $mouvements = collect();

        foreach ($entrees as $entree) {
            $mouvements->push([
                'date' => $entree->date_entree,
                'type' => 'Entrée',
                'reference' => $entree->reference_doc,
                'quantite_entree' => $entree->quantite,
                'quantite_sortie' => 0,
                'prix_unitaire' => $entree->prix_unitaire,
                'source_destination' => $entree->source,
                'observations' => $entree->observations,
                'user' => $entree->user->name,
            ]);
        }

        foreach ($sorties as $sortie) {
            $mouvements->push([
                'date' => $sortie->date_sortie,
                'type' => 'Sortie',
                'reference' => 'S-' . $sortie->id,
                'quantite_entree' => 0,
                'quantite_sortie' => $sortie->quantite,
                'prix_unitaire' => null,
                'source_destination' => $sortie->beneficiaire->name,
                'observations' => $sortie->motif,
                'user' => $sortie->user->name,
            ]);
        }

        foreach ($ajustements as $ajustement) {
            $quantiteEntree = 0;
            $quantiteSortie = 0;
            
            if ($ajustement->quantite_apres > $ajustement->quantite_avant) {
                $quantiteEntree = $ajustement->quantite_apres - $ajustement->quantite_avant;
            } else {
                $quantiteSortie = $ajustement->quantite_avant - $ajustement->quantite_apres;
            }
            
            $mouvements->push([
                'date' => $ajustement->date_ajustement,
                'type' => 'Ajustement',
                'reference' => 'A-' . $ajustement->id,
                'quantite_entree' => $quantiteEntree,
                'quantite_sortie' => $quantiteSortie,
                'prix_unitaire' => null,
                'source_destination' => 'Ajustement de stock',
                'observations' => $ajustement->motif,
                'user' => $ajustement->user->name,
            ]);
        }

        // Trier les mouvements par date
        $mouvements = $mouvements->sortBy('date');

        // Calculer le stock final
        $totalEntrees = $entrees->sum('quantite') + $ajustements->sum(function($ajustement) {
            return max(0, $ajustement->quantite_apres - $ajustement->quantite_avant);
        });
        
        $totalSorties = $sorties->sum('quantite') + $ajustements->sum(function($ajustement) {
            return max(0, $ajustement->quantite_avant - $ajustement->quantite_apres);
        });
        
        $stockFinal = $stockInitial + $totalEntrees - $totalSorties;

        // Générer le rapport
        if ($request->has('export_pdf')) {
            $pdf = PDF::loadView('comptabilite-matiere.rapports.fiche-stock-pdf', compact(
                'article', 'dateDebut', 'dateFin', 'stockInitial', 'mouvements', 'stockFinal'
            ));
            
            return $pdf->download('fiche_stock_' . $article->code_article . '.pdf');
        }

        return view('comptabilite-matiere.rapports.fiche-stock', compact(
            'article', 'dateDebut', 'dateFin', 'stockInitial', 'mouvements', 'stockFinal'
        ));
    }

    /**
     * Affiche le formulaire pour générer une fiche de stock
     *
     * @return \Illuminate\Http\Response
     */
    public function ficheStockForm()
    {
        $articles = Article::orderBy('designation')->get();
        return view('comptabilite-matiere.rapports.fiche-stock-form', compact('articles'));
    }

    /**
     * Génère le journal matière pour une période donnée
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function journalMatiere(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after_or_equal:date_debut',
        ]);

        if ($validator->fails()) {
            return redirect()->route('rapports.journal-matiere-form')
                ->withErrors($validator)
                ->withInput();
        }

        $dateDebut = Carbon::parse($request->date_debut)->startOfDay();
        $dateFin = Carbon::parse($request->date_fin)->endOfDay();

        // Récupérer toutes les entrées pour la période
        $entrees = Entree::with(['article', 'article.unite', 'user'])
            ->whereBetween('date_entree', [$dateDebut, $dateFin])
            ->orderBy('date_entree')
            ->get();

        // Récupérer toutes les sorties pour la période
        $sorties = Sortie::with(['article', 'article.unite', 'beneficiaire', 'user'])
            ->whereBetween('date_sortie', [$dateDebut, $dateFin])
            ->orderBy('date_sortie')
            ->get();

        // Récupérer tous les ajustements pour la période
        $ajustements = Ajustement::with(['article', 'article.unite', 'user'])
            ->whereBetween('date_ajustement', [$dateDebut, $dateFin])
            ->orderBy('date_ajustement')
            ->get();

        // Générer le rapport
        if ($request->has('export_pdf')) {
            $pdf = PDF::loadView('comptabilite-matiere.rapports.journal-matiere-pdf', compact(
                'dateDebut', 'dateFin', 'entrees', 'sorties', 'ajustements'
            ));
            
            return $pdf->download('journal_matiere_' . $dateDebut->format('Y-m-d') . '_' . $dateFin->format('Y-m-d') . '.pdf');
        }

        return view('comptabilite-matiere.rapports.journal-matiere', compact(
            'dateDebut', 'dateFin', 'entrees', 'sorties', 'ajustements'
        ));
    }

    /**
     * Affiche le formulaire pour générer le journal matière
     *
     * @return \Illuminate\Http\Response
     */
    public function journalMatiereForm()
    {
        return view('comptabilite-matiere.rapports.journal-matiere-form');
    }

    /**
     * Génère le grand livre pour une période donnée
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function grandLivre(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after_or_equal:date_debut',
        ]);

        if ($validator->fails()) {
            return redirect()->route('rapports.grand-livre-form')
                ->withErrors($validator)
                ->withInput();
        }

        $dateDebut = Carbon::parse($request->date_debut)->startOfDay();
        $dateFin = Carbon::parse($request->date_fin)->endOfDay();

        // Récupérer tous les articles
        $articles = Article::with('unite')->orderBy('designation')->get();

        $grandLivre = [];

        foreach ($articles as $article) {
            // Calculer le stock initial
            $entreesAvant = Entree::where('article_id', $article->id)
                ->where('date_entree', '<', $dateDebut)
                ->sum('quantite');

            $sortiesAvant = Sortie::where('article_id', $article->id)
                ->where('date_sortie', '<', $dateDebut)
                ->sum('quantite');

            $stockInitial = $entreesAvant - $sortiesAvant;

            // Calculer les mouvements pendant la période
            $entreesTotal = Entree::where('article_id', $article->id)
                ->whereBetween('date_entree', [$dateDebut, $dateFin])
                ->sum('quantite');

            $sortiesTotal = Sortie::where('article_id', $article->id)
                ->whereBetween('date_sortie', [$dateDebut, $dateFin])
                ->sum('quantite');

            // Ajouter les ajustements
            $ajustements = Ajustement::where('article_id', $article->id)
                ->whereBetween('date_ajustement', [$dateDebut, $dateFin])
                ->get();

            $ajustementsEntrees = 0;
            $ajustementsSorties = 0;

            foreach ($ajustements as $ajustement) {
                if ($ajustement->quantite_apres > $ajustement->quantite_avant) {
                    $ajustementsEntrees += ($ajustement->quantite_apres - $ajustement->quantite_avant);
                } else {
                    $ajustementsSorties += ($ajustement->quantite_avant - $ajustement->quantite_apres);
                }
            }

            $stockFinal = $stockInitial + $entreesTotal + $ajustementsEntrees - $sortiesTotal - $ajustementsSorties;

            $grandLivre[] = [
                'article' => $article,
                'stock_initial' => $stockInitial,
                'entrees' => $entreesTotal + $ajustementsEntrees,
                'sorties' => $sortiesTotal + $ajustementsSorties,
                'stock_final' => $stockFinal
            ];
        }

        // Générer le rapport
        if ($request->has('export_pdf')) {
            $pdf = PDF::loadView('comptabilite-matiere.rapports.grand-livre-pdf', compact(
                'dateDebut', 'dateFin', 'grandLivre'
            ));
            
            return $pdf->download('grand_livre_' . $dateDebut->format('Y-m-d') . '_' . $dateFin->format('Y-m-d') . '.pdf');
        }

        return view('comptabilite-matiere.rapports.grand-livre', compact(
            'dateDebut', 'dateFin', 'grandLivre'
        ));
    }

    /**
     * Affiche le formulaire pour générer le grand livre
     *
     * @return \Illuminate\Http\Response
     */
    public function grandLivreForm()
    {
        return view('comptabilite-matiere.rapports.grand-livre-form');
    }

    /**
     * Affiche le formulaire de sélection d'inventaire pour le rapport
     * 
     * @return \Illuminate\Http\Response
     */
    public function rapportInventaireForm()
    {
        // Récupérer tous les inventaires clôturés
        $inventaires = Inventaire::with('user')
            ->orderBy('date_fin', 'desc')
            ->get();
            
        return view('comptabilite-matiere.rapports.inventaire-form', compact('inventaires'));
    }
    
    /**
     * Génère un rapport d'inventaire pour un inventaire spécifique
     * 
     * @param int $id ID de l'inventaire
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function rapportInventaire($id, Request $request)
    {
        $inventaire = Inventaire::with(['details', 'details.article', 'details.article.unite', 'user'])
            ->findOrFail($id);

        // Vérifier si l'inventaire est clôturé
        if ($inventaire->statut !== 'cloture') {
            return redirect()->route('inventaires.show', $id)
                ->with('error', 'Le rapport ne peut être généré que pour un inventaire clôturé.');
        }

        // Calculer les statistiques
        $totalArticles = $inventaire->details->count();
        $articlesAvecEcart = $inventaire->details->filter(function ($detail) {
            return $detail->stock_theorique != $detail->stock_reel;
        })->count();

        $ecartPositif = $inventaire->details->filter(function ($detail) {
            return $detail->stock_reel > $detail->stock_theorique;
        })->sum(function ($detail) {
            return $detail->stock_reel - $detail->stock_theorique;
        });

        $ecartNegatif = $inventaire->details->filter(function ($detail) {
            return $detail->stock_reel < $detail->stock_theorique;
        })->sum(function ($detail) {
            return $detail->stock_theorique - $detail->stock_reel;
        });

        // Générer le rapport PDF
        if (request()->has('export_pdf')) {
            $pdf = PDF::loadView('comptabilite-matiere.rapports.inventaire-pdf', compact(
                'inventaire', 'totalArticles', 'articlesAvecEcart', 'ecartPositif', 'ecartNegatif'
            ));
            
            return $pdf->download('rapport_inventaire_' . $inventaire->reference . '.pdf');
        }

        return view('comptabilite-matiere.rapports.inventaire', compact(
            'inventaire', 'totalArticles', 'articlesAvecEcart', 'ecartPositif', 'ecartNegatif'
        ));
    }

    /**
     * Affiche le formulaire pour générer le rapport des immobilisations
     * 
     * @return \Illuminate\Http\Response
     */
    public function rapportImmobilisationsForm()
    {
        // Récupérer la liste des services pour le filtre
        $services = Departement::orderBy('nom_departement')->pluck('nom_departement', 'id');
        
        // Définir les états physiques possibles
        $etatsPhysiques = [
            'bon' => 'Bon',
            'moyen' => 'Moyen',
            'mauvais' => 'Mauvais',
            'hors_service' => 'Hors service'
        ];
        
        // Définir les statuts possibles
        $statuts = [
            'en_service' => 'En service',
            'reforme' => 'Réformé',
            'perdu' => 'Perdu',
            'autre' => 'Autre'
        ];
        
        return view('comptabilite-matiere.rapports.immobilisations-form', compact('services', 'etatsPhysiques', 'statuts'));
    }

    /**
     * Génère le rapport des immobilisations
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function rapportImmobilisations(Request $request)
    {
        // Validation des données
        $validator = Validator::make($request->all(), [
            'service_id' => 'nullable|exists:departements,id',
            'etat_physique' => 'nullable|in:bon,moyen,mauvais',
            'statut' => 'nullable|in:en_service,hors_service,reforme',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        // Paramètres pour la clé de cache
        $cacheParams = [
            'service_id' => $request->service_id,
            'etat_physique' => $request->etat_physique,
            'statut' => $request->statut,
        ];
        
        // Générer une clé de cache unique basée sur les paramètres
        $cacheKey = $this->cacheService->generateKey('immobilisations', $cacheParams);
        
        // Journaliser l'accès au rapport
        $this->activityLog->logAccess('Accès au rapport des immobilisations', $cacheParams);
        
        // Récupérer les données du cache ou exécuter la requête si le cache n'existe pas
        // Durée de mise en cache: 30 minutes
        $immobilisations = $this->cacheService->remember($cacheKey, function() use ($request) {
            // Requête de base pour les immobilisations
            $query = Immobilisation::query();

            // Filtrer par service si spécifié
            if ($request->filled('service_id')) {
                $query->where('departement_id', $request->service_id);
            }

            // Filtrer par état physique si spécifié
            if ($request->filled('etat_physique')) {
                $query->where('etat_physique', $request->etat_physique);
            }

            // Filtrer par statut si spécifié
            if ($request->filled('statut')) {
                $query->where('statut', $request->statut);
            }

            return $query->orderBy('numero_inventaire')->get();
        }, 30); // 30 minutes de cache

        // Générer le rapport PDF
        if ($request->has('export_pdf')) {
            return $this->generatePdfReport(
                'comptabilite-matiere.rapports.immobilisations-pdf', 
                compact('immobilisations'), 
                'rapport_immobilisations.pdf',
                'rapport des immobilisations'
            );
        }

        return view('comptabilite-matiere.rapports.immobilisations', compact('immobilisations'));
    }

    /**
     * Fonction utilitaire pour générer les vues PDF
     * 
     * @param string $view Nom de la vue
     * @param array $data Données pour la vue
     * @param string $filename Nom du fichier PDF
     * @param string $reportType Type de rapport (pour la journalisation)
     * @return \Illuminate\Http\Response
     */
    private function generatePdfReport($view, $data, $filename, $reportType = 'rapport')
    {   
        // Journaliser l'export du rapport PDF
        $this->activityLog->logExport(
            "Export PDF du {$reportType}", 
            [
                'filename' => $filename,
                'user' => Auth::user()->name,
                'date' => Carbon::now()->format('Y-m-d H:i:s'),
                'ip' => request()->ip()
            ]
        );
        
        $pdf = PDF::loadView($view, $data);
        return $pdf->download($filename);
    }
}

@extends('admin.admin_dashboard')

@section('css')
<link rel="stylesheet" href="{{ asset('css/vehicule-liste.css') }}">
@endsection

@section('admin')
<div class="page-content">
    <!-- En-tête de page avec titre et bouton d'ajout -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-2">Gestion du Parc Automobile</h4>
                <p>G<PERSON><PERSON> et suivez tous les véhicules de votre parc automobile en un seul endroit</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Tableau de bord</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Parc Automobile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="{{ route('ajouter.engin') }}" class="btn btn-add-vehicle">
                    <i class="bx bx-plus-circle"></i> Ajouter un véhicule
                </a>
            </div>
        </div>
    </div>
    
    <!-- Cartes statistiques -->
    <div class="dashboard-stats-container mb-4">
        <div class="row g-4">
            <!-- Total Véhicules -->
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-card-header">
                        <h5 class="mb-0">Total Véhicules</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-car"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value" data-count="{{ count($vehicules) }}">0</div>
                        <div class="stat-card-subtitle">Véhicules enregistrés</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 12%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en bon état -->
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-card-header good">
                        <h5 class="mb-0">En bon état</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value" data-count="{{ $vehicules->where('etat', 'Bon')->count() }}">0</div>
                        <div class="stat-card-subtitle">Véhicules opérationnels</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend up">
                                <i class="bx bx-trending-up"></i> 5%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Véhicules en maintenance -->
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-card-header bad">
                        <h5 class="mb-0">Hors service</h5>
                        <div class="stat-card-icon">
                            <i class="bx bx-error-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-body">
                        <div class="stat-card-value" data-count="{{ $vehicules->where('etat', 'Mauvais')->count() }}">0</div>
                        <div class="stat-card-subtitle">Véhicules en maintenance</div>
                        <div class="stat-card-footer">
                            <div class="stat-card-trend down">
                                <i class="bx bx-trending-down"></i> 3%
                            </div>
                            <span>depuis le mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filtres et recherche -->
    <div class="filters-card">
        <div class="filters-card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="search-box">
                        <i class="bx bx-search"></i>
                        <input type="text" id="searchInput" class="form-control" placeholder="Rechercher un véhicule par immatriculation, marque, etc.">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="filter-buttons">
                        <button type="button" class="filter-btn btn btn-primary" data-filter="all">
                            <i class="bx bx-list-ul"></i> Tous
                        </button>
                        <button type="button" class="filter-btn btn btn-outline-primary" data-filter="bon">
                            <i class="bx bx-check-circle"></i> Bon état
                        </button>
                        <button type="button" class="filter-btn btn btn-outline-primary" data-filter="moyen">
                            <i class="bx bx-info-circle"></i> État moyen
                        </button>
                        <button type="button" class="filter-btn btn btn-outline-primary" data-filter="mauvais">
                            <i class="bx bx-error-circle"></i> Hors service
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="advanced-filters collapse" id="advancedFilters">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Département</label>
                        <select class="form-select" id="filterDepartement">
                            <option value="">Tous les départements</option>
                            @foreach($vehicules->pluck('departement.nom_departement')->unique() as $departement)
                                @if($departement)
                                <option value="{{ $departement }}">{{ $departement }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Marque</label>
                        <select class="form-select" id="filterMarque">
                            <option value="">Toutes les marques</option>
                            @foreach($vehicules->pluck('marque')->unique() as $marque)
                                @if($marque)
                                <option value="{{ $marque }}">{{ $marque }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Genre</label>
                        <select class="form-select" id="filterGenre">
                            <option value="">Tous les genres</option>
                            @foreach($vehicules->pluck('genre')->unique() as $genre)
                                @if($genre)
                                <option value="{{ $genre }}">{{ $genre }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Année d'acquisition</label>
                        <select class="form-select" id="filterAnnee">
                            <option value="">Toutes les années</option>
                            @foreach($vehicules->pluck('date_acquisition')->map(function($date) { return $date ? date('Y', strtotime($date)) : null; })->unique()->sort() as $annee)
                                @if($annee)
                                <option value="{{ $annee }}">{{ $annee }}</option>
                                @endif
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <a class="advanced-filters-toggle" data-bs-toggle="collapse" href="#advancedFilters" role="button" aria-expanded="false">
                    <i class="bx bx-chevron-down"></i> Filtres avancés
                </a>
                
                <div class="view-switch float-end">
                    <span class="view-switch-label">Vue :</span>
                    <div class="view-switch-buttons">
                        <button type="button" class="view-switch-btn active" data-view="table">
                            <i class="bx bx-table"></i>
                        </button>
                        <button type="button" class="view-switch-btn" data-view="grid">
                            <i class="bx bx-grid-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Liste des véhicules (Vue tableau) -->
    <div class="vehicle-table-card" id="tableView">
        <div class="table-responsive">
            <table id="vehiculesTable" class="table table-hover align-middle mb-0">
                <thead>
                    <tr>
                        <th width="50">#</th>
                        <th width="80">Photo</th>
                        <th>Immatriculation</th>
                        <th>Marque / Type</th>
                        <th>Département</th>
                        <th>État</th>
                        <th>Utilisateur</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($vehicules as $key => $item)
                    <tr class="vehicle-row" data-etat="{{ strtolower($item->etat) }}" data-departement="{{ $item->departement->nom_departement ?? '' }}" data-marque="{{ $item->marque }}" data-genre="{{ $item->genre }}" data-annee="{{ $item->date_acquisition ? date('Y', strtotime($item->date_acquisition)) : '' }}">
                        <td>{{ $key+1 }}</td>
                        <td>
                            <div class="vehicle-img">
                                <img src="{{ asset($item->image) }}" alt="{{ $item->marque }}" class="img-fluid">
                            </div>
                        </td>
                        <td>
                            <strong>{{ $item->immatriculation }}</strong>
                        </td>
                        <td>
                            <div>{{ $item->marque }}</div>
                            <small class="text-muted">{{ $item->type }}</small>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">{{ $item->departement->nom_departement ?? 'Non assigné' }}</span>
                        </td>
                        <td>
                            @if($item->etat == 'Bon')
                                <span class="vehicle-status good"><i class="bx bx-check-circle"></i> Bon état</span>
                            @elseif($item->etat == 'Moyen')
                                <span class="vehicle-status fair"><i class="bx bx-info-circle"></i> État moyen</span>
                            @else
                                <span class="vehicle-status bad"><i class="bx bx-error-circle"></i> Hors service</span>
                            @endif
                        </td>
                        <td>
                            {{ $item->utilisateur ?? 'Non assigné' }}
                        </td>
                        <td class="text-center">
                            <a href="#" class="btn-action view" data-bs-toggle="modal" data-bs-target="#detailsModal{{ $item->id }}" title="Voir détails">
                                <i class="bx bx-info-circle"></i>
                            </a>
                            <a href="{{ route('editer.engin', $item->id) }}" class="btn-action edit" title="Modifier">
                                <i class="bx bx-edit"></i>
                            </a>
                            <a href="{{ route('supprimer.engin', $item->id) }}" class="btn-action delete" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce véhicule?')">
                                <i class="bx bx-trash"></i>
                            </a>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-4">
            {{ $vehicules->links() }}
        </div>
    </div>
    
    <!-- Vue en grille (masquée par défaut) -->
    <div class="grid-view d-none" id="gridView">
        @foreach($vehicules as $item)
        <div class="vehicle-card" data-etat="{{ strtolower($item->etat) }}" data-departement="{{ $item->departement->nom_departement ?? '' }}" data-marque="{{ $item->marque }}" data-genre="{{ $item->genre }}" data-annee="{{ $item->date_acquisition ? date('Y', strtotime($item->date_acquisition)) : '' }}">
            <div class="vehicle-card-img">
                <img src="{{ asset($item->image) }}" alt="{{ $item->marque }}">
                @if($item->etat == 'Bon')
                    <span class="vehicle-card-status good">Bon état</span>
                @elseif($item->etat == 'Moyen')
                    <span class="vehicle-card-status fair">État moyen</span>
                @else
                    <span class="vehicle-card-status bad">Hors service</span>
                @endif
            </div>
            <div class="vehicle-card-body">
                <h5 class="vehicle-card-title">{{ $item->immatriculation }}</h5>
                <p class="vehicle-card-subtitle">{{ $item->marque }} {{ $item->type }}</p>
                
                <div class="vehicle-card-info">
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Acquisition</span>
                        <span class="vehicle-card-info-value">{{ $item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A' }}</span>
                    </div>
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Valeur</span>
                        <span class="vehicle-card-info-value">{{ number_format($item->valeur_acquisition, 0, ',', ' ') }} F</span>
                    </div>
                    <div class="vehicle-card-info-item">
                        <span class="vehicle-card-info-label">Genre</span>
                        <span class="vehicle-card-info-value">{{ $item->genre }}</span>
                    </div>
                </div>
            </div>
            <div class="vehicle-card-footer">
                <div class="vehicle-card-department">
                    {{ $item->departement->nom_departement ?? 'Non assigné' }}
                </div>
                <div class="vehicle-card-actions">
                    <a href="#" class="btn-action view" data-bs-toggle="modal" data-bs-target="#detailsModal{{ $item->id }}" title="Voir détails">
                        <i class="bx bx-info-circle"></i>
                    </a>
                    <a href="{{ route('editer.engin', $item->id) }}" class="btn-action edit" title="Modifier">
                        <i class="bx bx-edit"></i>
                    </a>
                </div>
            </div>
        </div>
        @endforeach
    </div>
    
    <!-- Modals de détails -->
    @foreach($vehicules as $item)
    <div class="modal fade vehicle-modal" id="detailsModal{{ $item->id }}" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bx bx-info-circle me-2"></i>Détails du véhicule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="vehicle-img-lg mb-3">
                                <img src="{{ asset($item->image) }}" alt="{{ $item->marque }}" class="img-fluid">
                            </div>
                            <div class="info-card">
                                <h6>Informations générales</h6>
                                <div class="info-item">
                                    <span class="info-label">Immatriculation</span>
                                    <span class="info-value">{{ $item->immatriculation }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Marque</span>
                                    <span class="info-value">{{ $item->marque }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Type</span>
                                    <span class="info-value">{{ $item->type }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Genre</span>
                                    <span class="info-value">{{ $item->genre }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">État</span>
                                    <span class="info-value">
                                        @if($item->etat == 'Bon')
                                            <span class="vehicle-status good"><i class="bx bx-check-circle"></i> Bon état</span>
                                        @elseif($item->etat == 'Moyen')
                                            <span class="vehicle-status fair"><i class="bx bx-info-circle"></i> État moyen</span>
                                        @else
                                            <span class="vehicle-status bad"><i class="bx bx-error-circle"></i> Hors service</span>
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="info-card h-100">
                                        <h6>Informations techniques</h6>
                                        <div class="info-item">
                                            <span class="info-label">PTC</span>
                                            <span class="info-value">{{ $item->ptc }}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Puissance</span>
                                            <span class="info-value">{{ $item->puissance }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-card h-100">
                                        <h6>Informations financières</h6>
                                        <div class="info-item">
                                            <span class="info-label">Valeur d'acquisition</span>
                                            <span class="info-value">{{ number_format($item->valeur_acquisition, 0, ',', ' ') }} F</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Date d'acquisition</span>
                                            <span class="info-value">{{ $item->date_acquisition ? date('d/m/Y', strtotime($item->date_acquisition)) : 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="info-card">
                                        <h6>Utilisation</h6>
                                        <div class="info-item">
                                            <span class="info-label">Département</span>
                                            <span class="info-value">{{ $item->departement->nom_departement ?? 'Non assigné' }}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Utilisateur</span>
                                            <span class="info-value">{{ $item->utilisateur ?? 'Non assigné' }}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Service d'utilisation</span>
                                            <span class="info-value">{{ $item->service_utilisation ?? 'Non spécifié' }}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Usage</span>
                                            <span class="info-value">{{ $item->usage }}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Date d'affectation</span>
                                            <span class="info-value">{{ $item->date_affectation ? date('d/m/Y', strtotime($item->date_affectation)) : 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>
                                @if($item->observation)
                                <div class="col-12">
                                    <div class="info-card">
                                        <h6>Observations</h6>
                                        <p>{{ $item->observation }}</p>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bx bx-x me-1"></i>Fermer</button>
                    <a href="{{ route('editer.engin', $item->id) }}" class="btn btn-primary"><i class="bx bx-edit me-1"></i>Modifier</a>
                </div>
            </div>
        </div>
    </div>
    @endforeach
</div>

@section('js')
<script>
    $(document).ready(function() {
        // Animation des compteurs
        function animateCounters() {
            $('.stat-card-value').each(function() {
                var $this = $(this);
                var countTo = parseInt($this.attr('data-count'));
                
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 1000,
                    easing: 'swing',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(countTo);
                    }
                });
            });
        }
        
        // Appel de l'animation des compteurs au chargement
        animateCounters();
        
        // Filtrage des véhicules
        function filterVehicles() {
            var searchValue = $('#searchInput').val().toLowerCase();
            var filterValue = $('.filter-btn.active').data('filter');
            var departementValue = $('#filterDepartement').val();
            var marqueValue = $('#filterMarque').val();
            var genreValue = $('#filterGenre').val();
            var anneeValue = $('#filterAnnee').val();
            
            $('.vehicle-row, .vehicle-card').each(function() {
                var $this = $(this);
                var etat = $this.data('etat');
                var departement = $this.data('departement');
                var marque = $this.data('marque');
                var genre = $this.data('genre');
                var annee = $this.data('annee');
                
                var textContent = $this.text().toLowerCase();
                var matchesSearch = searchValue === '' || textContent.includes(searchValue);
                var matchesFilter = filterValue === 'all' || etat === filterValue;
                var matchesDepartement = departementValue === '' || departement === departementValue;
                var matchesMarque = marqueValue === '' || marque === marqueValue;
                var matchesGenre = genreValue === '' || genre === genreValue;
                var matchesAnnee = anneeValue === '' || annee === anneeValue;
                
                if (matchesSearch && matchesFilter && matchesDepartement && matchesMarque && matchesGenre && matchesAnnee) {
                    $this.removeClass('d-none');
                } else {
                    $this.addClass('d-none');
                }
            });
        }
        
        // Événements pour le filtrage
        $('#searchInput').on('keyup', filterVehicles);
        
        $('.filter-btn').on('click', function() {
            var filterValue = $(this).data('filter');
            
            // Mise à jour des classes actives
            $('.filter-btn').removeClass('btn-primary').addClass('btn-outline-primary');
            $(this).removeClass('btn-outline-primary').addClass('btn-primary');
            
            filterVehicles();
        });
        
        $('#filterDepartement, #filterMarque, #filterGenre, #filterAnnee').on('change', filterVehicles);
        
        // Switch entre vue tableau et vue grille
        $('.view-switch-btn').on('click', function() {
            var viewType = $(this).data('view');
            
            $('.view-switch-btn').removeClass('active');
            $(this).addClass('active');
            
            if (viewType === 'table') {
                $('#tableView').removeClass('d-none');
                $('#gridView').addClass('d-none');
            } else {
                $('#tableView').addClass('d-none');
                $('#gridView').removeClass('d-none');
            }
        });
        
        // Initialisation des tooltips
        $('[title]').tooltip();
    });
</script>
@endsection

@endsection

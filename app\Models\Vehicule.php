<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vehicule extends Model
{
    use HasFactory;
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'date_enregistrement' => 'date:Y-m-d',
    ];

    //Ceci veut dire qu'un Véhicule a au moins un département
    public function departement(){
        return $this->belongsTo(Departement::class);
    }

    // Un véhicule est assigné à un employé
    public function employee(){
        // Spécifie la clé étrangère 'utilisateur' (table vehicules) et la clé propriétaire 'em_id' (table employees)
        return $this->belongsTo(Employee::class, 'utilisateur', 'em_id');
    }
}

<!doctype html>
<html lang="fr">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!--favicon-->
    <link rel="icon" href="{{ asset('backend/assets/images/favicon-32x32.png') }}" type="image/png"/>
    <!--plugins-->
    <link href="{{ asset('backend/assets/plugins/vectormap/jquery-jvectormap-2.0.2.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/simplebar/css/simplebar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/metismenu/css/metisMenu.min.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet"/>
    <!-- loader-->
    <link href="{{ asset('backend/assets/css/pace.min.css') }}" rel="stylesheet"/>
    <script src="{{ asset('backend/assets/js/pace.min.js') }}"></script>
    <!-- Bootstrap CSS -->
    <link href="{{ asset('backend/assets/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/bootstrap-extended.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/icons.css') }}" rel="stylesheet">
    <!-- Theme Style CSS -->
    <link rel="stylesheet" href="{{ asset('backend/assets/css/dark-theme.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/semi-dark.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/header-colors.css') }}"/>
    <!-- Toster CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" >
    <!-- Animate CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <!-- Custom Sidebar CSS -->
    <link rel="stylesheet" href="{{ asset('css/custom-sidebar.css') }}">
    <!-- End Custom Sidebar CSS -->
    <title>Rapports Comptabilité Matière</title>
    <style>
        .card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .card-header {
            border-bottom: none;
            padding: 20px 25px;
        }
        .card-body {
            padding: 25px;
        }
        .report-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: inline-block;
            background: rgba(var(--bs-primary-rgb), 0.1);
            width: 70px;
            height: 70px;
            line-height: 70px;
            text-align: center;
            border-radius: 50%;
        }
        .btn-report {
            border-radius: 50px;
            padding: 8px 20px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }
        .btn-report:hover {
            transform: scale(1.05);
        }
        .report-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .report-description {
            color: #6c757d;
            margin-bottom: 20px;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        .page-title {
            position: relative;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .page-title:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 50px;
            height: 3px;
            background: var(--bs-primary);
        }
        .breadcrumb-item a {
            color: var(--bs-primary);
        }
        .report-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .report-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .report-card .btn-container {
            margin-top: auto;
        }
        .primary-card { border-left: 4px solid var(--bs-primary); }
        .success-card { border-left: 4px solid var(--bs-success); }
        .info-card { border-left: 4px solid var(--bs-info); }
        .warning-card { border-left: 4px solid var(--bs-warning); }
        .danger-card { border-left: 4px solid var(--bs-danger); }
        .secondary-card { border-left: 4px solid var(--bs-secondary); }
    </style>
</head>

<body>
    <!--wrapper-->
    <div class="wrapper">
        <!--sidebar wrapper -->
        @include('admin.body.sidebar')
        <!--end sidebar wrapper -->
        <!--start header -->
        @include('admin.body.header')
        <!--end header -->
        <!--start page wrapper -->
        <div class="page-wrapper">
            <div class="page-content">
                <!--breadcrumb-->
                <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
                    <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
                    <div class="ps-3">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0 p-0">
                                <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">Rapports</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <!--end breadcrumb-->

                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-body">
                                <h4 class="page-title text-primary">Rapports Comptabilité Matière</h4>
                                <p class="text-muted">Sélectionnez un type de rapport à générer parmi les options ci-dessous.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row row-cols-1 row-cols-md-2 row-cols-xl-3 g-4">
                    <!-- Fiche de Stock -->
                    <div class="col">
                        <div class="card shadow-sm report-card primary-card animate__animated animate__fadeIn">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="report-icon text-primary">
                                        <i class="bx bx-package"></i>
                                    </div>
                                </div>
                                <h5 class="report-title text-primary text-center">Fiche de Stock</h5>
                                <p class="report-description text-center">Suivi détaillé des mouvements d'un article sur une période donnée avec calcul des soldes.</p>
                                <div class="btn-container text-center">
                                    <a href="{{ route('rapports.fiche-stock-form') }}" class="btn btn-primary btn-report">
                                        <i class="bx bx-file"></i> Générer le rapport
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Journal Matière -->
                    <div class="col">
                        <div class="card shadow-sm report-card success-card animate__animated animate__fadeIn animate__delay-1s">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="report-icon text-success">
                                        <i class="bx bx-book-open"></i>
                                    </div>
                                </div>
                                <h5 class="report-title text-success text-center">Journal Matière</h5>
                                <p class="report-description text-center">Journal chronologique de tous les mouvements de stock sur une période sélectionnée.</p>
                                <div class="btn-container text-center">
                                    <a href="{{ route('rapports.journal-matiere-form') }}" class="btn btn-success btn-report">
                                        <i class="bx bx-file"></i> Générer le rapport
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Grand Livre -->
                    <div class="col">
                        <div class="card shadow-sm report-card info-card animate__animated animate__fadeIn animate__delay-2s">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="report-icon text-info">
                                        <i class="bx bx-book"></i>
                                    </div>
                                </div>
                                <h5 class="report-title text-info text-center">Grand Livre</h5>
                                <p class="report-description text-center">Synthèse des mouvements et soldes de tous les articles sur une période définie.</p>
                                <div class="btn-container text-center">
                                    <a href="{{ route('rapports.grand-livre-form') }}" class="btn btn-info btn-report">
                                        <i class="bx bx-file"></i> Générer le rapport
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rapport Immobilisations -->
                    <div class="col">
                        <div class="card shadow-sm report-card warning-card animate__animated animate__fadeIn animate__delay-3s">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="report-icon text-warning">
                                        <i class="bx bx-buildings"></i>
                                    </div>
                                </div>
                                <h5 class="report-title text-warning text-center">Immobilisations</h5>
                                <p class="report-description text-center">État des immobilisations avec filtres par service, état et statut d'utilisation.</p>
                                <div class="btn-container text-center">
                                    <a href="{{ route('rapports.immobilisations-form') }}" class="btn btn-warning btn-report">
                                        <i class="bx bx-file"></i> Générer le rapport
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventaires -->
                    <div class="col">
                        <div class="card shadow-sm report-card danger-card animate__animated animate__fadeIn animate__delay-4s">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="report-icon text-danger">
                                        <i class="bx bx-list-check"></i>
                                    </div>
                                </div>
                                <h5 class="report-title text-danger text-center">Inventaires</h5>
                                <p class="report-description text-center">Accès aux rapports d'inventaires clôturés avec analyse des écarts constatés.</p>
                                <div class="btn-container text-center">
                                    <a href="{{ route('rapports.inventaire-form') }}" class="btn btn-danger btn-report">
                                        <i class="bx bx-file"></i> Générer le rapport
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rapport Personnalisé -->
                    <div class="col">
                        <div class="card shadow-sm report-card secondary-card animate__animated animate__fadeIn animate__delay-5s">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="report-icon text-secondary">
                                        <i class="bx bx-customize"></i>
                                    </div>
                                </div>
                                <h5 class="report-title text-secondary text-center">Tableau de Bord</h5>
                                <p class="report-description text-center">Vue d'ensemble des indicateurs clés de la comptabilité matière avec graphiques.</p>
                                <div class="btn-container text-center">
                                    <a href="#" class="btn btn-secondary btn-report">
                                        <i class="bx bx-bar-chart-alt-2"></i> Consulter
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end page wrapper -->
        <!--start overlay-->
        <div class="overlay toggle-icon"></div>
        <!--end overlay-->
        <!--Start Back To Top Button-->
        <a href="javaScript:;" class="back-to-top"><i class='bx bxs-up-arrow-alt'></i></a>
        <!--End Back To Top Button-->
        @include('admin.body.footer')
    </div>
    <!--end wrapper-->

    <!-- Bootstrap JS -->
    <script src="{{ asset('backend/assets/js/bootstrap.bundle.min.js') }}"></script>
    <!--plugins-->
    <script src="{{ asset('backend/assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/simplebar/js/simplebar.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/metismenu/js/metisMenu.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js') }}"></script>
    <!--app JS-->
    <script src="{{ asset('backend/assets/js/app.js') }}"></script>
    <script>
        new PerfectScrollbar(".app-container");
    </script>

    <!-- Toastr JS -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
    @if(Session::has('message'))
    var type = "{{ Session::get('alert-type','info') }}"
    switch(type){
        case 'info':
        toastr.info(" {{ Session::get('message') }} ");
        break;

        case 'success':
        toastr.success(" {{ Session::get('message') }} ");
        break;

        case 'warning':
        toastr.warning(" {{ Session::get('message') }} ");
        break;

        case 'error':
        toastr.error(" {{ Session::get('message') }} ");
        break; 
    }
    @endif 
    </script>
</body>
</html>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Ajoute des index aux tables du module Comptabilité Matière pour améliorer les performances
     */
    public function up(): void
    {
        // Optimisation de la table articles
        if (Schema::hasTable('articles')) {
            $this->addIndexIfNotExists('articles', 'code_article');
            $this->addIndexIfNotExists('articles', 'designation');
            $this->addIndexIfNotExists('articles', 'categorie');
            $this->addIndexIfNotExists('articles', 'unite_id');
        }

        // Optimisation de la table entrees
        if (Schema::hasTable('entrees')) {
            $this->addIndexIfNotExists('entrees', 'date_entree');
            $this->addIndexIfNotExists('entrees', 'article_id');
            $this->addIndexIfNotExists('entrees', 'source');
            $this->addIndexIfNotExists('entrees', 'user_id');
        }

        // Optimisation de la table sorties
        if (Schema::hasTable('sorties')) {
            $this->addIndexIfNotExists('sorties', 'date_sortie');
            $this->addIndexIfNotExists('sorties', 'article_id');
            $this->addIndexIfNotExists('sorties', 'beneficiaire_id');
            $this->addIndexIfNotExists('sorties', 'type_sortie');
            $this->addIndexIfNotExists('sorties', 'user_id');
        }

        // Optimisation de la table immobilisations
        if (Schema::hasTable('immobilisations')) {
            $this->addIndexIfNotExists('immobilisations', 'numero_inventaire');
            $this->addIndexIfNotExists('immobilisations', 'date_acquisition');
            $this->addIndexIfNotExists('immobilisations', 'service_affecte_id');
            $this->addIndexIfNotExists('immobilisations', 'etat_physique');
            $this->addIndexIfNotExists('immobilisations', 'statut');
            $this->addIndexIfNotExists('immobilisations', 'article_id');
        }

        // Optimisation de la table inventaires
        if (Schema::hasTable('inventaires')) {
            $this->addIndexIfNotExists('inventaires', 'date_debut');
            $this->addIndexIfNotExists('inventaires', 'date_fin');
            $this->addIndexIfNotExists('inventaires', 'statut');
            $this->addIndexIfNotExists('inventaires', 'reference');
            $this->addIndexIfNotExists('inventaires', 'user_id');
        }

        // Optimisation de la table inventaire_details
        if (Schema::hasTable('inventaire_details')) {
            // Pour les index composites, nous utilisons une approche différente
            $indexName = 'inventaire_details_inventaire_id_article_id_index';
            $indexExists = DB::select(
                "SHOW INDEX FROM inventaire_details WHERE Key_name = ?",
                [$indexName]
            );
            if (empty($indexExists)) {
                DB::statement("CREATE INDEX {$indexName} ON inventaire_details(inventaire_id, article_id)");
            }
        }

        // Optimisation de la table ajustements
        if (Schema::hasTable('ajustements')) {
            $this->addIndexIfNotExists('ajustements', 'date_ajustement');
            $this->addIndexIfNotExists('ajustements', 'article_id');
            $this->addIndexIfNotExists('ajustements', 'user_id');
        }
    }

    /**
     * Vérifie si un index existe déjà sur une table et une colonne données
     * Si l'index n'existe pas, il est créé
     * 
     * @param string $table Nom de la table
     * @param string $column Nom de la colonne
     * @return void
     */
    protected function addIndexIfNotExists($table, $column)
    {
        // Nom de l'index à vérifier
        $indexName = $table . '_' . $column . '_index';
        
        // Vérifier si l'index existe déjà
        $indexExists = DB::select(
            "SHOW INDEX FROM {$table} WHERE Key_name = ?",
            [$indexName]
        );
        
        // Si l'index n'existe pas, le créer
        if (empty($indexExists)) {
            DB::statement("CREATE INDEX {$indexName} ON {$table}({$column})");
        }
    }
    
    /**
     * Reverse the migrations.
     * Supprime les index ajoutés
     */
    public function down(): void
    {
        // Suppression des index de la table articles
        if (Schema::hasTable('articles')) {
            $this->dropIndexIfExists('articles', 'code_article');
            $this->dropIndexIfExists('articles', 'designation');
            $this->dropIndexIfExists('articles', 'categorie');
            $this->dropIndexIfExists('articles', 'unite_id');
        }

        // Suppression des index de la table entrees
        if (Schema::hasTable('entrees')) {
            $this->dropIndexIfExists('entrees', 'date_entree');
            $this->dropIndexIfExists('entrees', 'article_id');
            $this->dropIndexIfExists('entrees', 'source');
            $this->dropIndexIfExists('entrees', 'user_id');
        }

        // Suppression des index de la table sorties
        if (Schema::hasTable('sorties')) {
            $this->dropIndexIfExists('sorties', 'date_sortie');
            $this->dropIndexIfExists('sorties', 'article_id');
            $this->dropIndexIfExists('sorties', 'beneficiaire_id');
            $this->dropIndexIfExists('sorties', 'type_sortie');
            $this->dropIndexIfExists('sorties', 'user_id');
        }

        // Suppression des index de la table immobilisations
        if (Schema::hasTable('immobilisations')) {
            $this->dropIndexIfExists('immobilisations', 'numero_inventaire');
            $this->dropIndexIfExists('immobilisations', 'date_acquisition');
            $this->dropIndexIfExists('immobilisations', 'service_affecte_id');
            $this->dropIndexIfExists('immobilisations', 'etat_physique');
            $this->dropIndexIfExists('immobilisations', 'statut');
            $this->dropIndexIfExists('immobilisations', 'article_id');
        }

        // Suppression des index de la table inventaires
        if (Schema::hasTable('inventaires')) {
            $this->dropIndexIfExists('inventaires', 'date_debut');
            $this->dropIndexIfExists('inventaires', 'date_fin');
            $this->dropIndexIfExists('inventaires', 'statut');
            $this->dropIndexIfExists('inventaires', 'reference');
            $this->dropIndexIfExists('inventaires', 'user_id');
        }

        // Suppression des index de la table inventaire_details
        if (Schema::hasTable('inventaire_details')) {
            // Pour les index composites
            $indexName = 'inventaire_details_inventaire_id_article_id_index';
            DB::statement("DROP INDEX IF EXISTS {$indexName} ON inventaire_details");
        }

        // Suppression des index de la table ajustements
        if (Schema::hasTable('ajustements')) {
            $this->dropIndexIfExists('ajustements', 'date_ajustement');
            $this->dropIndexIfExists('ajustements', 'article_id');
            $this->dropIndexIfExists('ajustements', 'user_id');
        }
    }
    /**
     * Supprime un index s'il existe sur une table et une colonne données
     * 
     * @param string $table Nom de la table
     * @param string $column Nom de la colonne
     * @return void
     */
    protected function dropIndexIfExists($table, $column)
    {
        // Nom de l'index à supprimer
        $indexName = $table . '_' . $column . '_index';
        
        // Vérifier si l'index existe
        $indexExists = DB::select(
            "SHOW INDEX FROM {$table} WHERE Key_name = ?",
            [$indexName]
        );
        
        // Si l'index existe, le supprimer
        if (!empty($indexExists)) {
            DB::statement("DROP INDEX {$indexName} ON {$table}");
        }
    }
};

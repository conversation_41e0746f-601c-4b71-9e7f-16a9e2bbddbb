##CAHIER DE CHARGES##



1.CONTEXTE ET OBJECTIF

L'objectif est de développer un système informatisé de gestion de la comptabilité des matières conforme aux exigences d'une entité publique (ministère) pour assurer la traçabilité, la conservation, le suivi physique et comptable des biens mobiliers, consommables et immobilisations de l'État.



2.ARCHITECTURE GÉNÉRALE DU SYSTÈME

Modules principaux :
Référentiel (articles, unités, services, utilisateurs)

Gestion des stocks (entrée, sortie, ajustement)

Comptabilité des matières (livres, fiches, inventaires)

Immobilisations et affectations (suivi des biens durables)

Réformes et mutations

Rapports réglementaires

Audit et historisation



3.MODÉLISATION DE LA BASE DE DONNÉES (Extraits de tables clés)

Table : articles

id

code_article (unique)

designation

catégorie (consommable, immobilisation...)

unité_id (clé étrangère)

prix_unitaire_estime

stock_minimum

actif (booléen)

Table : entrees

id

article_id

quantité

prix_unitaire

date_entree

référence_doc (bon, bordereau)

source (fournisseur, direction...)

Table : sorties

id

article_id

quantité

date_sortie

bénéficiaire_id (agent ou service)

type_sortie (affectation, consommation, mutation...)

motif

Table : immobilisations

id

article_id

numero_inventaire

date_acquisition

valeur_d_acquisition

service_affecte_id

état_physique (bon, moyen, HS...)

statut (en service, réformé, perdu)

Table : users

id

nom, prénom, email

rôle (administrateur, magasinier, régisseur, DAF, contrôleur...)

service_id




4.FONCTIONNALITÉS DÉTAILLÉES PAR MODULE

4.1 Référentiel

CRUD des articles avec catégorisation et unité de mesure

Gestion des utilisateurs et rôles (RBAC)

Paramétrage des services, directions, fournisseurs

4.2 Entrées en stock

Enregistrement des bons d’entrée avec validation hiérarchique

Mise à jour automatique des fiches de stock

Historique par article et par source

4.3 Sorties et affectations

Génération de bons de sortie (par bénéficiaire)

Affectation des biens durables (matériels, mobilier)

Signature numérique optionnelle du responsable

4.4 Immobilisations

Suivi de chaque bien immobilisé : affectation, état, mutation

Édition de fiche individuelle d’immobilisation

Registre d’immobilisations téléchargeable

4.5 Réformes / mutations

Procédure de réforme validée par l’ordonnateur

Suivi des mouvements de mutation inter-services

Historique des décisions administratives associées

4.6 Inventaire et ajustement

Génération automatique des états d’inventaire

Comparaison entre stock théorique et physique

Fonction d’ajustement avec journalisation

4.7 Comptabilité des matières

Génération automatique du journal matière

Constitution du grand livre matière par catégorie

Édition de la balance matière par période

4.8 Audit et sécurité

Suivi de toutes les actions (CRUD) avec date, utilisateur

Alertes sur seuils critiques de stock

Archivage automatique des anciens enregistrements





5. ÉTATS ET RAPPORTS OBLIGATOIRES

Fiches de stock (article par article)

Registre des entrées et des sorties

Fiches d’immobilisations

Registre des immobilisations

Registre d’inventaire physique

Journal des matières

Grand livre matière

Balance matière

États de réforme, mutation, affectation



6. EXIGENCES NON FONCTIONNELLES

Interface claire et conviviale (tableaux, filtres, pagination)

Génération de PDF (bons, rapports, fiches)

Système multilingue (français + anglais)

Exports Excel et PDF des états

Responsive design (utilisable sur tablette en magasin)

Journal d’audit permanent (conforme contrôle interne)



    

7. CONTRAINTES & SÉCURITÉ

Protection des données sensibles (SSL, hashing)

Accès profilé par rôle (middleware Laravel)

Suivi et contrôle des droits d’accès par niveau hiérarchique

Aucune suppression définitive en base (soft delete)



8 . ÉVOLUTION FUTURE ENVISAGÉE

Intégration avec SIGFiP ou d’autres systèmes de gestion financière

Génération automatique de rapports pour Cour des Comptes

Suivi des bons de commande/factures liés aux entrées
@extends('admin.admin_dashboard')
@section('admin')
<style>
    /* Styles pour la page d'affichage des documents */
    :root {
        --primary: #4e73df;
        --primary-light: #eef2ff;
        --primary-dark: #224abe;
        --secondary: #6c757d;
        --success: #1cc88a;
        --danger: #e74a3b;
        --warning: #f6c23e;
        --info: #36b9cc;
        --light: #f8f9fc;
        --dark: #5a5c69;
        --white: #fff;
        --transition: all 0.3s ease;
        --border-radius: 0.375rem;
        --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .document-header {
        background: linear-gradient(to right, var(--primary), var(--primary-dark));
        color: var(--white);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--box-shadow);
    }

    .document-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .document-header .breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        margin-top: 0.5rem;
    }

    .document-header .breadcrumb-item a {
        color: var(--white);
        opacity: 0.8;
        transition: var(--transition);
    }

    .document-header .breadcrumb-item a:hover {
        opacity: 1;
        text-decoration: none;
    }

    .document-header .breadcrumb-item.active {
        color: var(--white);
        opacity: 1;
    }

    .document-card {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .document-card .card-header {
        background: var(--white);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding: 1rem 1.5rem;
    }

    .document-card .card-title {
        margin: 0;
        font-weight: 600;
        color: var(--dark);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .document-card .card-body {
        padding: 0;
    }

    .document-info {
        padding: 1.5rem;
        background-color: var(--primary-light);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .document-info-item {
        margin-bottom: 0.75rem;
    }

    .document-info-label {
        font-weight: 600;
        color: var(--primary-dark);
        margin-bottom: 0.25rem;
    }

    .document-info-value {
        color: var(--dark);
    }

    .document-actions {
        padding: 1rem 1.5rem;
        display: flex;
        justify-content: space-between;
        background-color: var(--light);
    }

    .btn-document {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
        font-weight: 500;
    }

    .btn-primary {
        background: linear-gradient(to right, var(--primary), var(--primary-dark));
        border: none;
        color: var(--white);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(34, 74, 190, 0.3);
    }

    .btn-secondary {
        background-color: var(--secondary);
        border: none;
        color: var(--white);
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }

    .document-viewer {
        width: 100%;
        min-height: 600px;
        border: none;
        background-color: var(--light);
    }

    .document-viewer iframe {
        width: 100%;
        height: 700px;
        border: none;
    }

    .document-viewer img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
        box-shadow: var(--box-shadow);
    }

    .document-type-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.35rem 0.65rem;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 1rem;
    }

    .document-type-pdf {
        background-color: rgba(231, 74, 59, 0.1);
        color: var(--danger);
    }

    .document-type-image {
        background-color: rgba(54, 185, 204, 0.1);
        color: var(--info);
    }

    /* Animation d'entrée */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeIn 0.5s ease forwards;
    }
</style>

<div class="page-content">
    <!-- En-tête avec titre et actions -->
    <div class="document-header animate-fade-in">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h4>
                    <i class='bx bx-file me-2'></i>
                    PV de Réception
                    @if($isPdf)
                        <span class="document-type-badge document-type-pdf">
                            <i class='bx bxs-file-pdf'></i> PDF
                        </span>
                    @else
                        <span class="document-type-badge document-type-image">
                            <i class='bx bxs-file-image'></i> Image
                        </span>
                    @endif
                </h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0 p-0 mt-2">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i> Accueil</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('liste_reception') }}">Liste des Réceptions</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Visualisation du PV</li>
                    </ol>
                </nav>
            </div>
            <div class="col-lg-4">
                <div class="text-lg-end mt-3 mt-lg-0">
                    <a href="{{ route('liste_reception') }}" class="btn btn-secondary btn-document">
                        <i class='bx bx-arrow-back'></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- Carte d'informations du document -->
            <div class="document-card animate-fade-in">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class='bx bx-info-circle me-2'></i> Informations du document
                    </h5>
                </div>
                <div class="card-body">
                    <div class="document-info">
                        <div class="row">
                            @if($reception)
                                <div class="col-md-4">
                                    <div class="document-info-item">
                                        <div class="document-info-label">Référence du Marché</div>
                                        <div class="document-info-value">{{ $reception->reference_marche }}</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="document-info-item">
                                        <div class="document-info-label">Référence du Courier</div>
                                        <div class="document-info-value">{{ $reception->reference_courier }}</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="document-info-item">
                                        <div class="document-info-label">Date d'Enregistrement</div>
                                        <div class="document-info-value">{{ \Carbon\Carbon::parse($reception->date_enregistrement)->format('d/m/Y') }}</div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="document-info-item">
                                        <div class="document-info-label">Objet</div>
                                        <div class="document-info-value">{{ $reception->objet }}</div>
                                    </div>
                                </div>
                            @else
                                <div class="col-md-6">
                                    <div class="document-info-item">
                                        <div class="document-info-label">Nom du fichier</div>
                                        <div class="document-info-value">{{ $fileName }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="document-info-item">
                                        <div class="document-info-label">Type de document</div>
                                        <div class="document-info-value">{{ $isPdf ? 'Document PDF' : 'Image' }}</div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="document-actions">
                        <div>
                            <a href="{{ $filePath }}" class="btn btn-primary btn-document" download>
                                <i class='bx bx-download'></i> Télécharger
                            </a>
                            @if($reception)
                                <a href="{{ route('editer_reception', $reception->id) }}" class="btn btn-secondary btn-document ms-2">
                                    <i class='bx bx-edit'></i> Modifier la réception
                                </a>
                            @endif
                        </div>
                        <div>
                            <a href="{{ route('liste_reception') }}" class="btn btn-secondary btn-document">
                                <i class='bx bx-arrow-back'></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visualiseur de document -->
            <div class="document-card animate-fade-in" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class='bx bx-file me-2'></i> Aperçu du document
                    </h5>
                </div>
                <div class="card-body">
                    <div class="document-viewer">
                        @if($isPdf)
                            <iframe src="{{ $filePath }}" title="Aperçu du PDF" allowfullscreen></iframe>
                        @else
                            <div class="text-center p-4">
                                <img src="{{ $filePath }}" alt="Aperçu du document" class="img-fluid">
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

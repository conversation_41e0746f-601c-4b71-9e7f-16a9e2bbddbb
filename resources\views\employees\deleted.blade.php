@extends('admin.admin_dashboard')
@section('admin')

<style>
    :root {
        --primary: #4361ee;
        --primary-light: #4895ef;
        --secondary: #3f37c9;
        --accent: #4cc9f0;
        --success: #4ade80;
        --danger: #f87171;
        --warning: #fbbf24;
        --info: #60a5fa;
        --dark: #374151;
        --light: #f9fafb;
    }

    /* Cards et conteneurs */
    .card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        padding: 1.2rem;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header .header-icon {
        font-size: 1.4rem;
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 1.8rem;
    }

    /* Fil d'ariane */
    .page-breadcrumb {
        background: linear-gradient(to right, #f9fafb, #f3f4f6);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        border-left: 4px solid var(--primary);
    }

    .breadcrumb-item a {
        color: var(--primary);
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .breadcrumb-item a:hover {
        color: var(--secondary);
        text-decoration: none;
    }

    .breadcrumb-item a i {
        margin-right: 0.5rem;
    }

    .breadcrumb-item.active {
        color: var(--dark);
        font-weight: 600;
    }

    /* Tableaux */
    .table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
    }

    .table th {
        font-weight: 600;
        color: var(--dark);
        background-color: #f9fafb;
        padding: 1rem;
        text-align: left;
        border-bottom: 2px solid #e5e7eb;
    }

    .table td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #e5e7eb;
        color: #4b5563;
    }

    .table tbody tr:hover {
        background-color: #f9fafb;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Badges */
    .badge {
        padding: 0.5rem 0.8rem;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.75rem;
    }

    .badge-primary {
        background-color: rgba(67, 97, 238, 0.1);
        color: var(--primary);
    }

    .badge-success {
        background-color: rgba(74, 222, 128, 0.1);
        color: var(--success);
    }

    .badge-danger {
        background-color: rgba(248, 113, 113, 0.1);
        color: var(--danger);
    }

    .badge-warning {
        background-color: rgba(251, 191, 36, 0.1);
        color: var(--warning);
    }

    .badge-info {
        background-color: rgba(96, 165, 250, 0.1);
        color: var(--info);
    }

    /* Boutons d'action */
    .btn-action {
        width: 36px;
        height: 36px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        transition: all 0.3s ease;
        margin-right: 0.5rem;
    }

    .btn-action:last-child {
        margin-right: 0;
    }

    .btn-action i {
        font-size: 1.2rem;
    }

    .btn-info {
        background-color: rgba(96, 165, 250, 0.1);
        color: var(--info);
        border: none;
    }

    .btn-info:hover {
        background-color: var(--info);
        color: white;
        transform: translateY(-2px);
    }

    .btn-success {
        background-color: rgba(74, 222, 128, 0.1);
        color: var(--success);
        border: none;
    }

    .btn-success:hover {
        background-color: var(--success);
        color: white;
        transform: translateY(-2px);
    }

    .btn-danger {
        background-color: rgba(248, 113, 113, 0.1);
        color: var(--danger);
        border: none;
    }

    .btn-danger:hover {
        background-color: var(--danger);
        color: white;
        transform: translateY(-2px);
    }

    /* Animation d'entrée */
    .animate__fadeIn {
        animation-duration: 0.6s;
    }

    .animate__fadeInUp {
        animation-duration: 0.8s;
    }
</style>

<div class="page-content animate__animated animate__fadeIn">
    <!-- Fil d'ariane amélioré -->
    <div class="page-breadcrumb d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3 d-flex align-items-center">
            <i class='bx bx-group me-2 text-primary fs-5'></i>
            <div>
                <h4 class="mb-0">Personnel Désactivé</h4>
                <p class="mb-0 text-secondary small">Liste du personnel désactivé</p>
            </div>
        </div>
        <div class="ms-auto">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class='bx bx-home-alt'></i> Tableau de bord</a></li>
                <li class="breadcrumb-item"><a href="{{ route('employees.index') }}"><i class='bx bx-group'></i> Personnel</a></li>
                <li class="breadcrumb-item active" aria-current="page">Personnel Désactivé</li>
            </ol>
        </div>
    </div>

    <!-- Section d'affichage des messages -->
    @if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class='bx bx-error-circle me-2 fs-4'></i>
            <strong>Erreur!</strong> {{ session('error') }}
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
        <div class="d-flex align-items-center">
            <i class='bx bx-check-circle me-2 fs-4'></i>
            <strong>Succès!</strong> {{ session('success') }}
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <!-- Carte principale avec tableau -->
    <div class="card animate__animated animate__fadeInUp">
        <div class="card-header">
            <div>
                <i class='bx bx-user-x header-icon'></i>
                Liste du Personnel Désactivé
            </div>
            <div>
                <a href="{{ route('employees.index') }}" class="btn btn-light btn-sm">
                    <i class='bx bx-arrow-back'></i> Retour à la liste active
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Tableau des employés désactivés -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Matricule</th>
                            <th>Nom</th>
                            <th>Prénom</th>
                            <th>Poste</th>
                            <th>Direction/Département</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(count($employees) > 0)
                            @foreach($employees as $employee)
                            <tr>
                                <td>{{ $employee->em_code }}</td>
                                <td>{{ $employee->last_name }}</td>
                                <td>{{ $employee->first_name }}</td>
                                <td>{{ $employee->position_held }}</td>
                                <td>
                                    {{ $employee->departement ? $employee->departement->nom_departement : 'Non assigné' }}
                                </td>
                                <td class="text-center">
                                    <a href="/employees/details/{{ $employee->em_code }}" title="Détails" class="btn btn-sm btn-info btn-action">
                                        <i class='bx bx-show'></i>
                                    </a>
                                    <button type="button" title="Réactiver" class="btn btn-sm btn-success btn-action restore-btn" data-id="{{ $employee->em_code }}" data-name="{{ $employee->first_name }} {{ $employee->last_name }}">
                                        <i class='bx bx-refresh'></i>
                                    </button>
                                </td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class='bx bx-info-circle text-secondary mb-2' style="font-size: 2rem;"></i>
                                        <p class="mb-0">Aucun employé désactivé trouvé</p>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion de la réactivation d'un employé
        const restoreBtns = document.querySelectorAll('.restore-btn');
        
        restoreBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const employeeId = this.getAttribute('data-id');
                const employeeName = this.getAttribute('data-name');
                
                Swal.fire({
                    title: 'Réactiver cet employé?',
                    html: `Voulez-vous vraiment réactiver <strong>${employeeName}</strong> ?<br>Cet employé sera à nouveau visible dans la liste principale.`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#4ade80',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Oui, réactiver',
                    cancelButtonText: 'Annuler',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Créer une requête AJAX pour réactiver l'employé
                        const xhr = new XMLHttpRequest();
                        xhr.open('POST', `/employees/restore/${employeeId}`);
                        xhr.setRequestHeader('Content-Type', 'application/json');
                        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                        xhr.responseType = 'json';
                        
                        // Ajouter le paramètre _method pour simuler une requête PUT
                        const data = JSON.stringify({
                            _method: 'PUT'
                        });
                        
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                Swal.fire({
                                    title: 'Réactivé!',
                                    text: `L'employé ${employeeName} a été réactivé avec succès.`,
                                    icon: 'success',
                                    confirmButtonColor: '#4361ee'
                                }).then(() => {
                                    // Recharger la page pour mettre à jour la liste
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Erreur!',
                                    text: xhr.response?.message || 'Une erreur est survenue lors de la réactivation.',
                                    icon: 'error',
                                    confirmButtonColor: '#4361ee'
                                });
                            }
                        };
                        
                        xhr.onerror = function() {
                            Swal.fire({
                                title: 'Erreur!',
                                text: 'Une erreur de connexion est survenue.',
                                icon: 'error',
                                confirmButtonColor: '#4361ee'
                            });
                        };
                        
                        xhr.send(data);
                    }
                });
            });
        });
    });
</script>

@endsection

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('immobilisations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('article_id')->constrained('articles');
            $table->string('numero_inventaire')->unique();
            $table->date('date_acquisition');
            $table->decimal('valeur_acquisition', 15, 2);
            $table->foreignId('service_affecte_id')->constrained('departements');
            $table->enum('etat_physique', ['bon', 'moyen', 'mauvais', 'hors_service']);
            $table->enum('statut', ['en_service', 'reforme', 'perdu', 'autre']);
            $table->text('description')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('immobilisations');
    }
};

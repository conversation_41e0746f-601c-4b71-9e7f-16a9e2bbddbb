/**
 * Code.js - Fonctions communes pour les confirmations de suppression
 * Utilise SweetAlert2 pour les confirmations
 */

$(function() {
    'use strict';

    // Fonction générique pour confirmer la suppression
    window.confirmDelete = function(event) {
        event.preventDefault();
        let form = event.target.closest('form');
        
        if (!form) {
            console.error('Formulaire non trouvé pour la suppression');
            return;
        }

        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: "Cette action est irréversible !",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer !',
            cancelButtonText: 'Annuler',
            reverseButtons: true,
            focusCancel: true
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    };

    // Gestion automatique des boutons avec classe 'delete-btn'
    $(document).on('click', '.delete-btn', function(e) {
        e.preventDefault();
        
        const url = $(this).attr('href');
        const itemName = $(this).data('name') || 'cet élément';
        
        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: `Vous êtes sur le point de supprimer ${itemName}. Cette action est irréversible!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74a3b',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler',
            reverseButtons: true,
            focusCancel: true
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = url;
            }
        });
    });

    // Gestion des formulaires de suppression avec classe 'delete-form'
    $(document).on('submit', '.delete-form', function(e) {
        e.preventDefault();
        
        const form = this;
        const itemName = $(form).data('name') || 'cet élément';
        
        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: `Vous êtes sur le point de supprimer ${itemName}. Cette action est irréversible!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74a3b',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler',
            reverseButtons: true,
            focusCancel: true
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });

    // Messages de succès après suppression
    if (typeof successMessage !== 'undefined' && successMessage) {
        Swal.fire({
            title: 'Supprimé!',
            text: successMessage,
            icon: 'success',
            confirmButtonColor: '#28a745'
        });
    }

    // Messages d'erreur après suppression
    if (typeof errorMessage !== 'undefined' && errorMessage) {
        Swal.fire({
            title: 'Erreur!',
            text: errorMessage,
            icon: 'error',
            confirmButtonColor: '#dc3545'
        });
    }
});

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicules', function (Blueprint $table) {
            $table->integer('departement_id')->nullable()->after('immatriculation');
        });

        // Copier les données de l'ancienne colonne vers la nouvelle
        DB::statement('UPDATE vehicules SET departement_id = direction_structure');

        Schema::table('vehicules', function (Blueprint $table) {
            $table->dropColumn('direction_structure');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicules', function (Blueprint $table) {
            $table->integer('direction_structure')->nullable()->after('immatriculation');
        });

        // Copier les données de la nouvelle colonne vers l'ancienne
        DB::statement('UPDATE vehicules SET direction_structure = departement_id');

        Schema::table('vehicules', function (Blueprint $table) {
            $table->dropColumn('departement_id');
        });
    }
};

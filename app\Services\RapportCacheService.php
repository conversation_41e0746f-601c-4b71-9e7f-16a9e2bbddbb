<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class RapportCacheService
{
    /**
     * Durée de mise en cache par défaut (en minutes)
     * 
     * @var int
     */
    protected $cacheDuration = 60; // 1 heure

    /**
     * Préfixe pour les clés de cache
     * 
     * @var string
     */
    protected $cachePrefix = 'rapport_';

    /**
     * Récupère les données du cache ou exécute la fonction de rappel si le cache n'existe pas
     * 
     * @param string $key Clé de cache
     * @param \Closure $callback Fonction à exécuter si le cache n'existe pas
     * @param int|null $duration Durée de mise en cache en minutes (null = utiliser la valeur par défaut)
     * @return mixed
     */
    public function remember($key, $callback, $duration = null)
    {
        $cacheKey = $this->cachePrefix . $key;
        $cacheDuration = $duration ?? $this->cacheDuration;

        return Cache::remember($cacheKey, $cacheDuration * 60, $callback);
    }

    /**
     * Supprime une entrée du cache
     * 
     * @param string $key Clé de cache
     * @return bool
     */
    public function forget($key)
    {
        return Cache::forget($this->cachePrefix . $key);
    }

    /**
     * Vérifie si une clé existe dans le cache
     * 
     * @param string $key Clé de cache
     * @return bool
     */
    public function has($key)
    {
        return Cache::has($this->cachePrefix . $key);
    }

    /**
     * Génère une clé de cache pour un rapport basée sur les paramètres
     * 
     * @param string $reportType Type de rapport
     * @param array $params Paramètres du rapport
     * @return string
     */
    public function generateKey($reportType, array $params)
    {
        // Trier les paramètres pour assurer la cohérence des clés
        ksort($params);
        
        // Convertir les paramètres en chaîne JSON
        $paramsString = json_encode($params);
        
        // Générer une clé unique basée sur le type de rapport et les paramètres
        return $reportType . '_' . md5($paramsString);
    }

    /**
     * Vide tout le cache des rapports
     * 
     * @return bool
     */
    public function flush()
    {
        // Récupérer toutes les clés de cache commençant par le préfixe
        $keys = Cache::get('cache_keys', []);
        $rapportKeys = array_filter($keys, function($key) {
            return strpos($key, $this->cachePrefix) === 0;
        });

        // Supprimer chaque clé
        foreach ($rapportKeys as $key) {
            Cache::forget($key);
        }

        return true;
    }
}

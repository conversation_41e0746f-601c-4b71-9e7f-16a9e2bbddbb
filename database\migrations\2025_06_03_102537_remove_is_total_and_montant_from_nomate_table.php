<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('nomate', function (Blueprint $table) {
            if (Schema::hasColumn('nomate', 'is_total')) {
                $table->dropColumn('is_total');
            }
            if (Schema::hasColumn('nomate', 'montant')) {
                $table->dropColumn('montant');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('nomate', function (Blueprint $table) {
            if (!Schema::hasColumn('nomate', 'is_total')) {
                $table->boolean('is_total')->default(false)->comment('Indique si c\'est une ligne de total');
            }
            if (!Schema::hasColumn('nomate', 'montant')) {
                $table->decimal('montant', 15, 2)->default(0);
            }
        });
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inventaire extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'reference',
        'date_debut',
        'date_fin',
        'statut',
        'observations',
        'user_id'
    ];
    
    protected $casts = [
        'date_debut' => 'date',
        'date_fin' => 'date',
    ];
    
    public function details()
    {
        return $this->hasMany(InventaireDetail::class);
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function ecartTotal()
    {
        return $this->details()->sum('ecart');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventaireDetail extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'inventaire_id',
        'article_id',
        'stock_theorique',
        'stock_physique',
        'ecart',
        'observations'
    ];
    
    public function inventaire()
    {
        return $this->belongsTo(Inventaire::class);
    }
    
    public function article()
    {
        return $this->belongsTo(Article::class);
    }
    
    public function calculerEcart()
    {
        return $this->stock_physique - $this->stock_theorique;
    }
}

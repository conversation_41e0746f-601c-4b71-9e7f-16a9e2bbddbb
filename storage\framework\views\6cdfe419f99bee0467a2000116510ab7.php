<?php $__env->startSection('admin'); ?>

<?php $__env->startSection('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/vehicule/form.css')); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
    /* Style spécifique pour la marge inférieure des sections */
    #informations-generales .form-card-body,
    #caracteristiques-techniques .form-card-body,
    .form-card-body {
        padding-bottom: 150px !important;
    }
    
    /* Style spécifique pour la section Utilisation */
    .form-card:nth-child(3) .form-card-body {
        padding-bottom: 200px !important;
        margin-bottom: 50px;
    }
    
    /* Correction pour Bootstrap */
    .form-select:not([multiple]):not([size]) {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
        background-position: right 0.75rem center !important;
        background-repeat: no-repeat !important;
        background-size: 16px 12px !important;
    }

    .form-label {
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
    }

    /* Style uniforme pour tous les champs de formulaire */
    .form-control, 
    .form-select,
    .choices__inner,
    .bootstrap-select > .dropdown-toggle,
    .selectpicker {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        color: #000000;
        background-color: #fff;
        height: 46px; /* Hauteur fixe pour tous les champs */
        font-size: 0.95rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        display: flex;
        align-items: center;
    }
    
    /* Styles pour Select2 */
    .select2-container--default .select2-selection--single {
        height: 46px;
        padding: 0.5rem 1rem;
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 28px;
        color: #000000;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 44px;
    }
    
    .select2-dropdown {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid var(--primary);
        border-radius: 0.25rem;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        box-shadow: 0 0 5px rgba(67, 97, 238, 0.2);
    }
    
    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: var(--primary);
        color: white;
    }
    
    .select2-container--default .select2-results__option {
        padding: 8px 12px;
    }
    
    .select2-container--open .select2-dropdown--below {
        margin-top: 5px;
    }
    
    /* Correction pour l'input-group avec Select2 */
    .input-group .select2-container {
        flex: 1 1 auto;
        width: 1% !important;
    }
    
    .input-group .select2-container .select2-selection--single {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Style spécifique pour le conteneur Select2 dans la section Utilisation */
    .form-card:nth-child(3) .select2-container--open .select2-dropdown--below {
        margin-top: 5px;
        z-index: 9999;
    }
    
    /* Assurer que le dropdown reste visible */
    .select2-container--open .select2-dropdown {
        z-index: 9999;
    }
    
    /* Augmenter l'espace pour la section Utilisation */
    .form-card:nth-child(3) {
        margin-bottom: 100px;
    }

    /* Style uniforme au focus */
    .form-control:focus, 
    .form-select:focus,
    .choices__inner:focus,
    .bootstrap-select > .dropdown-toggle:focus,
    .selectpicker:focus,
    .bootstrap-select.show > .dropdown-toggle {
        border-color: var(--primary);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
        outline: none;
        transform: translateY(-2px);
    }

    /* Alignement des groupes d'entrée */
    .input-group {
        display: flex;
        align-items: center;
        width: 100%;
    }
    
    .input-group-text {
        background-color: var(--primary-light);
        border-color: var(--gray-300);
        color: var(--primary);
        padding: 0 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem 0 0 0.375rem;
        min-width: 45px;
        font-size: 1.1rem;
        height: 46px;
    }
    
    /* Correction pour l'alignement des select dans les input-group */
    .input-group .form-select,
    .input-group .form-control,
    .input-group .bootstrap-select,
    .input-group .choices {
        flex: 1 1 auto;
        width: 1%;
    }
    
    .input-group .bootstrap-select > .dropdown-toggle,
    .input-group .choices__inner {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        height: 100%;
    }
    
    /* Assurer que tous les éléments ont la même hauteur */
    .input-group > * {
        height: 46px;
    }
    
    /* Force l'uniformité des menus déroulants */
    .bootstrap-select .dropdown-menu,
    .choices__list--dropdown {
        border: 1px solid var(--gray-300);
        border-radius: 0.375rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 0.5rem;
        margin-top: 0.25rem;
    }
    
    .bootstrap-select .dropdown-item,
    .choices__item--choice {
        padding: 0.75rem 1rem;
        border-radius: 0.25rem;
        transition: var(--transition);
    }
    
    .bootstrap-select .dropdown-item:hover,
    .bootstrap-select .dropdown-item:focus,
    .choices__item--choice:hover {
        background-color: var(--primary-light);
        color: var(--primary);
    }
    
    .bootstrap-select .dropdown-item.active,
    .bootstrap-select .dropdown-item:active,
    .choices__item--choice.is-selected {
        background-color: var(--primary);
        color: white;
    }

    .btn-primary {
        background-color: var(--primary);
        border-color: var(--primary);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    /* Image preview */
    .image-preview-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        transition: var(--transition);
        cursor: pointer;
    }

    .image-preview-container:hover {
        border-color: var(--primary);
    }

    .image-preview-placeholder {
        color: var(--gray-500);
        margin-bottom: 1rem;
    }

    .image-preview-placeholder i {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    .image-preview {
        max-width: 100%;
        max-height: 250px;
        border-radius: 0.375rem;
        margin-top: 1rem;
        box-shadow: var(--box-shadow);
    }

    .image-details {
        background-color: var(--gray-100);
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-top: 1rem;
        font-size: 0.875rem;
    }

    .image-details-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .image-details-item:last-child {
        margin-bottom: 0;
    }

    .image-details-label {
        font-weight: 500;
        color: var(--gray-700);
    }

    .image-details-value {
        color: var(--gray-600);
    }

    /* Styles supplémentaires pour Choices.js */
    .choices {
        margin-bottom: 0;
    }

    .choices__inner {
        min-height: 46px;
    }

    .choices__list--single {
        padding: 4px 16px 4px 4px;
    }

    .choices__list--dropdown .choices__item--selectable.is-highlighted {
        background-color: var(--primary);
        color: white;
    }

    /* Amélioration de l'affichage de la liste déroulante */
    .choices__list--dropdown {
        max-height: 250px;
        overflow-y: auto;
    }

    .choices__item--choice {
        padding: 8px 12px;
        font-size: 14px;
    }

    /* Assurer que tous les input-group ont la même hauteur et sont alignés horizontalement */
    .input-group {
        min-height: 44px;
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
    }

    .input-group .form-select,
    .input-group .form-control {
        min-height: 44px;
        flex: 1 !important;
    }

    .input-group .input-group-text {
        min-height: 44px;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-shrink: 0 !important;
        width: auto !important;
        border-right: 0 !important;
    }

    /* Spécifiquement pour le champ Type */
    #type {
        border-left: 0 !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    
    /* Styles supplémentaires pour Bootstrap-select */
    .bootstrap-select .bs-searchbox .form-control {
        padding: 0.5rem 0.75rem;
        margin-bottom: 0.5rem;
    }
    
    .bootstrap-select .bs-actionsbox, 
    .bootstrap-select .bs-donebutton, 
    .bootstrap-select .bs-searchbox {
        padding: 0.5rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-content {
            padding: 1rem;
        }

        .page-header {
            padding: 1rem;
        }

        .form-card-body {
            padding: 1rem;
        }
    }
    </style>
<?php $__env->stopSection(); ?>



<div class="page-content">
    <!-- En-tête de page -->
    <div class="page-header animate__animated animate__fadeIn">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="page-title">
                    <i class='bx bxs-car-garage me-2'></i>Ajout d'un Véhicule
                </h4>
                <p class="page-subtitle">Enregistrez un nouveau véhicule dans le parc automobile</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><i class="bx bx-home-alt"></i> Accueil</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('pageListeVehicule')); ?>">Parc Automobile</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Nouveau Véhicule</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?php echo e(route('pageListeVehicule')); ?>" class="btn btn-glass">
                    <i class="bx bx-arrow-back me-1"></i> Retour à la liste
                </a>
            </div>
        </div>
    </div>

    <!-- Formulaire -->
    <div class="row">
        <div class="col-lg-12">
            <form id="vehicleForm" action="<?php echo e(route('enregistrer.engin')); ?>" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <?php echo csrf_field(); ?>
                <?php echo method_field('POST'); ?>

                <!-- Informations générales -->
                <div id="informations-generales" class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-info-circle me-2"></i>Informations Générales</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row g-3">
                            <!-- Direction/Structure -->
                            <div class="col-12 full-width-select" style="width: 100% !important; display: block !important;">
                                <label for="departement_id" class="form-label">Direction / Structure</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-buildings"></i></span>
                                    <select id="departement_id" name="direction_structure" class="form-select" style="width: 100% !important; flex-grow: 1 !important;" required>
                                        <option value="">Choisir la Direction / Structure...</option>
                                        <?php $__currentLoopData = $departements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $departement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($departement->id); ?>"><?php echo e($departement->nom_departement); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Immatriculation -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="immatriculation" class="form-label">Immatriculation</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-id-card"></i></span>
                                    <input type="text" class="form-control" id="immatriculation" name="immatriculation" value="<?php echo e(old('immatriculation')); ?>" placeholder="Ex: AB-1234-CD" required>
                                </div>
                            </div>

                            <!-- Genre -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="genre" class="form-label">Genre</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-category"></i></span>
                                    <select name="genre" id="genre" class="form-select" size="6" style="overflow-y: auto;" required>
                                        <option value="">Choisir le Genre...</option>
                                        <option value="Voiture">Voiture</option>
                                        <option value="Moto">Moto</option>
                                        <option value="Tête Porte Chars">Tête Porte Chars</option>
                                        <option value="Porte Chars">Porte Chars</option>
                                        <option value="Tricycle">Tricycle</option>
                                        <option value="Camion">Camion</option>
                                        <option value="Bus">Bus</option>
                                        <option value="Minibus">Minibus</option>
                                        <option value="Fourgon">Fourgon</option>
                                        <option value="Fourgonnette">Fourgonnette</option>
                                        <option value="Semi-remorque">Semi-remorque</option>
                                        <option value="Remorque">Remorque</option>
                                        <option value="Autre">Autre</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Marque -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="marque" class="form-label">Marque</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bxs-car"></i></span>
                                    <select id="marque" name="marque" class="form-select" size="6" style="overflow-y: auto;" required>
                                        <option value="">Choisir la Marque...</option>
                                        <!-- Voitures -->
                                        <optgroup label="Voitures">
                                            <option value="Toyota">Toyota</option>
                                            <option value="Nissan">Nissan</option>
                                            <option value="Hyundai">Hyundai</option>
                                            <option value="Kia">Kia</option>
                                            <option value="Mitsubishi">Mitsubishi</option>
                                            <option value="Suzuki">Suzuki</option>
                                            <option value="Honda">Honda</option>
                                            <option value="Mazda">Mazda</option>
                                            <option value="Ford">Ford</option>
                                            <option value="Chevrolet">Chevrolet</option>
                                            <option value="Volkswagen">Volkswagen</option>
                                            <option value="Mercedes-Benz">Mercedes-Benz</option>
                                            <option value="BMW">BMW</option>
                                            <option value="Audi">Audi</option>
                                            <option value="Peugeot">Peugeot</option>
                                            <option value="Renault">Renault</option>
                                            <option value="Citroën">Citroën</option>
                                            <option value="Fiat">Fiat</option>
                                            <option value="Dacia">Dacia</option>
                                            <option value="Mahindra">Mahindra</option>
                                            <option value="Tata">Tata</option>
                                            <option value="Great Wall">Great Wall</option>
                                            <option value="Chery">Chery</option>
                                            <option value="Geely">Geely</option>
                                            <option value="BYD">BYD</option>
                                            <option value="JAC">JAC</option>
                                            <option value="Haval">Haval</option>
                                            <option value="Foton">Foton</option>
                                            <option value="Isuzu">Isuzu</option>
                                            <option value="Ssangyong">Ssangyong</option>
                                            <option value="Land Rover">Land Rover</option>
                                            <option value="Jeep">Jeep</option>
                                        </optgroup>
                                        <!-- Motos -->
                                        <optgroup label="Motos">
                                            <option value="Bajaj">Bajaj</option>
                                            <option value="TVS">TVS</option>
                                            <option value="Honda Moto">Honda Moto</option>
                                            <option value="Yamaha">Yamaha</option>
                                            <option value="Suzuki Moto">Suzuki Moto</option>
                                            <option value="Haojue">Haojue</option>
                                            <option value="Boxer">Boxer</option>
                                            <option value="Apsonic">Apsonic</option>
                                            <option value="Lifan">Lifan</option>
                                            <option value="Senke">Senke</option>
                                            <option value="Royal Enfield">Royal Enfield</option>
                                            <option value="Sanili">Sanili</option>
                                            <option value="Dayun">Dayun</option>
                                            <option value="Jincheng">Jincheng</option>
                                            <option value="Kymco">Kymco</option>
                                        </optgroup>
                                        <!-- Camions -->
                                        <optgroup label="Camions">
                                            <option value="Mercedes-Benz Trucks">Mercedes-Benz Trucks</option>
                                            <option value="MAN">MAN</option>
                                            <option value="Volvo Trucks">Volvo Trucks</option>
                                            <option value="Scania">Scania</option>
                                            <option value="Iveco">Iveco</option>
                                            <option value="DAF">DAF</option>
                                            <option value="Renault Trucks">Renault Trucks</option>
                                            <option value="Isuzu Trucks">Isuzu Trucks</option>
                                            <option value="Hino">Hino</option>
                                            <option value="Mitsubishi Fuso">Mitsubishi Fuso</option>
                                            <option value="UD Trucks">UD Trucks</option>
                                            <option value="Tata Motors">Tata Motors</option>
                                            <option value="Ashok Leyland">Ashok Leyland</option>
                                            <option value="FAW">FAW</option>
                                            <option value="Sinotruk">Sinotruk</option>
                                            <option value="Shacman">Shacman</option>
                                            <option value="Dongfeng">Dongfeng</option>
                                            <option value="Foton Trucks">Foton Trucks</option>
                                            <option value="JAC Trucks">JAC Trucks</option>
                                            <option value="CAMC">CAMC</option>
                                        </optgroup>
                                        <!-- Engins de chantier -->
                                        <optgroup label="Engins de chantier">
                                            <option value="Caterpillar">Caterpillar</option>
                                            <option value="Komatsu">Komatsu</option>
                                            <option value="JCB">JCB</option>
                                            <option value="Hitachi">Hitachi</option>
                                            <option value="Liebherr">Liebherr</option>
                                            <option value="XCMG">XCMG</option>
                                            <option value="SANY">SANY</option>
                                            <option value="Zoomlion">Zoomlion</option>
                                            <option value="Liugong">Liugong</option>
                                            <option value="Shantui">Shantui</option>
                                            <option value="Case">Case</option>
                                            <option value="New Holland">New Holland</option>
                                            <option value="Bobcat">Bobcat</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div>

                            <!-- Type -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="type" class="form-label">Type</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-car"></i></span>
                                    <select id="type" name="type" class="form-select" required>
                                        <option value="">Choisir le Type...</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Date d'Acquisition -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="date_acquisition" class="form-label">Date d'Acquisition</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-calendar"></i></span>
                                    <input type="date" class="form-control" id="date_acquisition" name="date_acquisition" value="<?php echo e(old('date_acquisition')); ?>" required>
                                </div>
                            </div>

                            <!-- Date d'Affectation -->
                            <div class="col-md-4 full-width-select" style="width: 33.33% !important; flex: 0 0 33.33% !important;">
                                <label for="date_affectation" class="form-label">Date d'Affectation</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-calendar-check"></i></span>
                                    <input type="date" class="form-control" id="date_affectation" name="date_affectation" value="<?php echo e(old('date_affectation')); ?>">
                                </div>
                            </div>

                            <!-- Valeur d'acquisition -->
                            <div class="col-md-6 full-width-select" style="width: 47.5% !important; flex: 0 0 47.5% !important;">
                                <label for="valeur_acquisition" class="form-label">Valeur d'Acquisition</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-money"></i></span>
                                    <input type="number" class="form-control" id="valeur_acquisition" name="valeur_acquisition" value="<?php echo e(old('valeur_acquisition')); ?>" placeholder="Montant en FCFA"  required>
                                    <span class="input-group-text">FCFA</span>
                                </div>
                            </div>

                            <!-- Usage -->
                            <div class="col-md-6 full-width-select" style="width: 47.5% !important; flex: 0 0 47.5% !important;">
                                <label for="usage" class="form-label">Usage</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-info-circle"></i></span>
                                    <select name="usage" id="usage" class="form-select" size="6" style="overflow-y: auto;" required>
                                        <option value="">Choisir l'Usage...</option>
                                        <option value="Privé">Privé</option>
                                        <option value="Taxi">Taxi</option>
                                        <option value="Transport de personnel">Transport de personnel</option>
                                        <option value="Transport de marchandises">Transport de marchandises</option>
                                        <option value="Transport de matériaux">Transport de matériaux</option>
                                        <option value="Transport spécialisé">Transport spécialisé</option>
                                        <option value="Véhicule de fonction">Véhicule de fonction</option>
                                        <option value="Véhicule de service">Véhicule de service</option>
                                        <option value="Véhicule d'intervention">Véhicule d'intervention</option>
                                        <option value="Autre">Autre</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Caractéristiques techniques -->
                <div id="caracteristiques-techniques" class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-cog me-2"></i>Caractéristiques Techniques</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row g-3">
                            <!-- État -->
                            <div class="col-md-3 full-width-select" style="width: 25% !important; flex: 0 0 25% !important;">
                                <label for="etat" class="form-label">État</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-health"></i></span>
                                    <select name="etat" id="etat" class="form-select" required>
                                        <option value="">Choisir l'état...</option>
                                        <option value="Bon">Bon</option>
                                        <option value="Passable">Passable</option>
                                        <option value="En Panne">En Panne</option>
                                        <option value="Hors Service">Hors Service</option>
                                    </select>
                                </div>
                            </div>

                            <!-- PTC -->
                            <div class="col-md-3 full-width-select" style="width: 25% !important; flex: 0 0 25% !important;">
                                <label for="ptc" class="form-label">Poids Total en Charge</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-weight"></i></span>
                                    <input type="text" class="form-control" id="ptc" name="ptc" value="<?php echo e(old('ptc')); ?>" placeholder="Ex: 3500 kg" >
                                </div>
                            </div>

                            <!-- Puissance -->
                            <div class="col-md-3 full-width-select" style="width: 25% !important; flex: 0 0 25% !important;">
                                <label for="puissance" class="form-label">Puissance</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-power-off"></i></span>
                                    <input type="text" class="form-control" id="puissance" name="puissance" value="<?php echo e(old('puissance')); ?>" placeholder="Ex: 110 CV" >
                                </div>
                            </div>

                            <!-- Énergie -->
                            <div class="col-md-3">
                                <label for="energie" class="form-label">Énergie</label>
                                <div class="input-group">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-gas-pump"></i></span>
                                    <select name="energie" id="energie" class="form-select" size="6" style="overflow-y: auto;" required>
                                        <option value="">Choisir l'Énergie...</option>
                                        <option value="Essence">Essence</option>
                                        <option value="Gasoil">Gasoil</option>
                                        <option value="Électrique">Électrique</option>
                                        <option value="Hybride">Hybride</option>
                                        <option value="Hybride Rechargeable">Hybride Rechargeable</option>
                                        <option value="GPL">GPL</option>
                                        <option value="GNV">GNV</option>
                                        <option value="Biocarburant">Biocarburant</option>
                                        <option value="Hydrogène">Hydrogène</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Utilisation -->
                <div class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-user me-2"></i>Utilisation</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row g-3">
                            <!-- Utilisateur -->
                            <div class="col-md-6 full-width-select" style="width: 50% !important; flex: 0 0 50% !important;">
                                <label for="utilisateur" class="form-label">Utilisateur</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-user"></i></span>
                                    <select class="form-control select2-utilisateur" id="utilisateur" name="utilisateur" required>
                                        <option value="">Sélectionner un utilisateur</option>
                                        <?php $__currentLoopData = $employees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $employee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($employee->em_id); ?>"><?php echo e($employee->em_code); ?> <?php echo e($employee->first_name); ?> <?php echo e($employee->last_name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Service -->
                            <div class="col-md-6 full-width-select" style="width: 50% !important; flex: 0 0 50% !important;">
                                <label for="service_utilisation" class="form-label">Service</label>
                                <div class="input-group align-items-center">
                                    <span class="input-group-text d-flex align-items-center justify-content-center"><i class="bx bx-building"></i></span>
                                    <input type="text" class="form-control" id="service_utilisation" name="service_utilisation" value="<?php echo e(old('service_utilisation')); ?>" placeholder="Service ou bureau d'utilisation"  required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Photo du véhicule -->
                <div class="form-card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
                    <div class="form-card-header">
                        <h5 class="form-card-title"><i class="bx bx-image me-2"></i>Photo du Véhicule</h5>
                    </div>
                    <div class="form-card-body">
                        <div class="row">
                            <div class="col-md-7">
                                <div class="image-preview-container" id="dropzone" onclick="document.getElementById('image').click()">
                                    <input type="file" class="d-none" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                                    <div id="preview-placeholder" class="image-preview-placeholder">
                                        <i class="bx bx-image-add"></i>
                                        <p>Cliquez ou déposez une image ici</p>
                                        <small class="text-muted">Formats acceptés: JPG, PNG, GIF (Max: 2MB)</small>
                                    </div>
                                    <img id="showImage" class="image-preview d-none" src="<?php echo e(url('upload/no_image.jpg')); ?>" alt="Photo du véhicule">
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div id="imageDetails" class="image-details d-none">
                                    <h6 class="mb-3">Détails de l'image</h6>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Nom du fichier:</span>
                                        <span id="fileName" class="image-details-value"></span>
                                    </div>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Taille:</span>
                                        <span id="fileSize" class="image-details-value"></span>
                                    </div>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Type:</span>
                                        <span id="fileType" class="image-details-value"></span>
                                    </div>
                                    <div class="image-details-item">
                                        <span class="image-details-label">Dimensions:</span>
                                        <span id="dimensions" class="image-details-value"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
                    <button type="reset" class="btn btn-outline-secondary me-md-2 btn-with-icon">
                        <i class="bx bx-reset me-1"></i> Réinitialiser
                    </button>
                    <button type="submit" class="btn btn-primary btn-with-icon pulse-on-hover">
                        <i class="bx bx-save me-1"></i> Enregistrer le véhicule
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/scripts/choices.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
// Ajout d'effets visuels pour les cartes
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter des effets d'ombre au survol des cartes
    const formCards = document.querySelectorAll('.form-card');
    formCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('card-hover');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('card-hover');
        });
    });
    
    // Ajouter une validation personnalisée
    const form = document.getElementById('vehicleForm');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
            
            // Afficher un message d'erreur avec animation
            const invalidFields = form.querySelectorAll(':invalid');
            invalidFields.forEach(field => {
                field.parentElement.classList.add('shake-error');
                setTimeout(() => {
                    field.parentElement.classList.remove('shake-error');
                }, 1000);
            });
        }
        form.classList.add('was-validated');
    });
});

// Prévisualisation de l'image
function previewImage(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        var file = input.files[0];
        
        reader.onload = function(e) {
            // Afficher l'image
            $('#showImage').attr('src', e.target.result).removeClass('d-none');
            $('#preview-placeholder').addClass('d-none');
            
            // Afficher les détails du fichier
            $('#fileName').text(file.name);
            $('#fileSize').text((file.size / 1024).toFixed(2) + ' KB');
            $('#fileType').text(file.type);
            
            // Obtenir les dimensions de l'image
            var img = new Image();
            img.onload = function() {
                $('#dimensions').text(this.width + ' x ' + this.height + ' px');
            };
            img.src = e.target.result;
            
            // Afficher la section de détails
            $('#imageDetails').removeClass('d-none');
        }
        
        reader.readAsDataURL(file);
    }
}

// Initialisation de Choices.js pour les selects
document.addEventListener('DOMContentLoaded', function() {
    // Configuration de base pour tous les selects
    const configChoices = {
        removeItemButton: true,
        searchEnabled: true,
        searchPlaceholderValue: 'Rechercher...',
        noResultsText: 'Aucun résultat trouvé',
        noChoicesText: 'Aucun choix disponible',
        itemSelectText: 'Cliquer pour sélectionner',
        shouldSort: false,
        position: 'bottom',
        allowHTML: true
    };

    // Configuration spéciale pour le select Type
    const configChoicesType = {
        ...configChoices,
        placeholder: true,
        placeholderValue: 'Choisir le Type...',
        searchEnabled: false,
        removeItemButton: false
    };

    // Initialiser les selects principaux
    const departementSelect = new Choices('#departement_id', configChoices);
    const genreSelect = new Choices('#genre', configChoices);
    const marqueSelect = new Choices('#marque', configChoices);
    // Note: Le champ Type utilise un select natif pour une meilleure compatibilité
    const etatSelect = new Choices('#etat', configChoices);
    const energieSelect = new Choices('#energie', configChoices);
    const usageSelect = new Choices('#usage', configChoices);
    
    // Initialiser Select2 pour l'utilisateur avec des options optimisées pour la saisie
    if ($('#utilisateur').length) {
        $('#utilisateur').select2({
            placeholder: 'Sélectionner un utilisateur',
            allowClear: true,
            width: '100%',
            dropdownParent: $('#utilisateur').parent(),
            language: {
                noResults: function() {
                    return "Aucun résultat trouvé";
                },
                searching: function() {
                    return "Recherche en cours...";
                },
                inputTooShort: function(args) {
                    return "Veuillez saisir au moins " + args.minimum + " caractères";
                }
            },
            minimumInputLength: 1,
            templateResult: formatEmployee,
            templateSelection: formatEmployeeSelection,
            escapeMarkup: function(markup) {
                return markup;
            }
        });
    }
    
    // Fonction pour formater les résultats de recherche
    function formatEmployee(employee) {
        if (!employee.id) return employee.text;
        
        var text = employee.text;
        var parts = text.split(' ');
        
        if (parts.length >= 3) {
            var code = parts[0];
            var firstName = parts[1];
            var lastName = parts.slice(2).join(' ');
            
            return '<div class="select2-result-employee">' +
                   '<div class="select2-result-employee__code">' + code + '</div>' +
                   '<div class="select2-result-employee__name">' + firstName + ' ' + lastName + '</div>' +
                   '</div>';
        }
        
        return employee.text;
    }
    
    // Fonction pour formater la sélection
    function formatEmployeeSelection(employee) {
        if (!employee.id) return employee.text;
        return employee.text;
    }
    
    // Fonction pour mettre à jour les types selon le genre
    function updateTypeChoices(genre) {
        const selectElement = document.getElementById('type');

        // Définir les nouveaux choix selon le genre
        let newChoices = [{value: '', label: 'Choisir le Type...', disabled: true, selected: true}];

        if (genre === 'Voiture') {
            newChoices = newChoices.concat([
                {value: 'Berline', label: 'Berline'},
                {value: 'SUV', label: 'SUV'},
                {value: 'Pickup', label: 'Pickup'},
                {value: 'Break', label: 'Break'},
                {value: 'Citadine', label: 'Citadine'},
                {value: 'Coupé', label: 'Coupé'},
                {value: 'Cabriolet', label: 'Cabriolet'},
                {value: 'Monospace', label: 'Monospace'},
                {value: 'Crossover', label: 'Crossover'},
                {value: 'Compacte', label: 'Compacte'},
                {value: 'Familiale', label: 'Familiale'},
                {value: 'Limousine', label: 'Limousine'},
                {value: 'Sportive', label: 'Sportive'},
                {value: 'Utilitaire', label: 'Utilitaire'}
            ]);
        } else if (genre === 'Camion') {
            newChoices = newChoices.concat([
                {value: 'Benne', label: 'Benne'},
                {value: 'Plateau', label: 'Plateau'},
                {value: 'Citerne', label: 'Citerne'},
                {value: 'Frigorifique', label: 'Frigorifique'},
                {value: 'Fourgon', label: 'Fourgon'},
                {value: 'Porteur', label: 'Porteur'},
                {value: 'Semi-remorque', label: 'Semi-remorque'},
                {value: 'Tracteur routier', label: 'Tracteur routier'},
                {value: 'Camion-grue', label: 'Camion-grue'},
                {value: 'Bétonnière', label: 'Bétonnière'},
                {value: 'Dépanneuse', label: 'Dépanneuse'},
                {value: 'Pompier', label: 'Pompier'},
                {value: 'Ambulance', label: 'Ambulance'}
            ]);
        } else if (genre === 'Moto') {
            newChoices = newChoices.concat([
                {value: 'Sport', label: 'Sport'},
                {value: 'Routière', label: 'Routière'},
                {value: 'Trail', label: 'Trail'},
                {value: 'Custom', label: 'Custom'},
                {value: 'Scooter', label: 'Scooter'},
                {value: 'Enduro', label: 'Enduro'},
                {value: 'Cross', label: 'Cross'},
                {value: 'Touring', label: 'Touring'},
                {value: 'Naked', label: 'Naked'},
                {value: 'Supermotard', label: 'Supermotard'},
                {value: 'Café Racer', label: 'Café Racer'},
                {value: 'Chopper', label: 'Chopper'}
            ]);
        } else if (genre === 'Tricycle') {
            newChoices = newChoices.concat([
                {value: 'Standard', label: 'Standard'},
                {value: 'Cargo', label: 'Cargo'},
                {value: 'Passager', label: 'Passager'},
                {value: 'Livraison', label: 'Livraison'},
                {value: 'Électrique', label: 'Électrique'}
            ]);
        } else if (genre === 'Tête Porte Chars' || genre === 'Porte Chars') {
            newChoices = newChoices.concat([
                {value: 'Standard', label: 'Standard'},
                {value: 'Lourd', label: 'Lourd'},
                {value: 'Surbaissé', label: 'Surbaissé'},
                {value: 'Extensible', label: 'Extensible'},
                {value: 'Multi-niveaux', label: 'Multi-niveaux'}
            ]);
        }

        // Mettre à jour le select natif
        selectElement.innerHTML = '';
        newChoices.forEach(choice => {
            const option = document.createElement('option');
            option.value = choice.value;
            option.textContent = choice.label;
            if (choice.disabled) option.disabled = true;
            if (choice.selected) option.selected = true;
            selectElement.appendChild(option);
        });
    }

    // Filtrer les types en fonction du genre sélectionné
    document.getElementById('genre').addEventListener('change', function(e) {
        const genre = e.target.value;
        updateTypeChoices(genre);
    });

    // Initialiser le select Type au chargement
    updateTypeChoices('');
    
    // Support du drag and drop pour l'image
    const dropzone = document.getElementById('dropzone');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropzone.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropzone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropzone.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropzone.classList.add('border-primary');
    }
    
    function unhighlight() {
        dropzone.classList.remove('border-primary');
    }
    
    dropzone.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length) {
            document.getElementById('image').files = files;
            previewImage(document.getElementById('image'));
        }
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\GestionParkAutoFinal_ok\resources\views/vehicule/ajouter.blade.php ENDPATH**/ ?>
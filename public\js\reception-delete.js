// Script pour la confirmation de suppression avec SweetAlert2
document.addEventListener('DOMContentLoaded', function() {
    // Sélectionner tous les boutons de suppression
    const deleteButtons = document.querySelectorAll('.delete-reception-btn');
    
    // Ajouter un écouteur d'événement à chaque bouton
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            // Empêcher le comportement par défaut du lien
            event.preventDefault();
            
            // Récupérer l'URL de suppression
            const deleteUrl = this.getAttribute('href');
            
            // Afficher la boîte de dialogue SweetAlert2
            Swal.fire({
                title: 'Êtes-vous sûr?',
                text: "Cette action supprimera définitivement cette réception!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#e74a3b',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Oui, supprimer!',
                cancelButtonText: 'Annuler',
                reverseButtons: true,
                focusCancel: true
            }).then((result) => {
                // Si l'utilisateur confirme, rediriger vers l'URL de suppression
                if (result.isConfirmed) {
                    window.location.href = deleteUrl;
                }
            });
        });
    });
});

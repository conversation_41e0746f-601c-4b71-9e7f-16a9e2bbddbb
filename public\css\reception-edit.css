/* Styles pour la page d'édition des réceptions */
:root {
    --primary: #4e73df;
    --primary-light: #eef2ff;
    --primary-dark: #224abe;
    --secondary: #6c757d;
    --success: #1cc88a;
    --danger: #e74a3b;
    --warning: #f6c23e;
    --info: #36b9cc;
    --light: #f8f9fc;
    --dark: #5a5c69;
    --white: #fff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --transition: all 0.3s ease;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* En-tête et navigation */
.reception-breadcrumb {
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    color: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

.reception-breadcrumb .breadcrumb-title {
    font-size: 1.25rem;
    font-weight: 600;
}

.reception-breadcrumb .breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    margin-top: 0.5rem;
}

.reception-breadcrumb .breadcrumb-item a {
    color: var(--white);
    opacity: 0.8;
    transition: var(--transition);
}

.reception-breadcrumb .breadcrumb-item a:hover {
    opacity: 1;
    text-decoration: none;
}

.reception-breadcrumb .breadcrumb-item.active {
    color: var(--white);
    opacity: 1;
}

/* Carte principale */
.reception-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.reception-card .card-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
}

.reception-card .card-header h4 {
    margin: 0;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
}

.reception-card .card-body {
    padding: 1.5rem;
}

/* Sections du formulaire */
.form-section {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-left: 4px solid var(--primary);
}

.form-section-title {
    color: var(--primary);
    font-weight: 600;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
}

.form-section-title i {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

/* Champs de formulaire */
.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-control, 
.form-select,
.choices__inner {
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    color: var(--gray-800);
    background-color: var(--white);
    height: 46px;
    font-size: 0.95rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.form-control:focus, 
.form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    outline: none;
    transform: translateY(-2px);
}

/* Icônes dans les champs */
.input-icon {
    position: relative;
}

.input-icon input {
    padding-left: 2.5rem;
}

.input-icon span {
    position: absolute;
    left: 1rem;
    color: var(--primary);
    font-size: 1.1rem;
}

/* Boutons */
.btn-primary {
    background: linear-gradient(to right, var(--primary), var(--primary-dark));
    border: none;
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.btn-secondary {
    background-color: var(--gray-600);
    border: none;
    color: var(--white);
}

/* Champ de fichier */
.file-upload {
    position: relative;
    display: flex;
    align-items: center;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    margin-bottom: 1rem;
}

.file-upload:hover {
    border-color: var(--primary);
}

.file-upload input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
}

.file-upload-icon {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.file-upload-text {
    color: var(--gray-700);
}

.file-upload-info {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--gray-600);
}

/* Affichage du fichier actuel */
.current-file {
    display: flex;
    align-items: center;
    background-color: var(--primary-light);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.current-file-icon {
    font-size: 1.5rem;
    color: var(--primary);
    margin-right: 1rem;
}

.current-file-info {
    flex-grow: 1;
}

.current-file-name {
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.current-file-meta {
    font-size: 0.8rem;
    color: var(--gray-600);
}

.current-file-actions {
    display: flex;
    gap: 0.5rem;
}

.current-file-actions a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    color: var(--white);
    transition: var(--transition);
}

.current-file-actions .view-btn {
    background-color: var(--info);
}

.current-file-actions .delete-btn {
    background-color: var(--danger);
}

.current-file-actions a:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Checkbox personnalisée */
.custom-checkbox {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: relative;
    height: 20px;
    width: 20px;
    background-color: var(--white);
    border: 2px solid var(--gray-400);
    border-radius: 4px;
    margin-right: 0.75rem;
    transition: var(--transition);
}

.custom-checkbox:hover input ~ .checkmark {
    border-color: var(--primary);
}

.custom-checkbox input:checked ~ .checkmark {
    background-color: var(--primary);
    border-color: var(--primary);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.custom-checkbox .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.custom-checkbox-label {
    font-size: 0.95rem;
    color: var(--gray-700);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Responsive */
@media (max-width: 768px) {
    .reception-breadcrumb {
        padding: 1rem;
    }
    
    .form-section {
        padding: 1rem;
    }
}

/* Messages d'erreur de validation */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--danger);
}

.is-invalid {
    border-color: var(--danger) !important;
}

.is-invalid:focus {
    box-shadow: 0 0 0 0.25rem rgba(231, 74, 59, 0.25) !important;
}

/* Textarea */
textarea.form-control {
    height: auto;
    min-height: 100px;
    resize: vertical;
}

/* Tooltip personnalisé */
.tooltip-reception {
    position: relative;
    display: inline-block;
}

.tooltip-reception .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: var(--dark);
    color: var(--white);
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip-reception .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--dark) transparent transparent transparent;
}

.tooltip-reception:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

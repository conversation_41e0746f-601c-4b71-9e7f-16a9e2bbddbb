// Script personnalisé pour la page de réception
document.addEventListener('DOMContentLoaded', function() {
    // Gestion de l'upload de fichier
    const fileInput = document.getElementById('pv_reception');
    const fileLabel = document.getElementById('file-label');
    const fileInfo = document.getElementById('file-info');
    const checkbox = document.getElementById('pv_checked');
    
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                const fileName = this.files[0].name;
                fileInfo.textContent = fileName;
                fileInfo.style.display = 'block';
                checkbox.checked = true;
                
                // Animation pour montrer que le fichier a été sélectionné
                fileLabel.innerHTML = '<i class="bx bx-check-circle upload-icon text-success"></i><div>Fichier sélectionné</div>';
            } else {
                fileInfo.style.display = 'none';
                checkbox.checked = false;
                fileLabel.innerHTML = '<i class="bx bx-upload upload-icon"></i><div>Cliquez ou glissez un fichier ici</div>';
            }
        });
    }
    
    // Animation des champs du formulaire
    const formInputs = document.querySelectorAll('.reception-form .form-control, .reception-form .form-select');
    
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('input-focus');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('input-focus');
        });
    });
    
    // Validation en temps réel
    const dateInput = document.getElementById('date_enregistrement');
    const directionSelect = document.getElementById('departement_id');
    const submitBtn = document.querySelector('.btn-reception');
    
    function checkRequiredFields() {
        if (dateInput && directionSelect) {
            if (dateInput.value && directionSelect.value !== 'Choisir la Direction / Structure...') {
                submitBtn.disabled = false;
            } else {
                submitBtn.disabled = true;
            }
        }
    }
    
    if (dateInput && directionSelect) {
        dateInput.addEventListener('change', checkRequiredFields);
        directionSelect.addEventListener('change', checkRequiredFields);
        
        // Vérification initiale
        checkRequiredFields();
    }
    
    // Initialisation des tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    if (typeof bootstrap !== 'undefined') {
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});

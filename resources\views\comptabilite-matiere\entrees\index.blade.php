<!doctype html>
<html lang="fr">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!--favicon-->
    <link rel="icon" href="{{ asset('backend/assets/images/favicon-32x32.png') }}" type="image/png"/>
    <!--plugins-->
    <link href="{{ asset('backend/assets/plugins/vectormap/jquery-jvectormap-2.0.2.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/simplebar/css/simplebar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css') }}" rel="stylesheet" />
    <link href="{{ asset('backend/assets/plugins/metismenu/css/metisMenu.min.css') }}" rel="stylesheet"/>
    <link href="{{ asset('backend/assets/plugins/datatable/css/dataTables.bootstrap5.min.css') }}" rel="stylesheet"/>
    <!-- loader-->
    <link href="{{ asset('backend/assets/css/pace.min.css') }}" rel="stylesheet"/>
    <script src="{{ asset('backend/assets/js/pace.min.js') }}"></script>
    <!-- Bootstrap CSS -->
    <link href="{{ asset('backend/assets/css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/bootstrap-extended.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/assets/css/icons.css') }}" rel="stylesheet">
    <!-- Theme Style CSS -->
    <link rel="stylesheet" href="{{ asset('backend/assets/css/dark-theme.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/semi-dark.css') }}"/>
    <link rel="stylesheet" href="{{ asset('backend/assets/css/header-colors.css') }}"/>
    <!-- Toster CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" >
    <!-- Animate CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <!-- Custom Sidebar CSS -->
    <link rel="stylesheet" href="{{ asset('css/custom-sidebar.css') }}">
    <title>Gestion des Entrées</title>
    <style>
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            border: none;
            margin-bottom: 24px;
        }
        .card-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 15px 20px;
            border-bottom: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .card-body {
            padding: 25px;
            border-radius: 0 0 15px 15px;
        }
        .btn-action {
            padding: 8px 16px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
        }
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }
        .btn-primary {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border: none;
        }
        .btn-success {
            background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
            border: none;
        }
        .btn-info {
            background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
            border: none;
        }
        .btn-warning {
            background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
            border: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);
            border: none;
        }
        .badge {
            padding: 6px 12px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 12px;
        }
        .badge-success {
            background-color: rgba(28, 200, 138, 0.15);
            color: #1cc88a;
            border: 1px solid rgba(28, 200, 138, 0.3);
        }
        .badge-warning {
            background-color: rgba(246, 194, 62, 0.15);
            color: #f6c23e;
            border: 1px solid rgba(246, 194, 62, 0.3);
        }
        .badge-danger {
            background-color: rgba(231, 74, 59, 0.15);
            color: #e74a3b;
            border: 1px solid rgba(231, 74, 59, 0.3);
        }
        .badge-info {
            background-color: rgba(54, 185, 204, 0.15);
            color: #36b9cc;
            border: 1px solid rgba(54, 185, 204, 0.3);
        }
        .badge-primary {
            background-color: rgba(78, 115, 223, 0.15);
            color: #4e73df;
            border: 1px solid rgba(78, 115, 223, 0.3);
        }
        .badge-secondary {
            background-color: rgba(133, 135, 150, 0.15);
            color: #858796;
            border: 1px solid rgba(133, 135, 150, 0.3);
        }
        .table {
            border-collapse: separate;
            border-spacing: 0 5px;
        }
        .table thead th {
            border-bottom: none;
            background-color: #f8f9fc;
            color: #6e707e;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
            padding: 15px;
        }
        .table tbody tr {
            box-shadow: 0 2px 10px rgba(0,0,0,0.03);
            border-radius: 5px;
            transition: all 0.2s;
        }
        .table tbody tr:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .table tbody td {
            background-color: white;
            padding: 15px;
            vertical-align: middle;
            border-top: none;
        }
        .table tbody tr td:first-child {
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
        }
        .table tbody tr td:last-child {
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        .action-buttons .btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-size: 14px;
        }
        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }
        .empty-state i {
            font-size: 60px;
            color: #d1d3e2;
            margin-bottom: 20px;
        }
        .empty-state h4 {
            color: #5a5c69;
            margin-bottom: 10px;
        }
        .empty-state p {
            color: #858796;
            margin-bottom: 20px;
        }
        .dataTables_wrapper .dataTables_length, 
        .dataTables_wrapper .dataTables_filter {
            margin-bottom: 20px;
        }
        .dataTables_wrapper .dataTables_length select, 
        .dataTables_wrapper .dataTables_filter input {
            border-radius: 50px;
            padding: 8px 15px;
            border: 1px solid #e3e6f0;
        }
        .dataTables_wrapper .dataTables_filter input {
            width: 300px;
        }
        .dataTables_wrapper .dataTables_info, 
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 20px;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button {
            border-radius: 50px;
            padding: 5px 15px;
            margin: 0 3px;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border: none;
            color: white !important;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #eaecf4;
            border: 1px solid #eaecf4;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border: none;
            color: white !important;
        }
        .filter-row {
            background-color: #f8f9fc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
        }
        .filter-row .form-group {
            margin-bottom: 0;
            flex: 1;
            min-width: 200px;
        }
        .filter-row .form-control {
            border-radius: 50px;
            padding: 8px 15px;
            border: 1px solid #e3e6f0;
        }
        .filter-row .btn {
            padding: 8px 20px;
            border-radius: 50px;
        }
    </style>
</head>

<body>
    <!--wrapper-->
    <div class="wrapper">
        <!--sidebar wrapper -->
        @include('admin.body.sidebar')
        <!--end sidebar wrapper -->
        <!--start header -->
        @extends('admin.admin_dashboard')
        <!--end header -->
        <!--start page wrapper -->
        <div class="page-wrapper">
            <div class="page-content">
                <!--breadcrumb-->
                <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
                    <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
                    <div class="ps-3">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0 p-0">
                                <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a></li>
                                <li class="breadcrumb-item">Enregistrements</li>
                                <li class="breadcrumb-item active" aria-current="page">Entrées</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ route('entrees.create') }}" class="btn btn-primary btn-action">
                            <i class="bx bx-plus"></i> Nouvelle Entrée
                        </a>
                    </div>
                </div>
                <!--end breadcrumb-->
                
                <div class="card animate__animated animate__fadeIn">
                    <div class="card-header">
                        <div>
                            <i class="bx bx-log-in-circle me-1"></i>
                            <span class="fw-bold">Liste des Entrées</span>
                        </div>
                        <div>
                            <button type="button" class="btn btn-light btn-sm" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                                <i class="bx bx-filter-alt"></i> Filtres
                            </button>
                            <button type="button" class="btn btn-light btn-sm" id="exportBtn">
                                <i class="bx bx-export"></i> Exporter
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="collapse mb-3" id="filterCollapse">
                            <div class="filter-row">
                                <div class="form-group">
                                    <label for="articleFilter" class="form-label">Article</label>
                                    <select class="form-select" id="articleFilter">
                                        <option value="">Tous les articles</option>
                                        <!-- Options seront ajoutées via JavaScript -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="dateDebut" class="form-label">Date début</label>
                                    <input type="date" class="form-control" id="dateDebut">
                                </div>
                                <div class="form-group">
                                    <label for="dateFin" class="form-label">Date fin</label>
                                    <input type="date" class="form-control" id="dateFin">
                                </div>
                                <div class="form-group d-flex align-items-end">
                                    <button type="button" class="btn btn-primary" id="applyFilter">
                                        <i class="bx bx-search"></i> Appliquer
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" id="resetFilter">
                                        <i class="bx bx-reset"></i> Réinitialiser
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        @if(count($entrees) > 0)
                        <div class="table-responsive">
                            <table id="entreeTable" class="table align-middle mb-0">
                                <thead>
                                    <tr>
                                        <th>DATE</th>
                                        <th>RÉFÉRENCE</th>
                                        <th>ARTICLE</th>
                                        <th>QUANTITÉ</th>
                                        <th>PRIX UNITAIRE</th>
                                        <th>MONTANT TOTAL</th>
                                        <th>ENREGISTRÉ PAR</th>
                                        <th>ACTIONS</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($entrees as $entree)
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($entree->date_entree)->format('d/m/Y') }}</td>
                                        <td>
                                            <span class="badge badge-primary">{{ $entree->reference }}</span>
                                        </td>
                                        <td>{{ $entree->article->designation }}</td>
                                        <td>
                                            <span class="fw-bold">{{ number_format($entree->quantite, 0, ',', ' ') }}</span>
                                            <small class="text-muted">{{ $entree->article->unite }}</small>
                                        </td>
                                        <td>{{ number_format($entree->prix_unitaire, 0, ',', ' ') }} F</td>
                                        <td>
                                            <span class="fw-bold">{{ number_format($entree->quantite * $entree->prix_unitaire, 0, ',', ' ') }} F</span>
                                        </td>
                                        <td>{{ $entree->user->name }}</td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="{{ route('entrees.show', $entree->id) }}" class="btn btn-info" data-bs-toggle="tooltip" title="Voir">
                                                    <i class="bx bx-show"></i>
                                                </a>
                                                <a href="{{ route('entrees.edit', $entree->id) }}" class="btn btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                                    <i class="bx bx-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-danger delete-btn" data-id="{{ $entree->id }}" data-bs-toggle="tooltip" title="Supprimer">
                                                    <i class="bx bx-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <div class="empty-state">
                            <i class="bx bx-package"></i>
                            <h4>Aucune entrée enregistrée</h4>
                            <p>Vous n'avez pas encore enregistré d'entrées de stock.</p>
                            <a href="{{ route('entrees.create') }}" class="btn btn-primary btn-action">
                                <i class="bx bx-plus"></i> Enregistrer une entrée
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <!--end page wrapper -->
        <!--start footer -->
        @include('admin.body.footer')
        <!--end footer -->
    </div>
    <!--end wrapper-->

    <!-- Modal de confirmation de suppression -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmation de suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer cette entrée ? Cette action est irréversible.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <form id="deleteForm" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Supprimer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="{{ asset('backend/assets/js/bootstrap.bundle.min.js') }}"></script>
    <!--plugins-->
    <script src="{{ asset('backend/assets/js/jquery.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/simplebar/js/simplebar.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/metismenu/js/metisMenu.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/perfect-scrollbar/js/perfect-scrollbar.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('backend/assets/plugins/datatable/js/dataTables.bootstrap5.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!--app JS-->
    <script src="{{ asset('backend/assets/js/app.js') }}"></script>
    
    <script>
        $(document).ready(function() {
            // Initialiser DataTables
            var table = $('#entreeTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/fr-FR.json',
                },
                order: [[0, 'desc']], // Trier par date décroissante
                responsive: true,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "Tous"]]
            });
            
            // Remplir le filtre d'articles
            var articles = [];
            @foreach($entrees as $entree)
                if (!articles.includes('{{ $entree->article->designation }}')) {
                    articles.push('{{ $entree->article->designation }}');
                    $('#articleFilter').append('<option value="{{ $entree->article->designation }}">{{ $entree->article->designation }}</option>');
                }
            @endforeach
            
            // Appliquer les filtres
            $('#applyFilter').click(function() {
                var article = $('#articleFilter').val();
                var dateDebut = $('#dateDebut').val();
                var dateFin = $('#dateFin').val();
                
                table.columns(2).search(article); // Colonne Article
                
                // Filtrer par date (personnalisé)
                $.fn.dataTable.ext.search.push(
                    function(settings, data, dataIndex) {
                        if (dateDebut === '' && dateFin === '') return true;
                        
                        var dateEntree = data[0].split('/').reverse().join('-'); // Format date pour comparaison
                        
                        if (dateDebut !== '' && dateFin === '') {
                            return dateEntree >= dateDebut;
                        }
                        if (dateDebut === '' && dateFin !== '') {
                            return dateEntree <= dateFin;
                        }
                        return dateEntree >= dateDebut && dateEntree <= dateFin;
                    }
                );
                
                table.draw();
                
                // Nettoyer le filtre personnalisé
                $.fn.dataTable.ext.search.pop();
            });
            
            // Réinitialiser les filtres
            $('#resetFilter').click(function() {
                $('#articleFilter').val('');
                $('#dateDebut').val('');
                $('#dateFin').val('');
                table.search('').columns().search('').draw();
            });
            
            // Gérer la suppression
            $('.delete-btn').click(function() {
                var id = $(this).data('id');
                $('#deleteForm').attr('action', '{{ route("entrees.destroy", "") }}/' + id);
                $('#deleteModal').modal('show');
            });
            
            // Exporter en CSV
            $('#exportBtn').click(function() {
                var csv = 'Date,Référence,Article,Quantité,Prix Unitaire,Montant Total,Enregistré Par\n';
                
                // Récupérer les données visibles (filtrées)
                var data = table.rows({search:'applied'}).data();
                
                // Ajouter chaque ligne au CSV
                for (var i = 0; i < data.length; i++) {
                    var row = data[i];
                    // Nettoyer les données (enlever les balises HTML)
                    var cleanRow = row.map(function(cell) {
                        var div = document.createElement('div');
                        div.innerHTML = cell;
                        return div.textContent || div.innerText || '';
                    });
                    
                    // Exclure la colonne Actions
                    cleanRow.pop();
                    
                    csv += cleanRow.join(',') + '\n';
                }
                
                // Créer un lien de téléchargement
                var link = document.createElement('a');
                link.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csv);
                link.target = '_blank';
                link.download = 'entrees_' + new Date().toISOString().slice(0,10) + '.csv';
                link.click();
            });
            
            // Initialiser les tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
            
            // Afficher les messages toast
            @if(session('success'))
                toastr.success("{{ session('success') }}");
            @endif
            
            @if(session('error'))
                toastr.error("{{ session('error') }}");
            @endif
        });
    </script>
</body>

</html>

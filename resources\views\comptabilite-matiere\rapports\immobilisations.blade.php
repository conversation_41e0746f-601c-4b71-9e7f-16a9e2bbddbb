@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Rapport des Immobilisations</h1>
        <div>
            <a href="{{ route('rapports.immobilisations-form') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">
                <i class="fas fa-sync fa-sm text-white-50"></i> Modifier les filtres
            </a>
            <a href="{{ url()->current() }}?{{ http_build_query(request()->except('export_pdf')) }}&export_pdf=1" class="d-none d-sm-inline-block btn btn-sm btn-danger shadow-sm">
                <i class="fas fa-file-pdf fa-sm text-white-50"></i> Exporter en PDF
            </a>
        </div>
    </div>

    <!-- Informations sur les filtres appliqués -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">Filtres appliqués</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Service :</strong> 
                        @if(request()->has('service_id') && request()->service_id)
                            {{ $immobilisations->first() ? $immobilisations->first()->service_affecte->nom : 'Service spécifique' }}
                        @else
                            Tous les services
                        @endif
                    </p>
                </div>
                <div class="col-md-4">
                    <p><strong>État physique :</strong> 
                        @if(request()->has('etat_physique') && request()->etat_physique)
                            @switch(request()->etat_physique)
                                @case('bon')
                                    Bon
                                    @break
                                @case('moyen')
                                    Moyen
                                    @break
                                @case('mauvais')
                                    Mauvais
                                    @break
                                @case('hors_service')
                                    Hors service
                                    @break
                            @endswitch
                        @else
                            Tous les états
                        @endif
                    </p>
                </div>
                <div class="col-md-4">
                    <p><strong>Statut :</strong> 
                        @if(request()->has('statut') && request()->statut)
                            @switch(request()->statut)
                                @case('en_service')
                                    En service
                                    @break
                                @case('reforme')
                                    Réformé
                                    @break
                                @case('perdu')
                                    Perdu
                                    @break
                                @case('autre')
                                    Autre
                                    @break
                            @endswitch
                        @else
                            Tous les statuts
                        @endif
                    </p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <p><strong>Nombre d'immobilisations :</strong> {{ $immobilisations->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des immobilisations -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">Liste des Immobilisations</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>N° Inventaire</th>
                            <th>Article</th>
                            <th>Date acquisition</th>
                            <th>Service affecté</th>
                            <th>État physique</th>
                            <th>Statut</th>
                            <th>Valeur acquisition</th>
                            <th>Observations</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($immobilisations as $immobilisation)
                        <tr>
                            <td>{{ $immobilisation->numero_inventaire }}</td>
                            <td>{{ $immobilisation->article->designation }}</td>
                            <td>{{ \Carbon\Carbon::parse($immobilisation->date_acquisition)->format('d/m/Y') }}</td>
                            <td>{{ $immobilisation->service_affecte->nom }}</td>
                            <td>
                                @switch($immobilisation->etat_physique)
                                    @case('bon')
                                        <span class="badge badge-success">Bon</span>
                                        @break
                                    @case('moyen')
                                        <span class="badge badge-warning">Moyen</span>
                                        @break
                                    @case('mauvais')
                                        <span class="badge badge-danger">Mauvais</span>
                                        @break
                                    @case('hors_service')
                                        <span class="badge badge-dark">Hors service</span>
                                        @break
                                    @default
                                        <span class="badge badge-secondary">{{ $immobilisation->etat_physique }}</span>
                                @endswitch
                            </td>
                            <td>
                                @switch($immobilisation->statut)
                                    @case('en_service')
                                        <span class="badge badge-success">En service</span>
                                        @break
                                    @case('reforme')
                                        <span class="badge badge-danger">Réformé</span>
                                        @break
                                    @case('perdu')
                                        <span class="badge badge-dark">Perdu</span>
                                        @break
                                    @default
                                        <span class="badge badge-secondary">{{ $immobilisation->statut }}</span>
                                @endswitch
                            </td>
                            <td class="text-right">{{ number_format($immobilisation->valeur_acquisition, 2, ',', ' ') }}</td>
                            <td>{{ $immobilisation->observations }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row">
        <!-- Répartition par état physique -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Répartition par état physique</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>État physique</th>
                                    <th>Nombre</th>
                                    <th>Pourcentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $totalImmo = $immobilisations->count();
                                    $etatStats = [
                                        'bon' => ['label' => 'Bon', 'count' => 0],
                                        'moyen' => ['label' => 'Moyen', 'count' => 0],
                                        'mauvais' => ['label' => 'Mauvais', 'count' => 0],
                                        'hors_service' => ['label' => 'Hors service', 'count' => 0],
                                    ];
                                    
                                    foreach ($immobilisations as $immobilisation) {
                                        if (isset($etatStats[$immobilisation->etat_physique])) {
                                            $etatStats[$immobilisation->etat_physique]['count']++;
                                        }
                                    }
                                @endphp
                                
                                @foreach($etatStats as $key => $stat)
                                <tr>
                                    <td>{{ $stat['label'] }}</td>
                                    <td>{{ $stat['count'] }}</td>
                                    <td>
                                        @if($totalImmo > 0)
                                            {{ number_format(($stat['count'] / $totalImmo) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Répartition par statut -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Répartition par statut</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Statut</th>
                                    <th>Nombre</th>
                                    <th>Pourcentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $statutStats = [
                                        'en_service' => ['label' => 'En service', 'count' => 0],
                                        'reforme' => ['label' => 'Réformé', 'count' => 0],
                                        'perdu' => ['label' => 'Perdu', 'count' => 0],
                                        'autre' => ['label' => 'Autre', 'count' => 0],
                                    ];
                                    
                                    foreach ($immobilisations as $immobilisation) {
                                        if (isset($statutStats[$immobilisation->statut])) {
                                            $statutStats[$immobilisation->statut]['count']++;
                                        } else {
                                            $statutStats['autre']['count']++;
                                        }
                                    }
                                @endphp
                                
                                @foreach($statutStats as $key => $stat)
                                <tr>
                                    <td>{{ $stat['label'] }}</td>
                                    <td>{{ $stat['count'] }}</td>
                                    <td>
                                        @if($totalImmo > 0)
                                            {{ number_format(($stat['count'] / $totalImmo) * 100, 1) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Valeur totale -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">Valeur totale des immobilisations</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Valeur totale d'acquisition</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ number_format($immobilisations->sum('valeur_acquisition'), 2, ',', ' ') }} FCFA
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Valeur des immobilisations en service</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ number_format($immobilisations->where('statut', 'en_service')->sum('valeur_acquisition'), 2, ',', ' ') }} FCFA
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "order": [[ 0, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
    });
</script>
@endsection

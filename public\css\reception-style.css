/* Styles personnalisés pour la page de réception */
.reception-card {
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border: none;
    background: linear-gradient(to right, #ffffff, #f8f9fa);
}

.reception-card .card-header {
    background: linear-gradient(to right, #4e73df, #224abe);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 20px;
    border: none;
}

.reception-form label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.reception-form .form-control,
.reception-form .form-select {
    border-radius: 10px;
    padding: 12px;
    border: 1px solid #ced4da;
    transition: all 0.3s;
}

.reception-form .form-control:focus,
.reception-form .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.reception-form .input-icon i {
    color: #4e73df;
}

.btn-reception {
    background: linear-gradient(to right, #4e73df, #224abe);
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s;
}

.btn-reception:hover {
    background: linear-gradient(to right, #224abe, #1a3a94);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(34, 74, 190, 0.3);
}

.reception-breadcrumb {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.file-upload-wrapper {
    position: relative;
    border: 2px dashed #4e73df;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
    background-color: #f8f9fa;
    margin-bottom: 20px;
}

.file-upload-wrapper:hover {
    background-color: #e9ecef;
    cursor: pointer;
}

.file-upload-wrapper input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload-wrapper .upload-icon {
    font-size: 2rem;
    color: #4e73df;
    margin-bottom: 10px;
}

.file-upload-wrapper .file-info {
    display: none;
    margin-top: 10px;
    padding: 8px;
    background-color: #e9ecef;
    border-radius: 5px;
}

.form-section {
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
}

.form-section-title {
    font-size: 1.2rem;
    color: #4e73df;
    margin-bottom: 15px;
    font-weight: 600;
}

/* Animation pour les notifications */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification {
    animation: fadeInDown 0.5s ease-out;
}

/* Style pour la checkbox */
.custom-checkbox {
    display: flex;
    align-items: center;
    margin: 15px 0;
}

.custom-checkbox input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .reception-card .card-header {
        padding: 15px;
    }
    
    .reception-form .form-control,
    .reception-form .form-select {
        padding: 10px;
    }
}

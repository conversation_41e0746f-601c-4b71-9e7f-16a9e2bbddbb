@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Fiche de Stock</h1>
        <div>
            <a href="{{ route('rapports.fiche-stock-form') }}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">
                <i class="fas fa-sync fa-sm text-white-50"></i> Modifier les paramètres
            </a>
            <a href="{{ route('rapports.fiche-stock', ['article_id' => $article->id, 'date_debut' => $dateDebut->format('Y-m-d'), 'date_fin' => $dateFin->format('Y-m-d'), 'export_pdf' => 1]) }}" class="d-none d-sm-inline-block btn btn-sm btn-danger shadow-sm">
                <i class="fas fa-file-pdf fa-sm text-white-50"></i> Exporter en PDF
            </a>
        </div>
    </div>

    <!-- Informations de l'article -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Informations de l'article</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Code article :</th>
                            <td>{{ $article->code_article }}</td>
                        </tr>
                        <tr>
                            <th>Désignation :</th>
                            <td>{{ $article->designation }}</td>
                        </tr>
                        <tr>
                            <th>Unité :</th>
                            <td>{{ $article->unite->libelle }}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">Période :</th>
                            <td>Du {{ $dateDebut->format('d/m/Y') }} au {{ $dateFin->format('d/m/Y') }}</td>
                        </tr>
                        <tr>
                            <th>Stock initial :</th>
                            <td>{{ $stockInitial }} {{ $article->unite->libelle }}</td>
                        </tr>
                        <tr>
                            <th>Stock final :</th>
                            <td>{{ $stockFinal }} {{ $article->unite->libelle }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des mouvements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Mouvements de stock</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Référence</th>
                            <th>Entrée</th>
                            <th>Sortie</th>
                            <th>P.U.</th>
                            <th>Source/Destination</th>
                            <th>Observations</th>
                            <th>Utilisateur</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($mouvements as $mouvement)
                        <tr>
                            <td>{{ \Carbon\Carbon::parse($mouvement['date'])->format('d/m/Y') }}</td>
                            <td>
                                @if($mouvement['type'] == 'Entrée')
                                    <span class="badge badge-success">{{ $mouvement['type'] }}</span>
                                @elseif($mouvement['type'] == 'Sortie')
                                    <span class="badge badge-danger">{{ $mouvement['type'] }}</span>
                                @else
                                    <span class="badge badge-warning">{{ $mouvement['type'] }}</span>
                                @endif
                            </td>
                            <td>{{ $mouvement['reference'] }}</td>
                            <td class="text-right">
                                @if($mouvement['quantite_entree'] > 0)
                                    {{ $mouvement['quantite_entree'] }}
                                @endif
                            </td>
                            <td class="text-right">
                                @if($mouvement['quantite_sortie'] > 0)
                                    {{ $mouvement['quantite_sortie'] }}
                                @endif
                            </td>
                            <td class="text-right">
                                @if($mouvement['prix_unitaire'])
                                    {{ number_format($mouvement['prix_unitaire'], 2, ',', ' ') }}
                                @endif
                            </td>
                            <td>{{ $mouvement['source_destination'] }}</td>
                            <td>{{ $mouvement['observations'] }}</td>
                            <td>{{ $mouvement['user'] }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Résumé -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Résumé</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Stock Initial</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stockInitial }} {{ $article->unite->libelle }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Total Entrées</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $mouvements->sum('quantite_entree') }} {{ $article->unite->libelle }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                        Total Sorties</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        {{ $mouvements->sum('quantite_sortie') }} {{ $article->unite->libelle }}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Stock Final</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stockFinal }} {{ $article->unite->libelle }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "order": [[ 0, "asc" ]],
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
            }
        });
    });
</script>
@endsection

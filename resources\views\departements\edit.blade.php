@extends('admin.admin_dashboard')
@section('admin')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

<style>
    /* Couleurs et thème */
    :root {
        --primary: #4361ee;
        --primary-light: #4895ef;
        --secondary: #3f37c9;
        --accent: #4cc9f0;
        --success: #4ade80;
        --danger: #f87171;
        --warning: #fbbf24;
        --info: #60a5fa;
        --dark: #374151;
        --light: #f9fafb;
    }
    
    /* Styles de base */
    .card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        background: linear-gradient(145deg, #ffffff 0%, #f9fafb 100%);
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(67, 97, 238, 0.1);
    }
    
    .card-body {
        padding: 2.5rem;
    }
    
    /* Fil d'ariane amélioré */
    .page-breadcrumb {
        background: linear-gradient(to right, #f9fafb, #f3f4f6);
        padding: 1.2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        border-left: 4px solid var(--primary);
    }
    
    .breadcrumb-item a {
        color: var(--primary);
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .breadcrumb-item a:hover {
        color: var(--secondary);
        text-decoration: none;
        transform: translateX(3px);
    }
    
    .breadcrumb-item.active {
        color: var(--dark);
        font-weight: 600;
    }
    
    /* Champs de formulaire améliorés */
    .form-control {
        border-radius: 10px;
        padding: 0.8rem 1.2rem;
        border: 1px solid #e5e7eb;
        background-color: #f9fafb;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-control:focus {
        border-color: var(--primary-light);
        box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.15);
        background-color: #fff;
    }
    
    .form-control::placeholder {
        color: #9ca3af;
        font-style: italic;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 0.75rem;
        font-size: 1rem;
        display: flex;
        align-items: center;
    }
    
    .form-label i {
        font-size: 1.2rem;
        margin-right: 0.5rem;
        color: var(--primary);
    }
    
    /* Boutons stylisés */
    .btn {
        border-radius: 10px;
        padding: 0.8rem 1.5rem;
        font-weight: 600;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    
    .btn i {
        font-size: 1.2rem;
    }
    
    .btn-primary {
        background: linear-gradient(145deg, var(--primary) 0%, var(--secondary) 100%);
        border: none;
        color: white;
    }
    
    .btn-primary:hover {
        background: linear-gradient(145deg, var(--secondary) 0%, var(--primary) 100%);
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
    }
    
    .btn-primary:active {
        transform: translateY(1px);
    }
    
    .btn-secondary {
        background-color: #f3f4f6;
        border: 1px solid #e5e7eb;
        color: var(--dark);
        margin-right: 1rem;
    }
    
    .btn-secondary:hover {
        background-color: #e5e7eb;
        color: var(--dark);
    }
    
    /* En-tête de section */
    .section-title {
        color: var(--primary);
        font-weight: 700;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        font-size: 1.5rem;
        position: relative;
        padding-bottom: 1rem;
    }
    
    .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 80px;
        height: 4px;
        background: linear-gradient(to right, var(--primary), var(--primary-light));
        border-radius: 2px;
    }
    
    .section-title i {
        margin-right: 0.8rem;
        font-size: 1.8rem;
        background: linear-gradient(135deg, var(--primary), var(--accent));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    /* Badge d'édition */
    .edit-badge {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        font-size: 0.95rem;
        margin-left: 1rem;
        box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
        }
    }
    
    /* Validations et erreurs */
    .invalid-feedback {
        font-size: 0.85rem;
        font-weight: 500;
        color: var(--danger);
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
    }
    
    .invalid-feedback:before {
        content: '\f071';
        font-family: 'boxicons';
        margin-right: 0.5rem;
    }
    
    .is-invalid {
        border-color: var(--danger) !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23f87171' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23f87171' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
    
    /* Card Header avec design */
    .card-header {
        background: linear-gradient(to right, var(--primary-light), var(--primary));
        color: white;
        padding: 1.5rem 2.5rem;
        border: none;
    }
    
    /* Aide flottante */
    .help-tooltip {
        display: inline-block;
        margin-left: 0.5rem;
        color: var(--info);
        cursor: help;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .help-tooltip:hover {
        color: var(--primary);
        transform: scale(1.2);
    }
    
    /* Animation et effets */
    .form-group {
        transition: all 0.3s ease;
    }
    
    .form-group:focus-within label {
        color: var(--primary);
    }
    
    /* Amélioration du responsive */
    @media (max-width: 768px) {
        .card-body {
            padding: 1.5rem;
        }
        
        .btn {
            width: 100%;
            margin-bottom: 0.5rem;
        }
        
        .btn-secondary {
            margin-right: 0;
        }
    }
</style>

<div class="page-content animate__animated animate__fadeIn">
    <!-- Breadcrumb amélioré -->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center animate__animated animate__fadeInDown">
        <div class="breadcrumb-title pe-3">
            <i class='bx bx-building-house me-1'></i>
            Directions / Structures
        </div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('admin.dashboard') }}" class="d-flex align-items-center">
                            <i class="bx bx-home-alt me-1"></i> Accueil
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('departements.index') }}" class="d-flex align-items-center">
                            <i class='bx bx-buildings me-1'></i> Directions
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class='bx bx-edit me-1'></i> Modifier
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container mt-4">
        <div class="main-body">
            <div class="row">
                <!-- Carte principale avec animation -->
                <div class="col-lg-10 col-md-12 mx-auto animate__animated animate__fadeInUp animate__delay-1s">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0 text-white d-flex align-items-center">
                                <i class='bx bx-edit-alt me-2'></i>
                                Modification d'une Direction / Structure
                            </h4>
                        </div>
                        <div class="card-body">
                            <h5 class="section-title">
                                <i class='bx bx-edit'></i>
                                Informations de la Direction
                                <span class="edit-badge">
                                    <i class='bx bx-building'></i>
                                    {{ $departements->nom_departement }}
                                </span>
                            </h5>

                            <!-- Progress Tracker -->
                            <div class="progress mb-4" style="height: 8px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="width: 70%" 
                                     aria-valuenow="70" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>

                            <form id="myForm" action="{{ route('departements.update', $departements->id) }}" method="POST" class="needs-validation">
                                @csrf
                                @method('PUT')
                                
                                <div class="row mb-4 g-3">
                                    <div class="col-md-10 mx-auto">
                                        <div class="form-floating mb-4 animate__animated animate__fadeInRight animate__delay-2s">
                                            <input type="text" 
                                                   name="name" 
                                                   id="name" 
                                                   class="form-control @error('name') is-invalid @enderror"  
                                                   value="{{ $departements->nom_departement }}" 
                                                   placeholder="Entrez le nom de la Direction ou Structure"
                                                   required/>
                                            <label for="name" class="text-muted">
                                                <i class='bx bx-buildings me-1'></i>
                                                Nom de la Direction / Structure *
                                            </label>
                                            <div class="form-text text-muted">Modifiez le nom de la Direction ou Structure selon vos besoins.</div>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Métadonnées -->
                                        <div class="my-4 pt-4 border-top">
                                            <div class="row text-muted small">
                                                <div class="col-md-6">
                                                    <p><i class='bx bx-calendar-plus me-1'></i> Créé le: {{ \Illuminate\Support\Carbon::parse($departements->created_at)->format('d-m-Y à H:i') }}</p>
                                                </div>
                                                <div class="col-md-6 text-md-end">
                                                    <p><i class='bx bx-calendar-edit me-1'></i> Dernière modification: {{ \Illuminate\Support\Carbon::parse($departements->updated_at)->format('d-m-Y à H:i') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-10 mx-auto d-flex mt-3">
                                        <a href="{{ route('departements.index') }}" class="btn btn-secondary me-2">
                                            <i class='bx bx-arrow-back'></i> Retour à la liste
                                        </a>
                                        <button type="submit" class="btn btn-primary ms-auto animate__animated animate__pulse animate__infinite animate__slower">
                                            <i class='bx bx-save me-1'></i> Enregistrer les modifications
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function (){
        // Animation d'entrée au chargement
        setTimeout(function() {
            $('.animate__infinite').removeClass('animate__infinite');
        }, 3000);
        
        // Validation du formulaire améliorée
        $('#myForm').validate({
            rules: {
                name: {
                    required : true,
                    minlength: 3
                }
            },
            messages :{
                name: {
                    required : 'Veuillez saisir le nom de la direction ou structure',
                    minlength: 'Le nom doit contenir au moins 3 caractères'
                }
            },
            errorElement : 'span', 
            errorPlacement: function (error,element) {
                error.addClass('invalid-feedback');
                element.closest('.form-floating').append(error);
            },
            highlight : function(element, errorClass, validClass){
                $(element).addClass('is-invalid');
                $(element).removeClass('is-valid');
            },
            unhighlight : function(element, errorClass, validClass){
                $(element).removeClass('is-invalid');
                $(element).addClass('is-valid');
            },
        });
        
        // Focus automatique sur le premier champ
        $('#name').focus();
        
        // Effets de survol des boutons
        $('.btn').hover(
            function() { $(this).addClass('shadow-sm'); },
            function() { $(this).removeClass('shadow-sm'); }
        );
    });
</script>
@endsection
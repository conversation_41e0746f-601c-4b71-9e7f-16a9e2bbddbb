@extends('admin.admin_dashboard')
@section('admin')

<style>
    .card {
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.05);
        border: none;
        margin-bottom: 24px;
    }
    .card-header {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 15px 20px;
        border-bottom: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .card-body {
        padding: 25px;
        border-radius: 0 0 15px 15px;
    }
    .btn-action {
        padding: 8px 16px;
        border-radius: 50px;
        font-weight: 500;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }
    .btn-warning {
        background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
        border: none;
    }
    .table {
        border-collapse: separate;
        border-spacing: 0 5px;
    }
    .table thead th {
        border-bottom: none;
        background-color: #f8f9fc;
        color: #6e707e;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 1px;
        padding: 15px;
    }
    .table tbody tr {
        box-shadow: 0 2px 10px rgba(0,0,0,0.03);
        border-radius: 5px;
        transition: all 0.2s;
    }
    .table tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .table tbody td {
        background-color: white;
        padding: 15px;
        vertical-align: middle;
        border-top: none;
    }
    .empty-state {
        text-align: center;
        padding: 40px 20px;
    }
    .empty-state i {
        font-size: 60px;
        color: #d1d3e2;
        margin-bottom: 20px;
    }
    .empty-state h4 {
        color: #5a5c69;
        margin-bottom: 10px;
    }
    .empty-state p {
        color: #858796;
        margin-bottom: 20px;
    }
</style>

<div class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3">Comptabilité Matière</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="javascript:;"><i class="bx bx-home-alt"></i></a></li>
                    <li class="breadcrumb-item">Enregistrements</li>
                    <li class="breadcrumb-item active" aria-current="page">Ajustements</li>
                </ol>
            </nav>
        </div>
        <div class="ms-auto">
            <a href="{{ route('ajustements.create') }}" class="btn btn-warning btn-action">
                <i class="bx bx-plus"></i> Nouvel Ajustement
            </a>
        </div>
    </div>
    <!--end breadcrumb-->
    
    <div class="card animate__animated animate__fadeIn">
        <div class="card-header">
            <div>
                <i class="bx bx-revision me-1"></i>
                <span class="fw-bold">Liste des Ajustements</span>
            </div>
            <div>
                <button type="button" class="btn btn-light btn-sm" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bx bx-filter-alt"></i> Filtres
                </button>
                <button type="button" class="btn btn-light btn-sm" id="exportBtn">
                    <i class="bx bx-export"></i> Exporter
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="empty-state">
                <i class="bx bx-revision"></i>
                <h4>Aucun ajustement enregistré</h4>
                <p>Vous n'avez pas encore enregistré d'ajustements de stock.</p>
                <a href="{{ route('ajustements.create') }}" class="btn btn-warning btn-action">
                    <i class="bx bx-plus"></i> Enregistrer un ajustement
                </a>
            </div>
        </div>
    </div>
</div>

@endsection

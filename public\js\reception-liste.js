// Script pour la page de liste des réceptions

document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Gestion des boutons de suppression avec SweetAlert2
    const deleteButtons = document.querySelectorAll('.delete-reception-btn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const deleteUrl = this.getAttribute('href');
            
            Swal.fire({
                title: 'Êtes-vous sûr?',
                text: "Cette action supprimera définitivement cette réception!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#e74a3b',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Oui, supprimer!',
                cancelButtonText: 'Annuler',
                reverseButtons: true,
                focusCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = deleteUrl;
                }
            });
        });
    });
    
    // Initialisation de DataTables avec configuration avancée
    const receptionTable = $('#example2').DataTable({
        responsive: true,
        language: {
            processing: "Traitement en cours...",
            search: "Rechercher&nbsp;:",
            lengthMenu: "Afficher _MENU_ éléments",
            info: "Affichage de l'élément _START_ à _END_ sur _TOTAL_ éléments",
            infoEmpty: "Affichage de l'élément 0 à 0 sur 0 élément",
            infoFiltered: "(filtré de _MAX_ éléments au total)",
            infoPostFix: "",
            loadingRecords: "Chargement en cours...",
            zeroRecords: "Aucun élément à afficher",
            emptyTable: "Aucune donnée disponible dans le tableau",
            paginate: {
                first: "Premier",
                previous: "Précédent",
                next: "Suivant",
                last: "Dernier"
            },
            aria: {
                sortAscending: ": activer pour trier la colonne par ordre croissant",
                sortDescending: ": activer pour trier la colonne par ordre décroissant"
            }
        },
        dom: '<"top"<"left-col"l><"center-col"B><"right-col"f>>rtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="bx bx-file-export"></i> Excel',
                className: 'btn btn-sm btn-reception export-btn',
                exportOptions: {
                    columns: ':not(:last-child)'
                }
            },
            {
                extend: 'pdf',
                text: '<i class="bx bx-file-pdf"></i> PDF',
                className: 'btn btn-sm btn-reception export-btn',
                exportOptions: {
                    columns: ':not(:last-child)'
                }
            },
            {
                extend: 'print',
                text: '<i class="bx bx-printer"></i> Imprimer',
                className: 'btn btn-sm btn-reception export-btn',
                exportOptions: {
                    columns: ':not(:last-child)'
                }
            }
        ],
        initComplete: function() {
            // Ajouter des filtres par colonne
            this.api().columns([1, 5, 6]).each(function(index) {
                const column = this;
                const select = $('<select class="form-select filter-select"><option value="">Tous</option></select>')
                    .appendTo($('.filters-row th').eq(column.index()))
                    .on('change', function() {
                        const val = $.fn.dataTable.util.escapeRegex($(this).val());
                        column.search(val ? '^' + val + '$' : '', true, false).draw();
                    });

                column.data().unique().sort().each(function(d, j) {
                    if (d) {
                        select.append('<option value="' + d + '">' + d + '</option>');
                    }
                });
            });
            
            // Animation d'entrée
            $('.reception-card').addClass('animate-fade-in');
            
            // Initialiser les tooltips
            initTooltips();
        }
    });
    
    // Fonction pour initialiser les tooltips
    function initTooltips() {
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new bootstrap.Tooltip(tooltip);
        });
    }
    
    // Gestion des filtres rapides
    $('#quick-filter-status').on('change', function() {
        const value = $(this).val();
        if (value === 'all') {
            receptionTable.column(7).search('').draw();
        } else if (value === 'with-pv') {
            receptionTable.column(7).search('checked').draw();
        } else if (value === 'without-pv') {
            receptionTable.column(7).search('^((?!checked).)*$', true, false).draw();
        }
    });
    
    // Recherche globale améliorée
    $('#global-search').on('keyup', function() {
        receptionTable.search($(this).val()).draw();
    });
    
    // Gestion des dates
    $('#date-filter').on('change', function() {
        const value = $(this).val();
        let dateRange;
        
        switch(value) {
            case 'today':
                dateRange = getTodayRange();
                break;
            case 'week':
                dateRange = getWeekRange();
                break;
            case 'month':
                dateRange = getMonthRange();
                break;
            case 'year':
                dateRange = getYearRange();
                break;
            default:
                dateRange = null;
        }
        
        if (dateRange) {
            $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                const date = new Date(data[0].split('-').reverse().join('-'));
                return date >= dateRange.start && date <= dateRange.end;
            });
        } else {
            $.fn.dataTable.ext.search.pop();
        }
        
        receptionTable.draw();
    });
    
    // Fonctions utilitaires pour les plages de dates
    function getTodayRange() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return {
            start: today,
            end: new Date(today.getTime() + 86400000)
        };
    }
    
    function getWeekRange() {
        const today = new Date();
        const firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
        firstDay.setHours(0, 0, 0, 0);
        const lastDay = new Date(firstDay);
        lastDay.setDate(lastDay.getDate() + 6);
        lastDay.setHours(23, 59, 59, 999);
        
        return {
            start: firstDay,
            end: lastDay
        };
    }
    
    function getMonthRange() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        lastDay.setHours(23, 59, 59, 999);
        
        return {
            start: firstDay,
            end: lastDay
        };
    }
    
    function getYearRange() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), 0, 1);
        const lastDay = new Date(today.getFullYear(), 11, 31);
        lastDay.setHours(23, 59, 59, 999);
        
        return {
            start: firstDay,
            end: lastDay
        };
    }
    
    // Gestion des actions en lot
    $('#select-all').on('change', function() {
        $('.row-checkbox').prop('checked', $(this).prop('checked'));
        updateBulkActionButtons();
    });
    
    $(document).on('change', '.row-checkbox', function() {
        updateBulkActionButtons();
    });
    
    function updateBulkActionButtons() {
        const checkedCount = $('.row-checkbox:checked').length;
        if (checkedCount > 0) {
            $('.bulk-actions').removeClass('d-none');
            $('.selected-count').text(checkedCount);
        } else {
            $('.bulk-actions').addClass('d-none');
        }
    }
    
    // Action de suppression en lot
    $('#bulk-delete').on('click', function(e) {
        e.preventDefault();
        
        const selectedIds = [];
        $('.row-checkbox:checked').each(function() {
            selectedIds.push($(this).data('id'));
        });
        
        if (selectedIds.length === 0) return;
        
        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: `Vous êtes sur le point de supprimer ${selectedIds.length} réception(s). Cette action est irréversible!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74a3b',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                // Ici, vous pouvez ajouter le code pour envoyer une requête AJAX pour supprimer les éléments
                console.log('Suppression des IDs:', selectedIds);
                
                // Simuler une suppression réussie
                Swal.fire(
                    'Supprimé!',
                    'Les réceptions sélectionnées ont été supprimées.',
                    'success'
                ).then(() => {
                    // Recharger la page ou mettre à jour le tableau
                    window.location.reload();
                });
            }
        });
    });
    
    // Gestion de la suppression individuelle
    $(document).on('click', '.delete-btn', function(e) {
        e.preventDefault();
        
        const url = $(this).attr('href');
        
        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: "Vous ne pourrez pas revenir en arrière!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74a3b',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = url;
            }
        });
    });
    
    // Prévisualisation du PV - Supprimé car remplacé par des liens directs
    // Les liens PV ouvrent maintenant directement dans un nouvel onglet
    
    // Afficher les détails de la réception
    $(document).on('click', '.view-details-btn', function(e) {
        e.preventDefault();
        
        const id = $(this).data('id');
        const row = $(this).closest('tr');
        const date = row.find('td:eq(0)').text().trim();
        const structure = row.find('td:eq(1)').text().trim();
        const reference = row.find('td:eq(2)').text().trim();
        const marche = row.find('td:eq(3)').text().trim();
        const objet = row.find('td:eq(4)').text().trim();
        const periode = row.find('td:eq(5)').text().trim();
        const executant = row.find('td:eq(6)').text().trim();
        const hasPv = row.find('.pv-status').hasClass('checked');
        
        $('#detailsModal .modal-title').text(`Détails de la réception - ${reference}`);
        
        $('#detailsModal .modal-body').html(`
            <div class="row">
                <div class="col-md-6">
                    <div class="detail-card">
                        <div class="detail-label">Date d'enregistrement</div>
                        <div class="detail-value">${date}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-card">
                        <div class="detail-label">Direction / Structure</div>
                        <div class="detail-value">${structure}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-card">
                        <div class="detail-label">Références du courier</div>
                        <div class="detail-value">${reference}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-card">
                        <div class="detail-label">Références du marché</div>
                        <div class="detail-value">${marche}</div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="detail-card">
                        <div class="detail-label">Objet</div>
                        <div class="detail-value">${objet}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-card">
                        <div class="detail-label">Période d'exécution</div>
                        <div class="detail-value">${periode}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-card">
                        <div class="detail-label">Exécutant</div>
                        <div class="detail-value">${executant}</div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="detail-card">
                        <div class="detail-label">Statut PV</div>
                        <div class="detail-value">
                            ${hasPv ? 
                                '<span class="badge badge-success"><i class="bx bx-check"></i> PV disponible</span>' : 
                                '<span class="badge badge-warning"><i class="bx bx-x"></i> Pas de PV</span>'
                            }
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        $('#detailsModal').modal('show');
    });
    
    // Gestion du mode sombre/clair
    $('#theme-toggle').on('click', function() {
        $('body').toggleClass('dark-theme');
        const isDarkMode = $('body').hasClass('dark-theme');
        localStorage.setItem('darkMode', isDarkMode);
        
        $(this).find('i').toggleClass('bx-sun bx-moon');
    });
    
    // Appliquer le thème sauvegardé
    if (localStorage.getItem('darkMode') === 'true') {
        $('body').addClass('dark-theme');
        $('#theme-toggle').find('i').removeClass('bx-moon').addClass('bx-sun');
    }
    
    // Animation au défilement
    const animateOnScroll = function() {
        const elements = document.querySelectorAll('.scroll-animation');
        
        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (elementPosition < windowHeight - 50) {
                element.classList.add('animate-fade-in');
            }
        });
    };
    
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Exécuter une fois au chargement
});

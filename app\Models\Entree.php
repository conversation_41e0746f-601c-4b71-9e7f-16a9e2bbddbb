<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Entree extends Model
{
    use HasFactory;
    use \Illuminate\Database\Eloquent\SoftDeletes;
    
    protected $fillable = [
        'article_id',
        'quantite',
        'prix_unitaire',
        'date_entree',
        'reference_doc',
        'source',
        'observations',
        'user_id'
    ];
    
    protected $casts = [
        'date_entree' => 'date',
    ];
    
    public function article()
    {
        return $this->belongsTo(Article::class);
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function montantTotal()
    {
        return $this->quantite * $this->prix_unitaire;
    }
}
